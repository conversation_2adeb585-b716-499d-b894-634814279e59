//
//  FNFreshMrFreshSwitchLocationParameterModel.m
//  FNFresh
//
//  Created by wa<PERSON><PERSON> on 2019/9/25.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshSwitchLocationParameterModel.h"
#import "NSString+Base64Encrypt.h"

@implementation FNFreshMrFreshSwitchLocationParameterModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"action":@"body.action",
        @"longitude":@"body.longitude",
        @"latitude":@"body.latitude",
        @"addrMap":@"body.addrMap",
        @"adcode":@"body.adcode",
        @"lastLongitude":@"body.lastLongitude",
        @"lastLatitude":@"body.lastLatitude",
        @"storeLongitude":@"body.storeLongitude",
        @"storeLatitude":@"body.storeLatitude",
        @"lastStoreId":@"body.lastStoreId"
    };
}

- (void)setLongitude:(NSString *)longitude {
    _longitude = [longitude base64Encrypt];
}

- (void)setLatitude:(NSString *)latitude {
    _latitude = [latitude base64Encrypt];
}

- (void)setLastLatitude:(NSString *)lastLatitude {
    _lastLatitude = [lastLatitude base64Encrypt];
}

- (void)setLastLongitude:(NSString *)lastLongitude {
    _lastLongitude = [lastLongitude base64Encrypt];
}

- (void)setAddrMap:(NSString *)addrMap {
    _addrMap = [addrMap base64Encrypt];
}

@end
