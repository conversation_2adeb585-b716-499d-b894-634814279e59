//
//  FNFreshStoreServiceBannerItemModel.h
//  FNFresh
//
//  Created by xn on 2022/1/12.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface FNFreshStoreServiceBannerItemModel : NSObject

@property (copy, nonatomic) NSString *img;
@property (copy, nonatomic) NSString *bgImg; //老式商店街背景图
@property (copy, nonatomic) NSString *url;
@property (copy, nonatomic) NSString *type; // 1：普通Banner，2：预售Banner 3：游戏banner
@property (copy, nonatomic) NSString *title; //利益点
@property (strong,nonatomic) NSNumber *imgHeight;//横幅banner图片高度
@property (strong,nonatomic) NSNumber *imgWidth;//横幅banner图片宽度
@property (nonatomic,strong)NSString *subTitle;
@property (nonatomic,strong)NSNumber *tag;

@end

