//
//  FNFreshStoreServiceResponseModel.h
//  FNFresh
//
//  Created by xn on 2022/1/10.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshBaseResponseModel.h"
#import "FNFreshStoreServiceBannerItemModel.h"
#import "FNFreshStoreServiceModel.h"
#import "FNFreshShopInfoModel.h"

@class FNFreshStoreServiceItemModel;

@interface FNFreshStoreServiceResponseModel : FNFreshBaseResponseModel


@property (copy, nonatomic) NSString *headBannerUrl; //背景url
@property (copy, nonatomic) NSString *storeName; //门店名称
@property (copy, nonatomic) NSString *storeAddress; //门店地址
@property (nonatomic,strong)NSString *ruleUrl; //规则链接;
@property (nonatomic,strong)NSString *saveMoneyTip;//省钱文案

@property (strong, nonatomic) FNFreshShopInfoModel *shopInfo; //门店信息
@property (copy, nonatomic) NSArray<FNFreshStoreServiceItemModel *> *storeService;//门店服务
@property (copy, nonatomic) NSArray<FNFreshStoreServiceBannerItemModel *> *storeBanner;//轮播banner

@property (copy, nonatomic) NSString *tipMsg;//优惠券卡券数量
@property (copy, nonatomic) NSString *integral;//积分
@property (nonatomic,strong)NSString *growthValue;//成长值
@property (nonatomic,strong)NSString *shoppingCardBalance;//购物卡余额
@property (nonatomic,strong)NSString *voucherCount;//优惠劵数量
@property (nonatomic,strong)NSString *bottomIcon;//底部icon信息，详细看1.12

@property (copy, nonatomic) NSArray<FNFreshStoreServiceModel *> *content;//可配置项目

//门店打卡状态 0：不显示打卡按钮， 1：用户当日已在该门店打卡（成长值），2：当日在该店未打卡，未在门店200m内（成长值） ，3：当日在该店未打卡，在门店200m内（成长值）
//4：用户当日已在该门店打卡，（积分）5：当日在该店未打卡，未在门店200m内 （积分）6：当日在该店未打卡，在门店200m内（积分）
@property (assign, nonatomic) NSInteger clockInState;

@end


//门店服务
@interface FNFreshStoreServiceItemModel : NSObject <YYModel>

@property (copy, nonatomic) NSString *isNeedLogin;
@property (copy, nonatomic) NSString *type;
@property (copy, nonatomic) NSString *linkUrl;
@property (copy, nonatomic) NSString *title;

@end
