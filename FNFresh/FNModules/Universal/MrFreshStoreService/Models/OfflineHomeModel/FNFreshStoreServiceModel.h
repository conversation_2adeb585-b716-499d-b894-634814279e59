//
//  FNFreshStoreServiceModel.h
//  FNFresh
//
//  Created by xn on 2022/1/10.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshStoreServiceBannerItemModel.h"
#import "FNFreshStoreServiceGoodsItemModel.h"
#import "FNFreshMrFreshPopupWindowResponseModel.h"
#import "FNCreditChannelResponseModel.h"

@class FNFreshStoreServiceHotSellingItemModel,
FNFreshStoreServiceCouponCenterModel,
FNFreshStoreServiceTodayLimitModel,
FNFreshStoreServiceCouponItemModel,
FNFreshStoreServiceGoodsModuleModel,
FNFreshStoreServiceCouponCenterModuleModel,
FNFreshStoreServiceCouponCenterModulePicModel,
FNFreshStoreServiceConponModel;

//模块类型（ 今日出炉 3 开业礼包 4 今日上新 5 今日省钱 6 今日大牌 7 活动报名+促销海报 8 商店街 9 排行榜 10 横幅BANNER 11）
typedef enum : NSUInteger {
    //新增
    FNFreshMrFreshStoreTypeTopView = 1,        //头部门店信息
//    FNFreshMrFreshStoreTypeTodayPastry = 3,    //今日出炉 已废弃
//    FNFreshMrFreshStoreTypeGiftBanner = 4,     // 开业礼包banner  ☑️ 已废弃
//    FNFreshMrFreshStoreTypeTodayNew = 5,       //今日上新 已废弃
    FNFreshMrFreshStoreTypeSaveMoney = 6,      //今日省钱
//    FNFreshMrFreshStoreTypeBigBrand = 7,       //今日大牌 已废弃
    FNFreshMrFreshStoreTypeApplyAndPoster = 8, //活动报名+促销海报
    FNFreshMrFreshStoreTypeShoppingStreet = 9, // 商店街 ☑️
    FNFreshMrFreshStoreTypeHotSelling = 10,     //排行榜
    FNFreshMrFreshStoreTypeSingleBanner = 11,   //单个横幅banner
    FNFreshMrFreshStoreTypeShoppingStreetNew = 13, //新版商店街
    FNFreshMrFreshStoreTypeShoppingStreetUnite = 14, //商店街联合模块 可能存在 99 ， 99+9， 99+13
    FNFreshMrFreshStoreTypeKingKonog = 15,           // 金刚区
    FNFreshMrFreshStoreTypeImagesCollection = 16,    //图片集合
//    FNFreshMrFreshStoreTypePointsMall = 17,          //积分商城
    FNFreshMrFreshStoreTypeCarousel = 18,            //轮播
    FNFreshMrFreshStoreTypeCarouselAndAds = 20,      //轮播+广告图
    FNFreshMrFreshStoreTypeGoodsModule = 21,         //商品陈列
    FNFreshMrFreshStoreTypeCouponCenter = 22,         //领券中心
    FNFreshMrFreshStoreTypeShoppingStreetIntegral = 99, //商店街积分模块
    FNFreshMrFreshStoreTypeCellHeaderStyle = 1000 // 这个表示每一个样式 cell
    
    
} FNFreshStoreHomeSectionType;

@interface FNFreshStoreServiceModel : NSObject <YYModel>

//今日出炉 3 开业礼包 4 今日上新 5 今日省钱 6 今日大牌 7
//活动报名+促销海报 8 商店街 9 排行榜 10 横幅BANNER 11 新版商店街 13 商店街联合模块 14 （API152版本开始9和13不再返回，只会有14）
@property (assign, nonatomic) FNFreshStoreHomeSectionType type;
@property (copy, nonatomic) NSString *moduleName;
@property (copy, nonatomic) NSString *bannerUrl; //背景url
@property (copy, nonatomic) NSString *color; //边框色值

//开业礼包模块 / 横幅banner模块
@property (strong, nonatomic) FNFreshStoreServiceBannerItemModel *pic;//开业礼包

//商店街/领券中心
@property (copy, nonatomic) NSString *hotLinkUrl;
//商店街 / 活动报名+促销海报模块 / 今日大牌模块 /今日省钱模块底部 / 新商店街 / 商店街子模块
@property (copy, nonatomic) NSArray<FNFreshStoreServiceBannerItemModel *> *picList;

@property (copy, nonatomic) NSArray<FNFreshStoreServiceGoodsItemModel*> *goodsList;

//排行榜模块
@property (copy, nonatomic) NSArray<FNFreshStoreServiceHotSellingItemModel *> *pondList;

//今日省钱模块
@property (strong, nonatomic) FNFreshStoreServiceCouponCenterModel *couponCenter;//领券中心
@property (strong, nonatomic) FNFreshStoreServiceTodayLimitModel *limited;//当日限定

// 177新增 新版商品街联合模块 -- 商店街样式类型（1：老式:2：新式）
// 198 新增 图片集合表现方式 --（1：一排横滑，2 ：1+1 3：1+2 4：2+2 ）
// 领券中心 -- 1：分版展示，2：不分版展示
@property (copy, nonatomic) NSString *styleType;

// 积分劵子模块
@property (copy, nonatomic) NSArray<FNFreshStoreServiceBannerItemModel *> *picList2;
@property (copy, nonatomic) NSString *title;
// 商店街子模块使用副标题
@property (copy, nonatomic) NSString *subTitle;
// 积分劵子模块使用副标题
@property (copy, nonatomic) NSString *subTitle2;

//针对 图片集合模块/商品陈列
@property (nonatomic,strong)NSString *moduleTitle;//模块标题
@property (nonatomic,strong)NSString *moduleSubTitle;//模块副标题

@property (nonatomic,strong)FNCreditOperationsModel *operationsModel;

//商品陈列
@property (copy, nonatomic) NSArray<FNFreshStoreServiceGoodsModuleModel *> *tabList;

//领券中心模块
@property (copy, nonatomic) NSArray<FNFreshStoreServiceCouponCenterModuleModel *> *moduleList;

@end

//榜单 item
@interface FNFreshStoreServiceHotSellingItemModel : NSObject <YYModel>

@property (copy, nonatomic) NSString *title;
@property (copy, nonatomic) NSString *pondId;
@property (copy, nonatomic) NSArray<FNFreshStoreServiceGoodsItemModel *> *goodsList;
          
@end

//领券中心
@interface FNFreshStoreServiceCouponCenterModel : NSObject <YYModel>

@property (copy, nonatomic) NSString *moduleName;     //领券中心
@property (copy, nonatomic) NSString *moduleSubTitle; //模块副标题
@property (copy, nonatomic) NSString *hotLinkUrl;     //热区跳转链接

@property (copy, nonatomic) NSArray<FNMrFreshPopupWindowNewGuidanceItemModel *> *couponList;

@end

//当日限定
@interface FNFreshStoreServiceTodayLimitModel : NSObject <YYModel>

@property (copy, nonatomic) NSString *title;
@property (assign, nonatomic) long deadLine; //倒计时时间ms
@property (copy, nonatomic) NSString *toast;
@property (copy, nonatomic) NSArray<FNFreshStoreServiceGoodsItemModel *> *goodsList;
//for local 单位s
@property (assign, nonatomic) long countDownTime;

@end

//商品陈列
@interface FNFreshStoreServiceGoodsModuleModel : NSObject <YYModel>

@property (nonatomic,strong) NSString *moduleTitle;   //标题
@property (nonatomic,strong) NSString *moduleSubTitle;//副标题
@property (nonatomic,strong) NSString *hotLinkUrl;   //跳转链接
@property(nonatomic,strong) NSArray<FNFreshProductListMerchandiseModel *> *goodsList;

//for local
@property(nonatomic,assign) BOOL isSelected;
@property(nonatomic,assign) CGPoint contentOffset;
@property(nonatomic,assign) BOOL isShowMoreView;

@end

@interface FNFreshStoreServiceCouponCenterModuleModel : NSObject <YYModel>

@property (nonatomic,strong) NSString *moduleTitle;    // 模块主标题
@property (nonatomic,strong) NSString *moduleSubTitle; // 模块副标题
@property (nonatomic,strong) NSString *styleType;      //1：图片展示，2：券展示
@property (nonatomic,strong) FNFreshStoreServiceCouponCenterModulePicModel *pic;
@property (copy, nonatomic) NSArray<FNFreshStoreServiceConponModel *> *couponList;
                
@end

@interface FNFreshStoreServiceCouponCenterModulePicModel : NSObject <YYModel>

@property (nonatomic,strong) NSString *img;
@end

@interface FNFreshStoreServiceConponModel : NSObject <YYModel>

@property (copy, nonatomic) NSString *couponId; //券活动id
@property (copy, nonatomic) NSString *couponUseTag; //券适用标签（例如：互通券、门店券、APP专享券、运费券（新人礼包运费券））
@property (assign, nonatomic) NSInteger couponUseTagType; //券适用标签文案（例如：1 app专享券；2门店券；3互通券；0：不展示
//券类型（1:满减券 2:礼品券 3:单品立减券 4：单品定价券 5：满折券 6：赠品券7：抵用券 8：运费券 9：自提券）
@property (assign, nonatomic) NSInteger couponType;
@property (copy, nonatomic) NSString *couponName;  // 券名称
@property (copy, nonatomic) NSString *couponValue; // 券金额、折扣
@property (copy, nonatomic) NSString *couponThreshold; // 券适用门槛
@property (nonatomic, copy) NSArray<FNTag *> *leftTagList;//渠道标，小时达/半日达单渠道标，自提专享标
@property (assign, nonatomic) NSInteger inOnline; // 是否线上券，1：线上券，3：互通券


@end


                   
                   
                   
                   


