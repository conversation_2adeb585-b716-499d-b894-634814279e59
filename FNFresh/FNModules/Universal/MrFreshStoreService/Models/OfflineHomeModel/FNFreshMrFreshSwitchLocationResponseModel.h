//
//  FNFreshMrFreshSwitchLocationResponseModel.h
//  FNFresh
//
//  Created by wang<PERSON> on 2019/9/25.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

#import "FNFreshBaseResponseModel.h"
#import "FNFreshShopInfoModel.h"

NS_ASSUME_NONNULL_BEGIN

@class FNFreshMrFreshHomeInfoModel,FNFreshMrFreshErrorInfoModel,FNFreshMrFreshLocationModel;

@interface FNFreshMrFreshSwitchLocationResponseModel : FNFreshBaseResponseModel

@property (assign, nonatomic) NSInteger showType;
@property (assign, nonatomic) NSInteger isLocationStore;
@property (strong, nonatomic) FNFreshMrFreshHomeInfoModel *homeInfo;
@property (strong, nonatomic) FNFreshShopInfoModel *shopInfo;
@property (strong, nonatomic) FNFreshMrFreshErrorInfoModel* errorInfo;
@property (strong, nonatomic) FNFreshMrFreshLocationModel* location;

@end

#pragma mark - 二级
@interface FNFreshMrFreshHomeInfoModel : NSObject <YYModel>

@property (assign, nonatomic) NSInteger isShowStoreService;
@property (copy, nonatomic) NSString *locationWarn;

@end

@interface FNFreshMrFreshErrorInfoModel : NSObject <YYModel>

@property (assign, nonatomic) NSInteger urlType; // 0:新店容错页，1：老店容错页，2：游客容错页
@property (copy, nonatomic) NSString *errorUrl;  // 容错页链接
@property (copy, nonatomic) NSString *errorTip;  // 容错页提示语
@property (copy, nonatomic) NSString *topBackColor; // 新店容错页背景色
@property (copy, nonatomic) NSString *errorStore; // 门店Id

@end

@interface FNFreshMrFreshLocationModel : NSObject <YYModel>

@property (copy, nonatomic) NSString *longitude;
@property (copy, nonatomic) NSString *latitude;
@property (copy, nonatomic) NSString *addrMap;
@property (copy, nonatomic) NSString *adcode;

@end

NS_ASSUME_NONNULL_END
