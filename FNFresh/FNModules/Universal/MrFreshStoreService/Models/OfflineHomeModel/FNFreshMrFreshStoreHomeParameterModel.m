//
//  FNFreshMrFreshStoreHomeParameterModel.m
//  FNFresh
//
//  Created by wang<PERSON> on 2019/9/25.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshStoreHomeParameterModel.h"

@implementation FNFreshMrFreshStoreHomeParameterModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"storeCode":@"body.storeCode",
        @"latitude":@"body.latitude",
        @"longitude":@"body.longitude",
        @"curLatitude":@"body.curLatitude",
        @"curLongitude":@"body.curLongitude",
    };
}

@end

@implementation FNFreshMrFreshStoreHomeSignParameterModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"storeCode":@"body.storeCode",
        @"type":@"body.type",
    };
}

@end


@implementation FNFreshMrFreshStoreHomeSignGetIntegralParameterModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"storeCode":@"body.storeCode",
        @"type":@"body.type",
        @"latitude":@"body.latitude",
        @"longitude":@"body.longitude",
        @"taskId":@"body.taskId",
        @"pageId":@"body.pageId",
    };
}

@end

