//
//  FNFreshStoreServiceModel.m
//  FNFresh
//
//  Created by xn on 2022/1/10.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceModel.h"

@implementation FNFreshStoreServiceModel

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"pic" : [FNFreshStoreServiceBannerItemModel class],
        @"picList" : [FNFreshStoreServiceBannerItemModel class],
        @"goodsList" : [FNFreshStoreServiceGoodsItemModel class],
        @"pondList" : [FNFreshStoreServiceHotSellingItemModel class],
        @"couponCenter" : [FNFreshStoreServiceCouponCenterModel class],
        @"limited" : [FNFreshStoreServiceTodayLimitModel class],
        @"picList2" : [FNFreshStoreServiceBannerItemModel class],
        @"operationsModel": [FNCreditOperationsModel class],
        @"tabList": [FNFreshStoreServiceGoodsModuleModel class],
        @"moduleList": [FNFreshStoreServiceCouponCenterModuleModel class],
    };
}

@end


//榜单 item
@implementation FNFreshStoreServiceHotSellingItemModel
+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"goodsList" : [FNFreshStoreServiceGoodsItemModel class],
    };
}
@end


//领券中心
@implementation FNFreshStoreServiceCouponCenterModel
+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"couponList" : [FNMrFreshPopupWindowNewGuidanceItemModel class],
    };
}
@end

//当日限定
@implementation FNFreshStoreServiceTodayLimitModel
+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"goodsList" : [FNFreshStoreServiceGoodsItemModel class],
    };
}

- (void)setDeadLine:(long)deadLine {
    _deadLine = deadLine;
    self.countDownTime = deadLine/1000.0;
}

@end


//商品陈列
@implementation FNFreshStoreServiceGoodsModuleModel

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"goodsList" : [FNFreshProductListMerchandiseModel class],
    };
}


- (BOOL)isShowMoreView {
    if (_goodsList.count > 0 && _hotLinkUrl.length > 0) {
        return true;
    }
    return false;
}
@end

@implementation FNFreshStoreServiceCouponCenterModuleModel

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"couponList" : [FNFreshStoreServiceConponModel class],
        @"pic" : [FNFreshStoreServiceCouponCenterModulePicModel class]
    };
}

@end

@implementation FNFreshStoreServiceCouponCenterModulePicModel

@end

@implementation FNFreshStoreServiceConponModel

@end
