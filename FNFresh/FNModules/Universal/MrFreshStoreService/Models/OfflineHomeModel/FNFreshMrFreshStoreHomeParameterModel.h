//
//  FNFreshMrFreshStoreHomeParameterModel.h
//  FNFresh
//
//  Created by wa<PERSON><PERSON> on 2019/9/25.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

#import "FNFreshBaseParameterModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface FNFreshMrFreshStoreHomeParameterModel : FNFreshBaseParameterModel

@property (copy, nonatomic) NSString *storeCode;
@property (copy, nonatomic) NSString *latitude;
@property (copy, nonatomic) NSString *longitude;
@property (copy, nonatomic) NSString *curLatitude;
@property (copy, nonatomic) NSString *curLongitude;

@end


@interface FNFreshMrFreshStoreHomeSignParameterModel : FNFreshBaseParameterModel

@property (copy, nonatomic) NSString *storeCode;
//获取成长值类型：0：每日登录送成长值 1：门店打卡送成长值
@property (copy, nonatomic) NSString *type;

@end


@interface FNFreshMrFreshStoreHomeSignGetIntegralParameterModel : FNFreshBaseParameterModel

//门店id
@property (copy, nonatomic) NSString *storeCode;
//获取积分类型： 1：门店打卡送积分 2：分享完成送积分 3：浏览完成送积分
@property (copy, nonatomic) NSString *type;
//客户位置纬度type=1必传
@property (copy, nonatomic)NSString *latitude;
//客户位置经度type=1必传
@property (copy, nonatomic)NSString *longitude;

//任务id,type=2，3必传
@property (copy, nonatomic)NSString *taskId;
//type=2，3必传
@property (copy, nonatomic)NSString *pageId;
@end


NS_ASSUME_NONNULL_END
