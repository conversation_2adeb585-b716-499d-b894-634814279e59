//
//  FNFreshStoreServiceResponseModel.m
//  FNFresh
//
//  Created by xn on 2022/1/10.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceResponseModel.h"

@implementation FNFreshStoreServiceResponseModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"headBannerUrl":@"body.headBannerUrl",
        @"storeName":@"body.storeName",
        @"storeAddress":@"body.storeAddress",
        @"shopInfo":@"body.shopInfo",
        @"storeService":@"body.storeService",
        @"storeBanner":@"body.storeBanner",
        @"tipMsg":@"body.tipMsg",
        @"integral":@"body.integral",
        @"content":@"body.content",
        @"clockInState":@"body.clockInState",
        @"ruleUrl":@"body.ruleUrl",
        @"saveMoneyTip":@"body.saveMoneyTip",
        @"growthValue":@"body.growthValue",
        @"shoppingCardBalance":@"body.shoppingCardBalance",
        @"voucherCount":@"body.voucherCount",
        @"bottomIcon":@"body.bottomIcon"
    };
}


+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"shopInfo" : [FNFreshShopInfoModel class],
        @"storeService" : [FNFreshStoreServiceItemModel class],
        @"storeBanner" : [FNFreshStoreServiceBannerItemModel class],
        @"content" : [FNFreshStoreServiceModel class],
    };
}

@end

//门店服务
@implementation FNFreshStoreServiceItemModel
@end
