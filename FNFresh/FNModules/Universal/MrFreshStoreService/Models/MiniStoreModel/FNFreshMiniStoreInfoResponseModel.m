//
//  FNFreshMiniStoreInfoResponseModel.m
//  FNFresh
//
//  Created by wangbo on 2020/6/19.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshMiniStoreInfoResponseModel.h"

@implementation FNFreshMiniStoreInfoResponseModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"longitude":@"body.longitude",
        @"latitude":@"body.latitude",
        @"provinceName":@"body.provinceName",
        @"cityName":@"body.cityName",
        @"districtName":@"body.districtName",
        @"area1Code":@"body.area1Code",
        @"area2Code":@"body.area2Code",
        @"area3Code":@"body.area3Code",
        @"storeName":@"body.storeName",
        @"storeAddress":@"body.storeAddress",
        @"canNotBindMsg":@"body.canNotBindMsg",
        @"storeId":@"body.storeId"
    };
}

@end
