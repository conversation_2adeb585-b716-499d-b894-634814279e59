//
//  FNFreshMiniStoreInfoResponseModel.h
//  FNFresh
//
//  Created by wangbo on 2020/6/19.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshBaseResponseModel.h"


@interface FNFreshMiniStoreInfoResponseModel : FNFreshBaseResponseModel

@property (nonatomic, strong) NSString *longitude;
@property (nonatomic, strong) NSString *latitude;
@property (nonatomic, strong) NSString *provinceName; // 省名
@property (nonatomic, strong) NSString *cityName;     // 市名
@property (nonatomic, strong) NSString *districtName; // 区名
@property (nonatomic, strong) NSString *area1Code;    // 省码
@property (nonatomic, strong) NSString *area2Code;    // 市码
@property (nonatomic, strong) NSString *area3Code;    // 区码
@property (nonatomic, strong) NSString *storeName;    // 门店名
@property (nonatomic, strong) NSString *storeAddress; // 门店地址
@property (nonatomic, strong) NSString *canNotBindMsg; // 不可办卡msg
@property (nonatomic, strong) NSString *storeId;

@end

