//
//  FNFreshMiniStoreHomeResponseModel.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/6/8.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshMiniStoreHomeResponseModel.h"

@implementation FNFreshMiniStoreHomeResponseModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"storeName":@"body.storeName",
        @"storeAddress":@"body.storeAddress",
        @"isBind":@"body.isBind",
        @"showCardNo":@"body.showCardNo",
        @"barCode":@"body.barCode",
        @"cardPopInfo":@"body.cardPopInfo",
        @"thirdId":@"body.thirdId",
        @"guidCard":@"body.guidCard",
        @"guidCardMobile":@"body.guidCardMobile",
        @"thirdCard":@"body.thirdCard",
        @"thirdCardMobile":@"body.thirdCardMobile",
        @"shopInfo":@"body.shopInfo",
        @"imgList":@"body.imgList"
    };
}

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"shopInfo":[FNFreshShopInfoModel class],
        @"imgList":[FNFreshMiniStorePictureModel class]
    };
}

@end

@implementation FNFreshMiniStorePictureModel


@end
