//
//  FNFreshMiniStoreHomeResponseModel.h
//  FNFresh
//
//  Created by wangbo on 2020/6/8.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshBaseResponseModel.h"
#import "FNFreshShopInfoModel.h"

@class FNFreshMiniStorePictureModel;

@interface FNFreshMiniStoreHomeResponseModel : FNFreshBaseResponseModel

@property (nonatomic, strong) NSString *storeName;      //门店展示名称
@property (nonatomic, strong) NSString *storeAddress;   //门店地址
@property (nonatomic, assign) NSInteger isBind;         //是否绑卡（1：绑卡）
@property (nonatomic, strong) NSString *showCardNo;     //明码
@property (nonatomic, strong) NSString *barCode;        //条码
@property (nonatomic, strong) NSString *cardPopInfo;    //（支付宝才有）
@property (nonatomic, strong) NSString *thirdId;        //第三方ID（支付宝为userId）（前端需要保存下来，选择帮卡时需要传一下）
@property (nonatomic, strong) NSString *guidCard;       //第三方绑定的会员卡号（明码）
@property (nonatomic, strong) NSString *guidCardMobile; //第三方绑定的会员卡对应手机号
@property (nonatomic, strong) NSString *thirdCard;      //Guid绑定的会员卡号（明码）
@property (nonatomic, strong) NSString *thirdCardMobile;//Guid绑定会员卡对应的手机号
@property (nonatomic, strong) FNFreshShopInfoModel *shopInfo;
@property (nonatomic, strong) NSArray<FNFreshMiniStorePictureModel *> *imgList;

@end

@interface FNFreshMiniStorePictureModel : NSObject <YYModel>

@property (nonatomic, strong) NSString *imgUrl;
@property (nonatomic, strong) NSString *imgHeight;
@property (nonatomic, strong) NSString *imgWidth;

@end
