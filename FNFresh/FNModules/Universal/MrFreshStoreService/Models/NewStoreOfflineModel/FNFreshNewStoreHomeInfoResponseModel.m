//
//  FNFreshNewStoreHomeInfoResponseModel.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/10/16.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshNewStoreHomeInfoResponseModel.h"

@implementation FNFreshNewStoreHomeInfoResponseModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"showName":@"body.showName",
        @"storeAddr":@"body.storeAddr",
        @"btnStatus":@"body.btnStatus",
        @"tipMsg":@"body.tipMsg",
        @"btnMsg":@"body.btnMsg",
        @"storeService":@"body.storeService",
        @"shopInfo":@"body.shopInfo",
        @"content":@"body.content",
        @"canSendOpenGift":@"body.canSendOpenGift",
    };
}

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"shopInfo" : [FNFreshShopInfoModel class],
        @"storeService" : [FNMrFreshTextModel class],
        @"content": [FNFreshStoreServiceModel class],
    };
}

@end

@implementation FNFreshStoreBottomInfoResponseModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"bottomIcon":@"body.bottomIcon"
    };
}

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"bottomIcon" : [FNFreshStoreBottomInfoModel class]
    };
}

@end


@implementation FNFreshStoreBottomInfoModel

- (BOOL)isHaveValue{
    if (self != nil && self.title.length > 0 && self.clickImgUrl.length > 0 && self.imgUrl.length > 0){
        return YES;
    }
    return NO;
}

@end
