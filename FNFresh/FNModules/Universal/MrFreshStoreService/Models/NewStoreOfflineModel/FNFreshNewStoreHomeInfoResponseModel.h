//
//  FNFreshNewStoreHomeInfoResponseModel.h
//  FNFresh
//
//  Created by wang<PERSON> on 2020/10/16.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshBaseResponseModel.h"
#import "FNFreshMrFreshResponseModel.h"
#import "FNFreshShopInfoModel.h"
#import "FNFreshStoreServiceBannerItemModel.h"
#import "FNFreshStoreServiceModel.h"

@class FNFreshNewStoreInfo;

@interface FNFreshNewStoreHomeInfoResponseModel : FNFreshBaseResponseModel

@property (copy, nonatomic) NSString *showName;
@property (copy, nonatomic) NSString *storeAddr;
@property (copy, nonatomic) NSString *btnStatus; //开业按钮状态（1：领取新人礼包 2：领取开业大礼包 3：礼包都不可领取）
@property (copy, nonatomic) NSString *tipMsg;
@property (copy, nonatomic) NSString *btnMsg;

@property (nonatomic, strong) FNFreshShopInfoModel *shopInfo;
@property (copy, nonatomic) NSArray<FNMrFreshTextModel *> *storeService;
@property (copy, nonatomic) NSArray<FNFreshStoreServiceModel *> *content;//可配置项目
@property (assign, nonatomic) NSInteger canSendOpenGift;//是否能领取开业礼包，api155版本开始新增字段0-不能，1-能

@end

@class FNFreshStoreBottomInfoModel;

@interface FNFreshStoreBottomInfoResponseModel : FNFreshBaseResponseModel

@property (nonatomic, strong) FNFreshStoreBottomInfoModel *bottomIcon;

@end

@interface FNFreshStoreBottomInfoModel : NSObject<YYModel>
//icon图
@property (nonatomic, copy)  NSString *imgUrl;
//点击图片
@property (nonatomic, copy)  NSString *clickImgUrl;
//icon文字
@property (nonatomic, copy)  NSString *title;
//底栏的图片、翻转图片、字 三者都有为YES
@property (nonatomic,assign,readonly)BOOL isHaveValue;

@end
