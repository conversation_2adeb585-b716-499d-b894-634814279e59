//
//  FNFreshNewStoreHomePopGiftWindowResponseModel.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/10/26.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshNewStoreHomePopGiftWindowResponseModel.h"

@implementation FNFreshNewStoreHomePopGiftWindowResponseModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"type":@"body.type",
        @"couponList":@"body.couponList",
        @"status":@"body.status",
        @"toastDesc":@"body.toastDesc"
    };
}

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"couponList":[FNMrFreshPopupWindowNewGuidanceItemModel class]
    };
}

@end
