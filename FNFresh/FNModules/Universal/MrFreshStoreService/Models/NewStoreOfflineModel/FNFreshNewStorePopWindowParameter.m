//
//  FNFreshNewStorePopWindowParameter.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/10/21.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshNewStorePopWindowParameter.h"
#import "NSString+Base64Encrypt.h"

@implementation FNFreshNewStorePopWindowParameter

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"shopInfo":@"body.shopInfo",
        @"currentLatitude":@"body.currentLatitude",
        @"currentLongitude":@"body.currentLongitude",
        @"isRaiseCardLocation":@"body.isRaiseCardLocation"
    };
}

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"shopInfo": [FNFreshShopInfoModel class],
    };
}

- (void)setCurrentLatitude:(NSString *)currentLatitude {
    _currentLatitude = [currentLatitude base64Encrypt];
}
- (void)setCurrentLongitude:(NSString *)currentLongitude {
    _currentLongitude = [currentLongitude base64Encrypt];
}

@end
