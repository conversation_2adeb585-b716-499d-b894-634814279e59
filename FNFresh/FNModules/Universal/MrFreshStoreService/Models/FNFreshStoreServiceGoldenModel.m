//
//  FNFreshStoreServiceGoldenModel.m
//  FNFresh
//
//  Created by <PERSON> on 2024/1/31.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceGoldenModel.h"

@implementation FNFreshStoreServiceGoldenModel

+(FNFreshStoreServiceGoldenModel *)cellModelForGoldenArray:(NSArray<FNFreshStoreServiceGoldenItemModel *> *)goldenArray {
    FNFreshStoreServiceGoldenModel *cellModel = [FNFreshStoreServiceGoldenModel new];
    cellModel.goldenArray = goldenArray;
    return cellModel;
}

@end


@implementation FNFreshStoreServiceGoldenItemModel

+(FNFreshStoreServiceGoldenItemModel *)cellModelForImgStr:(NSString *)imgStr  textStr:(NSString *)textStr floatStr:(NSString *)floatStr {
    FNFreshStoreServiceGoldenItemModel *cellModel = [FNFreshStoreServiceGoldenItemModel new];
    cellModel.imgStr = imgStr;
    cellModel.textStr = textStr;
    cellModel.floatStr = floatStr;
    return cellModel;
}

@end
