//
//  FNFreshStoreServiceGoldenModel.h
//  FNFresh
//
//  Created by <PERSON> on 2024/1/31.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class FNFreshStoreServiceGoldenItemModel;
@interface FNFreshStoreServiceGoldenModel : NSObject

@property (nonatomic,strong) NSArray<FNFreshStoreServiceGoldenItemModel *> *goldenArray;

+(FNFreshStoreServiceGoldenModel *)cellModelForGoldenArray:(NSArray<FNFreshStoreServiceGoldenItemModel *> *)goldenArray;

@end

@interface FNFreshStoreServiceGoldenItemModel : NSObject

//图片url
@property (nonatomic,strong) NSString *imgStr;
//文本
@property (nonatomic,strong) NSString *textStr;
//飘文案
@property (nonatomic,strong) NSString *floatStr;

+(FNFreshStoreServiceGoldenItemModel *)cellModelForImgStr:(NSString *)imgStr  textStr:(NSString *)textStr floatStr:(NSString *)floatStr;
@end

NS_ASSUME_NONNULL_END
