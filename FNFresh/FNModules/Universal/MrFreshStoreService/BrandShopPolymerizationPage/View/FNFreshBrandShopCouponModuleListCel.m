//
//  FNFreshBrandShopCouponModuleListCel.m
//  FNFresh
//
//  Created by WangDan on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopCouponModuleListCel.h"
#import "FNFreshImageLabelStyleView.h"
#import "FNCreditChannelStockProgressView.h"
#import "UILabel+factory.h"
#import "UIView+Corners.h"
@interface FNFreshBrandShopCouponModuleListCel()

@property (nonatomic,strong)UIView *bgContentView;

@property (nonatomic,strong)UIImageView *contentImageView;

@property (nonatomic,strong)UILabel *titleLabel;

@property (nonatomic,strong)UILabel *subTitleLabel;

@property (nonatomic,strong)FNCreditChannelStockProgressView *progressView;

@property (nonatomic,strong)FNFreshImageLabelStyleView *rightButton;

@property (nonatomic,strong)FNFreshImageLabelStyleView *bottomSignView;

@end

@implementation FNFreshBrandShopCouponModuleListCel

- (void)setDataModel:(FNFreshBranShopCouponModuleCellModel *)dataModel{
    _dataModel = dataModel;
    self.imageNameString = dataModel.imageNameString;
    self.titleString = dataModel.titleString;
    self.subTitleString = dataModel.subTitleString;
    self.progress = dataModel.progress;
    self.bottomSignString = dataModel.bottomSignString;
    self.bottomMargin = dataModel.bottomMargin;
    self.rightBtnTitleString = dataModel.rightBtnTitleString;
    self.rightButtonStyle = dataModel.rightButtonStyle;
}

- (void)setImageNameString:(NSString *)imageNameString{
    _imageNameString = imageNameString;
    [self.contentImageView fn_setImageWithURL:[NSURL URLWithString:imageNameString ? imageNameString : @""] placeholder:nil];
}

- (void)setTitleString:(NSString *)titleString{
    _titleString = titleString;
    self.titleLabel.text = titleString;
}

- (void)setSubTitleString:(NSString *)subTitleString{
    _subTitleString = subTitleString;
    self.subTitleLabel.text = subTitleString;
}

- (void)setProgress:(CGFloat)progress{
    _progress = progress;
    self.progressView.progress = progress;
}

- (void)setBottomSignString:(NSString *)bottomSignString{
    _bottomSignString = bottomSignString;
    [self.bottomSignView setTitle:bottomSignString forState:UIControlStateNormal];
}

- (void)setBottomMargin:(CGFloat)bottomMargin{
    _bottomMargin = bottomMargin;
    [self.bgContentView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.contentView.mas_bottom).mas_offset(-1 * self.bottomMargin);
    }];
}

- (void)setRightBtnTitleString:(NSString *)rightBtnTitleString{
    _rightBtnTitleString = rightBtnTitleString;
    [self.rightButton setTitle:rightBtnTitleString forState:UIControlStateNormal];
}

- (void)setRightButtonStyle:(enum FNFreshBranShopCouponModuleCellRightButtonStyle)rightButtonStyle{
    _rightButtonStyle = rightButtonStyle;
    
    switch (rightButtonStyle) {
        case FNFreshBranShopCouponModuleCellRightButtonStyle_canClick:
        {
            _rightButton.attributeGradientDataModel = [FNFreshGradientViewDataModel gradientDataModelForStartPoint:CGPointMake(1, 0.5) endPoint:CGPointMake(0, 0.5) location: @[@(0), @(1.0f)] colorStrings:@[@"#C68C53",@"#EBBD80"]];
        }
            break;
        case FNFreshBranShopCouponModuleCellRightButtonStyle_noClick:
        {
            _rightButton.attributeGradientDataModel = [FNFreshGradientViewDataModel gradientDataModelForStartPoint:CGPointMake(1, 0.5) endPoint:CGPointMake(0, 0.5) location: @[@(0), @(1.0f)] colorStrings:@[@"#DEDEDE",@"#DEDEDE"]];
        }
            break;
        default:
            break;
    }
}


- (void)initData{
    [super initData];
    _bottomMargin = 6;
}

- (void)addChildView{
    [super addChildView];
    
    self.containerView.backgroundColor = [UIColor fn_colorWithHex:@"#F4E3CE"];

    [self.containerView addSubview:self.bgContentView];
    [self.bgContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.containerView).mas_offset(8);
        make.trailing.mas_equalTo(self.containerView.mas_trailing).mas_offset(-8);
        make.top.mas_equalTo(self.containerView);
        make.bottom.mas_equalTo(self.contentView.mas_bottom).mas_offset(-1 * self.bottomMargin);
    }];
    
    [self.bgContentView addSubview:self.contentImageView];
    [self.contentImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.bgContentView).mas_offset(8);
        make.top.mas_equalTo(self.bgContentView).mas_offset(8);
        make.bottom.mas_equalTo(self.bgContentView.mas_bottom).mas_offset(-8);
        make.size.mas_equalTo(CGSizeMake(64, 64));
    }];
    [self.bgContentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentImageView.mas_trailing).mas_offset(6);
        make.top.mas_equalTo(self.contentImageView);
        make.trailing.mas_lessThanOrEqualTo(self.bgContentView.mas_trailing).mas_offset(-8);
    }];

    [self.bgContentView addSubview:self.subTitleLabel];
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentImageView.mas_trailing).mas_offset(6);
        make.top.mas_equalTo(self.titleLabel.mas_bottom).mas_offset(4);
    }];

    [self.bgContentView addSubview:self.progressView];
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentImageView.mas_trailing).mas_offset(6);
        make.top.mas_equalTo(self.subTitleLabel.mas_bottom).mas_offset(7);
        make.height.mas_equalTo(13);
        make.trailing.mas_equalTo(self.bgContentView.mas_trailing).mas_offset(-108);
    }];

    [self.bgContentView addSubview:self.rightButton];
    [self.rightButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.subTitleLabel);
        make.trailing.mas_equalTo(self.bgContentView.mas_trailing).mas_offset(-8);
        make.width.mas_equalTo(78);
        make.leading.mas_greaterThanOrEqualTo(self.subTitleLabel.mas_trailing).mas_offset(8).priorityHigh();
    }];
    
    [self.bgContentView addSubview:self.bottomSignView];
    [self.bottomSignView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.bgContentView);
        make.trailing.mas_equalTo(self.bgContentView.mas_trailing);
    }];
}


- (void)layoutSubviews{
    [super layoutSubviews];
    
    [self.bgContentView setCornerRadius:5];
    [self.rightButton setCornerRadius:14];
    [self.bottomSignView cutCornerRadius:5 bounds:self.bottomSignView.bounds rectCorner:UIRectCornerTopLeft];
}

- (void)setBindEvent{
    [super setBindEvent];
    __weak FNFreshBrandShopCouponModuleListCel *weakSelf = self;
    self.rightButton.clickControlBlock = ^{
        [weakSelf.nextResponder touchActionName:FNFreshBrandShopEventNameEnumAtOnceGetClickValue object:weakSelf userInfo:nil];
    };
}

- (UIView *)bgContentView{
    if (!_bgContentView){
        _bgContentView = [UIView new];
        _bgContentView.backgroundColor = [UIColor fn_colorWithHex:@"#FFFFFF"];
    }
    return _bgContentView;
}

- (UIImageView *)contentImageView{
    if (!_contentImageView){
        _contentImageView = [UIImageView new];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFill;
        _contentImageView.backgroundColor = [UIColor redColor];
        _contentImageView.clipsToBounds = YES;
        [_contentImageView setCornerRadius:5];
        _contentImageView.backgroundColor = [UIColor fn_colorWithHex:@"#F8F8F8"];

    }
    return _contentImageView;
}

- (UILabel *)titleLabel{
    if (!_titleLabel){
        _titleLabel = [UILabel labelWithTextColorString:@"#333333" fontSize:16 fontWeight:UIFontWeightMedium];
        _titleLabel.numberOfLines = 1;
        [_titleLabel setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel{
    if (!_subTitleLabel){
        _subTitleLabel = [UILabel labelWithTextColorString:@"#666666" fontSize:12 fontWeight:UIFontWeightRegular];
        [_subTitleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        _subTitleLabel.numberOfLines = 1;
    }
    return _subTitleLabel;
}

- (FNCreditChannelStockProgressView *)progressView{
    if (!_progressView){
        _progressView = [FNCreditChannelStockProgressView new];
        _progressView.bgColor = [UIColor fn_colorWithHex:@"#F8E8D5"];
        _progressView.progressColor = [UIColor fn_colorWithHex:@"#DEAA76"];
        _progressView.indexImageNameString = @"store_service_brandShop_indexProgress_icon";
        _progressView.clipsToBounds = NO;
        _progressView.indexImageContentModel = UIViewContentModeScaleToFill;
    }
    return _progressView;
}

- (FNFreshImageLabelStyleView *)rightButton{
    if (!_rightButton){
        _rightButton = [FNFreshImageLabelStyleView new];
        _rightButton.style = FNFreshImageLabelStyleOnlyTitle;
        _rightButton.edge = UIEdgeInsetsMake(5, 10, 5, 10);
        [_rightButton setTitleColor:[UIColor fn_colorWithHex:@"#FFFFFF"] forState:UIControlStateNormal];
        [_rightButton.titleLabel setFont:[UIFont systemFontOfSize:13 weight:UIFontWeightRegular]];
        [_rightButton setTitle:@"立即领取" forState:UIControlStateNormal];
        [_rightButton setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        [_rightButton setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        _rightButton.attributeGradientDataModel = [FNFreshGradientViewDataModel gradientDataModelForStartPoint:CGPointMake(1, 0.5) endPoint:CGPointMake(0, 0.5) location: @[@(0), @(1.0f)] colorStrings:@[@"#C68C53",@"#EBBD80"]];
    }
    return _rightButton;
}

- (FNFreshImageLabelStyleView *)bottomSignView{
    if (!_bottomSignView){
        _bottomSignView = [FNFreshImageLabelStyleView new];
        _bottomSignView.style = FNFreshImageLabelStyleOnlyTitle;
        _bottomSignView.edge = UIEdgeInsetsMake(3, 6, 3, 6);
        [_bottomSignView setTitleColor:[UIColor fn_colorWithHex:@"#67390F"] forState:UIControlStateNormal];
        [_bottomSignView.titleLabel setFont:[UIFont systemFontOfSize:10 weight:UIFontWeightRegular]];
        [_bottomSignView setTitle:@"宝洁双会员专享" forState:UIControlStateNormal];
        _bottomSignView.backgroundColor = [UIColor fn_colorWithHex:@"#FEF0DF"];
    }
    return _bottomSignView;
}

@end
