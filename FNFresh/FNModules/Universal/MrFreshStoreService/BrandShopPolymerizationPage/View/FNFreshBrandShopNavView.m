//
//  FNFreshBrandShopNavView.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/28.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopNavView.h"

@interface FNFreshBrandShopNavView()

@property (nonatomic,strong)UIButton *backButton;

@property (nonatomic,strong)UIImageView *brandShopImageView;

@end

@implementation FNFreshBrandShopNavView

- (void)setAlpha:(CGFloat)alpha{
    [super setAlpha:alpha];
    self.backButton.alpha = alpha == 1? 1 : 0;
}

- (void)addChildView{
    [super addChildView];
    
    [self addSubview:self.backButton];
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self).mas_offset(12);
        make.bottom.mas_equalTo(self).mas_offset(-7);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
    
    [self addSubview:self.brandShopImageView];
    [self.brandShopImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.backButton);
        make.centerX.mas_equalTo(self);
        make.size.mas_equalTo(CGSizeMake(100, 30));
    }];
}

- (void)actionBackButton{
    if (self.delegate && [self.delegate respondsToSelector:@selector(backClick)]) {
        [self.delegate backClick];
    }
}


- (UIImageView *)brandShopImageView{
    if (!_brandShopImageView){
        _brandShopImageView = [UIImageView new];
        _brandShopImageView.backgroundColor = [UIColor randomColor];
        _brandShopImageView.hidden = YES;
    }
    return _brandShopImageView;
}

- (UIButton *)backButton{
    if (!_backButton){
        _backButton = [UIButton buttonWithType:0];
        [_backButton setImage:[UIImage fnFresh_imageNamed:@"orderNavbarReturnBlack"] forState:UIControlStateNormal];
        _backButton.frame = CGRectMake(0, 0, 30, 30);
        _backButton.exclusiveTouch = YES;
        [_backButton addTarget:self action:@selector(actionBackButton) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backButton;
}

@end
