//
//  FNFreshBrandShopHeaderCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/26.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopHeaderCell.h"
#import "FNFreshImageLabelStyleView.h"
#import "UILabel+factory.h"
#import "FNFreshBrandShopEventNameString.h"
@interface FNFreshBrandShopHeaderCell()

@property (nonatomic,strong)UILabel *titleLabel;

@property (nonatomic,strong)UILabel *subTitleLabel;

@property (nonatomic,strong)FNFreshImageLabelStyleView *rightButton;

@property (nonatomic,strong)UIView *bgContentView;

@end

@implementation FNFreshBrandShopHeaderCell

- (void)setTitleString:(NSString *)titleString{
    _titleString = titleString;
    self.titleLabel.text = titleString;
}

- (void)setSubTitleString:(NSString *)subTitleString{
    _subTitleString = subTitleString;
    self.subTitleLabel.text = subTitleString;
}

- (void)setRightTitleString:(NSString *)rightTitleString{
    _rightTitleString = rightTitleString;
    [self.rightButton setTitle:rightTitleString forState:UIControlStateNormal];
    self.rightButton.hidden = rightTitleString.length > 0 ? false : true;
}
- (void)setIsShowArrow:(BOOL)isShowArrow{
    _isShowArrow = isShowArrow;
    self.rightButton.style = isShowArrow == true ? FNFreshImageLabelStyleLeftTitleRightImage : FNFreshImageLabelStyleOnlyTitle;
}

- (void)setBgColorString:(NSString *)bgColorString{
    _bgColorString = bgColorString;
    self.bgContentView.backgroundColor = [UIColor fn_colorWithHex:bgColorString];
}


- (void)setBgEdgeInsets:(UIEdgeInsets)bgEdgeInsets{
    _bgEdgeInsets = bgEdgeInsets;
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.bgContentView).mas_offset(self.bgEdgeInsets.left);
        make.top.mas_equalTo(self.bgContentView).mas_offset(self.bgEdgeInsets.top);
        make.bottom.mas_equalTo(self.bgContentView).mas_offset(-1 * self.bgEdgeInsets.bottom);
    }];
}

- (void)initData{
    [super initData];
//    _leftMargin = 12;
    _bgEdgeInsets = UIEdgeInsetsMake(0, 12, 0, 0);
}

- (void)addChildView{
    [super addChildView];
    
    [self.containerView addSubview:self.bgContentView];
    [self.bgContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 0, 0, 0));
    }];
    
    [self.bgContentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.bgContentView).mas_offset(self.bgEdgeInsets.top);
        make.top.mas_equalTo(self.bgContentView);
        make.bottom.mas_equalTo(self.bgContentView);
    }];
    
    [self.bgContentView addSubview:self.subTitleLabel];
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.titleLabel);
        make.leading.mas_equalTo(self.titleLabel.mas_trailing).mas_offset(6);
    }];

    [self.bgContentView addSubview:self.rightButton];
    [self.rightButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.titleLabel);
        make.trailing.mas_equalTo(self.bgContentView.mas_trailing).mas_offset(-12);
        make.leading.mas_greaterThanOrEqualTo(self.subTitleLabel.mas_trailing).mas_offset(10);
    }];
}

- (void)setBindEvent{
    [super setBindEvent];
    __weak FNFreshBrandShopHeaderCell *weakSelf = self;
    self.rightButton.clickControlBlock = ^{
        [weakSelf.nextResponder touchActionName:FNFreshBrandShopEventNameEnumBrandHeaderBrowseClickValue object:weakSelf userInfo:nil];
    };
}

#pragma mark -<lazy>

- (UILabel *)titleLabel{
    if (!_titleLabel){
        _titleLabel = [UILabel labelWithTextColorString:@"#333333" fontSize:16 fontWeight:UIFontWeightMedium];
        _titleLabel.numberOfLines = 1;
        [_titleLabel setContentCompressionResistancePriority:300 forAxis:UILayoutConstraintAxisHorizontal];

    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel{
    if (!_subTitleLabel){
        _subTitleLabel = [UILabel labelWithTextColorString:@"#666666" fontSize:12 fontWeight:UIFontWeightRegular];
        _subTitleLabel.numberOfLines = 1;
        [_subTitleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _subTitleLabel;
}

- (FNFreshImageLabelStyleView *)rightButton{
    if (!_rightButton){
        _rightButton = [FNFreshImageLabelStyleView new];
        _rightButton.style = FNFreshImageLabelStyleLeftTitleRightImage;
        _rightButton.spacing = 2;
        [_rightButton setTitleColor:[UIColor fn_colorWithHex:@"#333333"] forState:UIControlStateNormal];
        [_rightButton setImage:[UIImage imageNamed:@"icon_store_service_right_arrow"] forState:UIControlStateNormal];
        [_rightButton.titleLabel setFont:[UIFont systemFontOfSize:13 weight:UIFontWeightRegular]];
        [_rightButton setTitle:@"查看" forState:UIControlStateNormal];
    }
    return _rightButton;
}

- (UIView *)bgContentView{
    if (!_bgContentView){
        _bgContentView = [UIView new];
    }
    return _bgContentView;
}


@end
