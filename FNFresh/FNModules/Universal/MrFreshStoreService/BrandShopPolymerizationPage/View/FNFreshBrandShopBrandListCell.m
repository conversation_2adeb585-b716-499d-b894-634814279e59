//
//  FNFreshBrandShopBrandListCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopBrandListCell.h"
#import "FNFreshGradientBaseView.h"
#import "FNFreshImageLabelStyleView.h"
#import "UILabel+factory.h"
@interface FNFreshBrandShopBrandListCell()

@property (nonatomic,strong)FNFreshGradientBaseView *bgView;

@property (nonatomic,strong)UIImageView *iconImageView;

@property (nonatomic,strong)UILabel *titleLabel;

@property (nonatomic,strong)UILabel *contentLabel;

@property (nonatomic,strong)FNFreshImageLabelStyleView *bottomBtn;

@property (nonatomic,strong)FNFreshGradationView *gradientBackgroundView;

@end

@implementation FNFreshBrandShopBrandListCell

- (void)setDataModel:(FNFreshBranShopBrandListCellModel *)dataModel{
    _dataModel = dataModel;
    self.imageNameString = dataModel.imageNameString;
    self.titleString = dataModel.titleString;
    self.contentString = dataModel.contentString;
    self.bottomBtnString = dataModel.bottomBtnString;
    self.bgColorString = dataModel.bgColorString;
}

- (void)setImageNameString:(NSString *)imageNameString{
    _imageNameString = imageNameString;
    [self.iconImageView fn_setImageWithURL:[NSURL URLWithString:imageNameString ? imageNameString : @""] placeholder:nil];
}

- (void)setTitleString:(NSString *)titleString{
    _titleString = titleString;
    self.titleLabel.text = titleString;
}

- (void)setContentString:(NSString *)contentString{
    _contentString = contentString;
    self.contentLabel.text = contentString;
}

- (void)setBottomBtnString:(NSString *)bottomBtnString{
    _bottomBtnString = bottomBtnString;
    [self.bottomBtn setTitle:[NSString stringWithFormat:@"%@",bottomBtnString] forState:UIControlStateNormal];
}

- (void)setBgColorString:(NSString *)bgColorString{
    _bgColorString = bgColorString;
    self.bgView.backgroundColor = [UIColor fn_colorWithHex:bgColorString];
}

- (void)addChildView{
    [super addChildView];
    
    [self.contentView addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.contentView);
    }];
    [self.contentView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.contentView).mas_offset(12);
        make.top.mas_equalTo(self.contentView).mas_offset(12);
        make.size.mas_equalTo(CGSizeMake(40, 40));
    }];
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.iconImageView);
        make.leading.mas_equalTo(self.iconImageView.mas_trailing).mas_offset(10);
        make.trailing.mas_lessThanOrEqualTo(self.contentView.mas_trailing).mas_offset(-12);
    }];
    [self.contentView addSubview:self.contentLabel];
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.iconImageView);
        make.top.mas_equalTo(self.iconImageView.mas_bottom).mas_offset(6);
        make.trailing.mas_lessThanOrEqualTo(self.contentView.mas_trailing).mas_offset(-12);
    }];
    [self.contentView addSubview:self.gradientBackgroundView];
    [self.gradientBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.mas_equalTo(self.contentView);
    }];
    
    [self.gradientBackgroundView addSubview:self.bottomBtn];
    [self.bottomBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.top.mas_equalTo(self.gradientBackgroundView);
        make.centerX.mas_equalTo(self.gradientBackgroundView);
        make.height.mas_equalTo(30);
    }];
}

- (void)setBindEvent{
    [super setBindEvent];
    __weak FNFreshBrandShopBrandListCell *weakSelf = self;
    self.bottomBtn.clickControlBlock = ^{
        [weakSelf.nextResponder touchActionName:FNFreshBrandShopEventNameEnumAtOnceBrowseOrGetClickValue object:weakSelf userInfo:nil];
    };
}

- (UIImageView *)iconImageView{
    if (!_iconImageView){
        _iconImageView = [UIImageView new];
        _iconImageView.contentMode = UIViewContentModeScaleAspectFill;
        _iconImageView.backgroundColor = [UIColor redColor];
        [_iconImageView setCornerRadius:20];
        _iconImageView.backgroundColor = [UIColor fn_colorWithHex:@"#f8f8f8"];
    }
    return _iconImageView;
}

- (UILabel *)titleLabel{
    if (!_titleLabel){
        _titleLabel = [UILabel labelWithTextColorString:@"#FFFFFF" fontSize:16 fontWeight:UIFontWeightMedium];
        _titleLabel.numberOfLines = 1;
    }
    return _titleLabel;
}

- (UILabel *)contentLabel{
    if (!_contentLabel){
        _contentLabel = [UILabel labelWithTextColorString:@"#FFFFFF" fontSize:12 fontWeight:UIFontWeightRegular];
        _contentLabel.numberOfLines = 2;
        _contentLabel.textAlignment = NSTextAlignmentLeft;
    }
    return _contentLabel;
}

- (FNFreshImageLabelStyleView *)bottomBtn{
    if (!_bottomBtn){
        _bottomBtn = [FNFreshImageLabelStyleView new];
        _bottomBtn.style = FNFreshImageLabelStyleLeftTitleRightImage;
        _bottomBtn.spacing = 2;
        _bottomBtn.edge = UIEdgeInsetsMake(6, 10, 6, 10);
        [_bottomBtn setImage:[UIImage imageNamed:@"brandShop_right_arrow"] forState:UIControlStateNormal];
        [_bottomBtn setTitleColor:[UIColor fn_colorWithHex:@"#FFFFFF"] forState:UIControlStateNormal];
        [_bottomBtn.titleLabel setFont:[UIFont systemFontOfSize:12 weight:UIFontWeightRegular]];
        [_bottomBtn setTitle:@"立即加入" forState:UIControlStateNormal];
//        _bottomBtn.attributeGradientDataModel = [FNFreshGradientViewDataModel gradientDataModelForStartPoint:CGPointMake(1, 0.5) endPoint:CGPointMake(0, 0.5) location: @[@(0), @(1.0f)] colors:@[(__bridge id)[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.0].CGColor, (__bridge id)[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.1500].CGColor]];
    }
    return _bottomBtn;
}

- (FNFreshGradationView *)gradientBackgroundView{
    if (!_gradientBackgroundView){
        _gradientBackgroundView = [[FNFreshGradationView alloc] initWithFrame:CGRectZero];
        _gradientBackgroundView.hidden = NO;
        _gradientBackgroundView.userInteractionEnabled = YES;
        _gradientBackgroundView.attributeGradientDataModel = [FNFreshGradientViewDataModel gradientDataModelForStartPoint:CGPointMake(1, 0.5) endPoint:CGPointMake(0, 0.5) location: @[@(0), @(1.0f)] colors:@[(__bridge id)[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.0].CGColor, (__bridge id)[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.1500].CGColor]];
    }
    return _gradientBackgroundView;
}


- (FNFreshGradientBaseView *)bgView{
    if (!_bgView){
        _bgView = [[FNFreshGradientBaseView alloc] initWithFrame:CGRectZero];
        _bgView.colors = @[(__bridge id)[UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.0].CGColor, (__bridge id)[UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.2000].CGColor];
        _bgView.startPoint = CGPointMake(1, 0.5);
        _bgView.endPoint = CGPointMake(0, 0.5);
        _bgView.locations = @[@(0), @(1.0f)];
        _bgView.backgroundColor = [UIColor fn_colorWithHex:@"#4EA586"];
    }
    return _bgView;
}


@end
