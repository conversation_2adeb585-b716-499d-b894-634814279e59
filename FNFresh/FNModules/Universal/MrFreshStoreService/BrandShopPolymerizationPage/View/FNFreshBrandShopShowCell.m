//
//  FNFreshBrandShopShowCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopShowCell.h"
#import "FNFresh-Swift.h"
#import "FNFreshBrandShopBannerView.h"

static CGFloat const kFNFreshBrandShopShowHeight = 107;

@interface FNFreshBrandShopShowCell()<FNCardSliderViewDeleage>

@property (nonatomic,strong)UIView *bgContentView;
@property (nonatomic,strong)FNCardSliderView * cardSliderView;

@end

@implementation FNFreshBrandShopShowCell

- (void)addChildView{
    [super addChildView];
    
    self.bgContentView.backgroundColor = [UIColor fn_colorWithHex:@"#A6FFFFFF"];
    [self.contentView addSubview:self.bgContentView];
    [self.bgContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.contentView);
        make.height.mas_equalTo(kFNFreshBrandShopShowHeight);
    }];
    
    [self.bgContentView setNeedsLayout];
    [self.bgContentView layoutIfNeeded];
    
    [self.bgContentView addSubview:self.cardSliderView];
    [self.cardSliderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.mas_equalTo(self.bgContentView);
        make.bottom.mas_equalTo(self.bgContentView).offset(-4);
    }];
    
}

- (void)setDataModel:(NSArray<FNFreshBranShopBrandCellModel *> *)dataModel {
    _dataModel = dataModel;
    self.cardSliderView.count = dataModel.count;
}

#pragma mark - FNCardSliderViewDeleage

- (void)cardSliderView:(FNCardSliderCell * _Nullable)cell cellForItemAt:(NSInteger)index {
    cell.shadowRadius = 0;
    cell.shadowOffset = CGSizeZero;
    cell.cornerRadius = 10;
    [self setupCell:cell index:index];
}

- (void)cardSliderView:(FNCardSliderCell * _Nullable)cell didSelectItemAt:(NSInteger)index {
    [self.nextResponder touchActionName:FNFreshBrandShopEventNameEnumBrandUnitAtOnceBrowseClickValue object:self userInfo:@{
        [FNFreshUserInfoConstKey index]:@(index).description
    }];
}

- (void)cardSliderView:(FNCardSliderCell * _Nullable)cell willDisplayForItemAt:(NSInteger)index {
}


#pragma mark - 私有方法

- (void)setupCell:(FNCardSliderCell *)cell index:(NSInteger)index {
    if (cell.fullContentView == nil) {
        cell.fullContentView = self.bannerView;
        [cell.contentView insertSubview:cell.fullContentView aboveSubview:cell.imageView];
        [cell.fullContentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.mas_equalTo(cell.contentView);
        }];
    }
    if (cell.fullContentView != nil && [cell.fullContentView isKindOfClass:FNFreshBrandShopBannerView.class]) {
        FNFreshBrandShopBannerView * banner = (FNFreshBrandShopBannerView *)cell.fullContentView;
        FNFreshBranShopBrandCellModel * model = [self.dataModel safeObjectAtIndex:index];
        banner.title = model.titleString;
        banner.subTitle = model.contentString;
        banner.logo = model.logoString;
        banner.bgColor = model.bgColorString;
    }
}

#pragma mark - 懒加载

- (UIView *)bgContentView{
    if (!_bgContentView){
        _bgContentView = [UIView new];
    }
    return _bgContentView;
}

- (FNCardSliderView *)cardSliderView {
    if (!_cardSliderView) {
        _cardSliderView = [[FNCardSliderView alloc]init];
        _cardSliderView.cardsLayout.itemSize = CGSizeMake(self.bgContentView.bounds.size.width * 0.85, self.bgContentView.bounds.size.height * 0.85);
        _cardSliderView.cardsLayout.minScale = 0.5;
        _cardSliderView.cardsLayout.spacing = 42.0 * Ratio;
        _cardSliderView.cardsLayout.contentInset = UIEdgeInsetsMake(0, 12, 0, 12);
        _cardSliderView.delegate = self;
        _cardSliderView.backgroundColor = [UIColor clearColor];
    }
    return _cardSliderView;
}

- (FNFreshBrandShopBannerView *)bannerView {
    return [[FNFreshBrandShopBannerView alloc]init];
}

@end
