//
//  FNFreshBrandShopBannerCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopBannerCell.h"
#import "FNFreshBaseImageView.h"
@interface FNFreshBrandShopBannerCell()

@property (nonatomic,strong)FNFreshBaseImageView *contentImageView;

@end

@implementation FNFreshBrandShopBannerCell

- (void)setDataModel:(FNFreshBranShopBannerCellModel *)dataModel{
    _dataModel = dataModel;
    self.imageNameString = dataModel.imageNameString;
    self.scale = dataModel.scale;
}

- (void)setImageNameString:(NSString *)imageNameString{
    _imageNameString = imageNameString;
    [self.contentImageView fn_setImageWithURL:[NSURL URLWithString:imageNameString ? imageNameString : @""] placeholder: nil];
}

- (void)setScale:(CGFloat)scale{
    _scale = scale;
    if(scale != 0){
        CGFloat height = (SCREEN_WIDTH - 24)/scale;
        [self.contentImageView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(height);
        }];
    }
}


- (void)addChildView{
    [super addChildView];
    self.backgroundColor = [UIColor clearColor];
    [self.containerView addSubview:self.contentImageView];
    [self.contentImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.containerView);
        make.height.mas_equalTo(89 * Ratio);
    }];
}

- (void)setBindEvent{
    [super setBindEvent];
    __weak FNFreshBrandShopBannerCell *weakSelf = self;
    self.contentImageView.clickBlock = ^{
        [weakSelf.nextResponder touchActionName:FNFreshBrandShopEventNameEnumBannerClickValue object:weakSelf userInfo:nil];
    };
    
}


- (FNFreshBaseImageView *)contentImageView{
    if (!_contentImageView){
        _contentImageView = [FNFreshBaseImageView new];
        _contentImageView.contentMode = UIViewContentModeScaleAspectFill;
        _contentImageView.backgroundColor = [UIColor fn_colorWithHex:@"#F8F8F8"];
    }
    return _contentImageView;
}

@end
