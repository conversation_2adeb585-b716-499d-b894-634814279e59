//
//  FNFreshBrandShopBannerView.m
//  FNFresh
//
//  Created by mu on 2024/2/2.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopBannerView.h"
#import "FNFreshGradientBaseView.h"

@interface FNFreshBrandShopBannerView ()

@property (nonatomic, strong)UILabel *titleLabel;
@property (nonatomic, strong)UILabel *subTitleLabel;
@property (nonatomic, strong)UIImageView *logoImageView;
@property (nonatomic, strong)UIButton *gotoButton;
@property (nonatomic, strong)FNFreshGradientBaseView *topGradientView;

@end

@implementation FNFreshBrandShopBannerView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self addSubview:self.topGradientView];
    [self.topGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self);
    }];
    [self addSubview:self.logoImageView];
    [self.logoImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(self).offset(-18);
        make.centerY.mas_equalTo(self).offset(0.25);
        make.size.mas_equalTo(CGSizeMake(60, 60));
    }];
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self).offset(18);
        make.top.mas_equalTo(self).offset(12);
        make.right.mas_equalTo(self.logoImageView.mas_left);
    }];
    [self addSubview:self.subTitleLabel];
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self).offset(18);
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(3);
        make.right.mas_equalTo(self.logoImageView.mas_left);
    }];
    [self addSubview:self.gotoButton];
    [self.gotoButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(self).offset(18);
        make.top.mas_equalTo(self.subTitleLabel.mas_bottom).offset(4);
        make.size.mas_equalTo(CGSizeMake(80, 25));
    }];
    self.layer.borderColor = [UIColor whiteColor].CGColor;
    self.layer.borderWidth = 0.5;
    self.layer.cornerRadius = 10;
    self.layer.masksToBounds = YES;
}

- (void)setBgColor:(NSString *)bgColor {
    self.backgroundColor = [UIColor colorForColorString:bgColor];
}

- (void)setTitle:(NSString *)title {
    self.titleLabel.text = title;
}

- (void)setSubTitle:(NSString *)subTitle {
    self.subTitleLabel.text = (subTitle == nil) ? @" " : subTitle;
}

- (void)setLogo:(NSString *)logo {
    [self.logoImageView sd_setImageWithURL:[NSURL URLWithString:logo]
    placeholderImage:[UIImage imageWithColor:self.backgroundColor]];
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc]init];
        _titleLabel.textColor = [UIColor whiteColor];
        _titleLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        _subTitleLabel = [[UILabel alloc]init];
        _subTitleLabel.textColor = [UIColor whiteColor];
        _subTitleLabel.font = [UIFont systemFontOfSize:13];
    }
    return _subTitleLabel;
}

- (UIImageView *)logoImageView {
    if (!_logoImageView) {
        _logoImageView = [[UIImageView alloc]init];
        _logoImageView.layer.masksToBounds = YES;
        _logoImageView.layer.cornerRadius = 30;
    }
    return _logoImageView;
}

- (UIButton *)gotoButton {
    if (!_gotoButton) {
        _gotoButton = [UIButton buttonWithType:UIButtonTypeCustom];
        _gotoButton.userInteractionEnabled = NO;
        _gotoButton.layer.masksToBounds = YES;
        _gotoButton.layer.cornerRadius = 12.5;
        _gotoButton.layer.borderColor = [UIColor whiteColor].CGColor;
        _gotoButton.layer.borderWidth = 0.5;
        _gotoButton.titleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
        [_gotoButton setTitle:@"立即查看" forState:UIControlStateNormal];
        [_gotoButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        [_gotoButton setImage:[UIImage imageNamed:@"icon_arrows_right_10"] forState:UIControlStateNormal];
        [_gotoButton setTitleEdgeInsets:UIEdgeInsetsMake(0, -20, 0, 0)];
        [_gotoButton setImageEdgeInsets:UIEdgeInsetsMake(0, 0, 0, -100)];
    }
    return _gotoButton;
}

- (FNFreshGradientBaseView *)topGradientView{
    if (!_topGradientView){
        _topGradientView = [[FNFreshGradientBaseView alloc] initWithFrame:CGRectZero];
        _topGradientView.colors = @[(__bridge id)[UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.0].CGColor, (__bridge id)[UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.2000].CGColor];
        _topGradientView.startPoint = CGPointMake(1, 0.5);
        _topGradientView.endPoint = CGPointMake(0, 0.5);
        _topGradientView.locations = @[@(0), @(1.0f)];
    }
    return _topGradientView;
}

@end
