//
//  FNFreshBrandShopCouponModuleListCel.h
//  FNFresh
//
//  Created by WangDan on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBaseView.h"
#import "FNFreshBrandShopEventNameString.h"
#import "FNFreshBranShopBrandCellModel.h"
NS_ASSUME_NONNULL_BEGIN
/**
 券模块
 */
@interface FNFreshBrandShopCouponModuleListCel : FNFreshBaseMarginCollectionViewCell


@property (nonatomic,strong)FNFreshBranShopCouponModuleCellModel *dataModel;

// 券图
@property (nonatomic,strong)NSString *imageNameString;
// 券标题
@property (nonatomic,strong)NSString *titleString;
// 适用范围
@property (nonatomic,strong)NSString *subTitleString;
//进度条
@property (nonatomic,assign)CGFloat progress;
//会员提示
@property (nonatomic,strong)NSString *bottomSignString;
// 默认是 6
@property (nonatomic,assign)CGFloat bottomMargin;
// 右边按钮 文案,立即领取,已领取
@property (nonatomic,strong)NSString *rightBtnTitleString;
// 右边按钮 状态
@property (nonatomic,assign)enum FNFreshBranShopCouponModuleCellRightButtonStyle rightButtonStyle;
@end

NS_ASSUME_NONNULL_END
