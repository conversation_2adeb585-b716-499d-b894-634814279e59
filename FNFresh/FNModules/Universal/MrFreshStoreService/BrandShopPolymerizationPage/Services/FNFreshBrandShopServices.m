//
//  FNFreshBrandShopServices.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopServices.h"

@implementation FNFreshBrandShopServices

/// 请求品牌馆聚合页数据
/// - Parameters:
///   - parameter: <#parameter description#>
///   - success: <#success description#>
///   - failure: <#failure description#>
+ (void)requestBrandShopPageDataWithParameter:(id)parameter success:(FNNetworkRequestSuccess)success failure:(FNNetworkRequestFailure)failure{
    FNNetworkRequest *request = [[FNNetworkRequest alloc] init];
    request.path = kBrandAggregation;
//    request.path = @"http://search-yxapp.beta1.fn/brandPage/i192";
    request.params = parameter;
    if (![parameter isKindOfClass:[NSDictionary class]]){
        request.reformClass = [FNFreshBrandShopPageResponseModel class];
    }
    [[FNNetworkClient freshClient] sendRequest:request success:success failure:failure];
}

/// 立即领取
/// - Parameters:
///   - parameter: parameter description
///   - success: <#success description#>
///   - failure: <#failure description#>
+ (void)requestAtOnceGetWithParameter:(id)parameter success:(FNNetworkRequestSuccess)success failure:(FNNetworkRequestFailure)failure{
    FNNetworkRequest *request = [[FNNetworkRequest alloc] init];
    request.path = kBrandVoucherGet;
//    request.path = @"http://search-yxapp.beta1.fn/acOnceGet/i192";
    request.params = parameter;
    if (![parameter isKindOfClass:[NSDictionary class]]){
        request.reformClass = [FNFreshBrandShopPageAcOnceGetResponseModel class];
    }
    [[FNNetworkClient freshClient] sendRequest:request success:success failure:failure];
    
}

@end
