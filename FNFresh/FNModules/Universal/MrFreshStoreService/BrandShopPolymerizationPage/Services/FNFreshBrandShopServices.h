//
//  FNFreshBrandShopServices.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshBrandShopPageResponseModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface FNFreshBrandShopServices : NSObject

/// 请求品牌馆聚合页数据
/// - Parameters:
///   - parameter: <#parameter description#>
///   - success: <#success description#>
///   - failure: <#failure description#>
+ (void)requestBrandShopPageDataWithParameter:(id)parameter success:(FNNetworkRequestSuccess)success failure:(FNNetworkRequestFailure)failure;

/// 立即领取
/// - Parameters:
///   - parameter: parameter description
///   - success: <#success description#>
///   - failure: <#failure description#>
+ (void)requestAtOnceGetWithParameter:(id)parameter success:(FNNetworkRequestSuccess)success failure:(FNNetworkRequestFailure)failure;

@end

NS_ASSUME_NONNULL_END
