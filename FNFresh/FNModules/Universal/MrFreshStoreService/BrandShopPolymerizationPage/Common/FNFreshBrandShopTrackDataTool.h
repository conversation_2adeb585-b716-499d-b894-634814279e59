//
//  FNFreshBrandShopTrackDataTool.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/2/3.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshBuryEnumerationKeysString.h"

typedef NS_ENUM(NSUInteger,FNFreshBrandShopTrackType) {
    FNFreshBrandShopTrackType_showBrandShopPage = 100,//浏览品牌馆聚合页
    FNFreshBrandShopTrackType_showBrandVenueModule, //入会品牌大模块曝光
    FNFreshBrandShopTrackType_clickBrandVenueModule,//点击入会品牌各区块,上报区块对应品牌名称
    FNFreshBrandShopTrackType_showBannerModule,//横幅banner模块曝光
    FNFreshBrandShopTrackType_clickBannerModule,//点击横幅banner
    FNFreshBrandShopTrackType_showCouponsModule,//券模块曝光
    FNFreshBrandShopTrackType_clickCouponsModule,//点击券模块按钮,1、未入会；2、已入会
    FNFreshBrandShopTrackType_clickGoMeetingEvent,//券模块入会成功事件
    FNFreshBrandShopTrackType_showBrandCollection,//品牌陈列模块曝光
    FNFreshBrandShopTrackType_clickBrandCollection,//点击品牌陈列模块,上报模块对应品牌名称
    FNFreshBrandShopTrackType_clickBrandCollectionGoMeetingEvent//品牌陈列模块入会成功事件
};

NS_ASSUME_NONNULL_BEGIN

@interface FNFreshBrandShopTrackDataTool : NSObject
/** 埋点 */
+ (void)eventTrackFor:(FNFreshBrandShopTrackType)type info:(NSDictionary *_Nullable)info;
@end

NS_ASSUME_NONNULL_END
