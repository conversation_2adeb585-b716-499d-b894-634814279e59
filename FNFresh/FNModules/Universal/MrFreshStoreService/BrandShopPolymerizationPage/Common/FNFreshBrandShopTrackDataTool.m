//
//  FNFreshBrandShopTrackDataTool.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/2/3.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopTrackDataTool.h"



@implementation FNFreshBrandShopTrackDataTool
/** 埋点 */
+ (void)eventTrackFor:(FNFreshBrandShopTrackType)type info:(NSDictionary *_Nullable)info{

    FNFreshBuryStartTempVariable
    page_id = @"313";
    col_position = info[FNFreshBuryParamsColPositionKeyValue];
    switch (type) {
        case FNFreshBrandShopTrackType_showBrandShopPage:
        {
            track_type = @"1";
            page_col = @"193069";
            break;
        }
        case FNFreshBrandShopTrackType_showBrandVenueModule:
        {
            track_type = @"6";
            page_col = @"193070";
            break;
        }
        case FNFreshBrandShopTrackType_clickBrandVenueModule:
        {
            track_type = @"2";
            page_col = @"193071";
            break;
        }
        case FNFreshBrandShopTrackType_showBannerModule:
        {
            track_type = @"6";
            page_col = @"193072";
            break;
        }
        case FNFreshBrandShopTrackType_clickBannerModule:
        {
            track_type = @"2";
            page_col = @"193073";
            break;
        }
        case FNFreshBrandShopTrackType_showCouponsModule:
        {
            track_type = @"6";
            page_col = @"193074";
            break;
        }
        case FNFreshBrandShopTrackType_clickCouponsModule:
        {
            track_type = @"2";
            page_col = @"193075";
            break;
        }
        case FNFreshBrandShopTrackType_clickGoMeetingEvent:
        {
            track_type = @"6";
            page_col = @"193076";
            break;
        }
        case FNFreshBrandShopTrackType_showBrandCollection:
        {
            track_type = @"6";
            page_col = @"193077";
            break;
        }
        case FNFreshBrandShopTrackType_clickBrandCollection:
        {
            track_type = @"2";
            page_col = @"193078";
            break;
        }
        case FNFreshBrandShopTrackType_clickBrandCollectionGoMeetingEvent:
        {
            track_type = @"6";
            page_col = @"193079";
            break;
        }
        default:
            break;
    }

    FNFreshBuryEndSendTempVariable
    
}


@end
