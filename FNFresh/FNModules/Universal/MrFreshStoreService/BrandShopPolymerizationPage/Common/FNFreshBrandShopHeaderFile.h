//
//  FNFreshBrandShopHeaderFile.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#ifndef FNFreshBrandShopHeaderFile_h
#define FNFreshBrandShopHeaderFile_h


/**
 品牌馆聚合页面 cell Type
 */

typedef NS_ENUM(NSUInteger, FNFreshBrandShopDisplayCellType) {
    FNFreshBrandShopDisplayCellType_brandModuleDisplay = 100, //入会品牌展示
    FNFreshBrandShopDisplayCellType_banner, //横幅BANNER
    FNFreshBrandShopDisplayCellType_couponList,//券列表
    FNFreshBrandShopDisplayCellType_brandList,  //品牌陈列
    
    FNFreshBrandShopDisplayCellType_cellHeader, // 相当于组头
    FNFreshBrandShopDisplayCellType_marginCell //间距cell
};


typedef NS_ENUM(NSUInteger, FNFreshBrandShopDisplayGroup) {
    FNFreshBrandShopDisplayGroup_brandModuleDisplay = 200, //入会品牌展示
    FNFreshBrandShopDisplayGroup_banner, //横幅BANNER
    FNFreshBrandShopDisplayGroup_couponList, //券列表
    FNFreshBrandShopDisplayGroup_brandList, //品牌陈列
    
    FNFreshBrandShopDisplayGroup_header, //每个的组的组头是单独一组
    FNFreshBrandShopDisplayGroup_marginEmpty // 每一组之间的间距 叫做间距组
};

#endif /* FNFreshBrandShopHeaderFile_h */
