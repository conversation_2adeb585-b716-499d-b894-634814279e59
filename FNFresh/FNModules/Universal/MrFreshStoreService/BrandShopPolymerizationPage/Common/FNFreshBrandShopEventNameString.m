//
//  FNFreshBrandShopEventNameString.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopEventNameString.h"

/**
 1.入会品牌展示 事件名字
 */
// 品牌区块 头部查看点击
FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumBrandHeaderBrowseClickValue = @"FNFreshBrandShopEventNameEnumBrandHeaderBrowseClickValue";
// 品牌区块点击
FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumBrandUnitClickValue = @"FNFreshBrandShopEventNameEnumBrandUnitClickValue";
// 品牌区块 更多按钮点击
FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumBrandUnitAtOnceBrowseClickValue = @"FNFreshBrandShopEventNameEnumBrandUnitAtOnceBrowseClickValue";

/**
 2.横幅BANNER
 */
// banner 点击
FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumBannerClickValue = @"FNFreshBrandShopEventNameEnumBannerClickValue";

/**
 3.券模块
 */
// 立即领取 点击
FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumAtOnceGetClickValue = @"FNFreshBrandShopEventNameEnumAtOnceGetClickValue";

/**
 4.品牌陈列
 */
// 立即入会/立即查看 点击
FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumAtOnceBrowseOrGetClickValue = @"FNFreshBrandShopEventNameEnumAtOnceBrowseOrGetClickValue";
