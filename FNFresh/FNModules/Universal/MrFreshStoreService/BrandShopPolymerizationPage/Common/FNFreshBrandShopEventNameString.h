//
//  FNFreshBrandShopEventNameString.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN


typedef NSString *FNFreshBrandShopEventNameEnum NS_TYPED_ENUM;
/**
 1.入会品牌展示 事件名字
 */
// 品牌区块 头部查看点击
extern FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumBrandHeaderBrowseClickValue;
// 品牌区块点击
extern FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumBrandUnitClickValue;
// 品牌区块 立即查看点击
extern FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumBrandUnitAtOnceBrowseClickValue;

/**
 2.横幅BANNER
 */
// banner 点击
extern FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumBannerClickValue;

/**
 3.券模块
 */
// 立即领取 点击
extern FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumAtOnceGetClickValue;

/**
 4.品牌陈列
 */
// 立即入会/立即查看 点击
extern FNFreshBrandShopEventNameEnum const FNFreshBrandShopEventNameEnumAtOnceBrowseOrGetClickValue;



NS_ASSUME_NONNULL_END
