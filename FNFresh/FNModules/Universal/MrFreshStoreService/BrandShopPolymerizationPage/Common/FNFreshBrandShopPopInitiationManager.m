//
//  FNFreshBrandShopPopInitiationManager.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/2/7.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopPopInitiationManager.h"
#import "FNBrandMemberWidget.h"

@interface FNFreshBrandShopPopInitiationManager()
@property (nonatomic,strong)FNFreshBaseTrackModel *trackModel;
@property (nonatomic,strong)NSString *brandId;
@property (nonatomic,strong)NSString *IsBrandPage;
@property (nonatomic,copy)void(^alertAppearBlock)(void);
@property (nonatomic,copy)void(^detailBlock)(void);
@property (nonatomic,copy)void(^joinClosure)(void);
@property (nonatomic,copy)void(^closeClosure)(void);
@property (nonatomic,copy)void(^joinSuccessClosure)(void);

@end

@implementation FNFreshBrandShopPopInitiationManager

- (void)brandMemberAlertControllerWithBrandId:(NSString *)brandId IsBrandPage:(NSString *)isBrandPage alertAppearClosure:(void(^)(void))alertAppearBlock detailClosure:(void(^)(void))detailBlock  joinClosure:(void(^)(void))joinBlock closeClosure:(void(^)(void))closeBlock joinSuccessClosure:(void(^)(void))joinSuccessBlock showAgain:(NSString *)showAgain buryingPointObject:(FNFreshBaseTrackModel *)buryingPointObject {
    self.trackModel = buryingPointObject;
    self.brandId = self.brandId;
    self.IsBrandPage = self.IsBrandPage;
    self.alertAppearBlock = alertAppearBlock;
    self.detailBlock = detailBlock;
    self.joinClosure = joinBlock;
    self.closeClosure = closeBlock;
    self.joinSuccessClosure = joinSuccessBlock;
    [[FNMediator sharedInstance] brandMemberAlertControllerWithBrandId:brandId IsBrandPage:@"0" alertAppearClosure:^{
        //曝光
        if(buryingPointObject){
            self.trackModel.page_col = @"184012";
            self.trackModel.page_id = self.trackModel.page_id;
            self.trackModel.track_type = @"6";
            [self eventTrackForInfo:@{
                FNFreshBuryParamsColPosContentKeyValue: self.trackModel.col_pos_content
            }];
        }
        if (self.alertAppearBlock){
            self.alertAppearBlock();
        }
        
    } detailClosure:^{
        if(buryingPointObject){
            self.trackModel.page_col = @"184023";
            self.trackModel.page_id = self.trackModel.page_id;
            self.trackModel.track_type = @"2";
            [self eventTrackForInfo:@{
                FNFreshBuryParamsColPosContentKeyValue: self.trackModel.col_pos_content
            }];
        }
        if (self.detailBlock){
            self.detailBlock();
        }

    } joinClosure:^{
    
        if(buryingPointObject){
            self.trackModel.page_col = @"184014";
            self.trackModel.page_id = self.trackModel.page_id;
            self.trackModel.track_type = @"2";
            [self eventTrackForInfo:@{
                FNFreshBuryParamsColPosContentKeyValue: self.trackModel.col_pos_content
            }];
        }
        if (self.joinClosure){
            self.joinClosure();
        }
        
    } closeClosure:^{
        if(buryingPointObject){
            self.trackModel.page_col = @"184013";
            self.trackModel.page_id = self.trackModel.page_id;
            self.trackModel.track_type = @"6";
            [self eventTrackForInfo:@{
                FNFreshBuryParamsColPosContentKeyValue: self.trackModel.col_pos_content
            }];
        }
        if (self.closeClosure){
            self.closeClosure();
        }
        
    } joinSuccessClosure:^{
        if(buryingPointObject){
            self.trackModel.page_col = @"184016";
            self.trackModel.page_id = self.trackModel.page_id;
            self.trackModel.track_type = @"2";
            [self eventTrackForInfo:@{
                FNFreshBuryParamsColPosContentKeyValue: self.trackModel.col_pos_content
            }];
        }
        if (self.joinClosure){
            self.joinClosure();
        }
        
    } showAgain: showAgain];
}

- (void)removeAllObserver{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

/** 埋点 */
- (void)eventTrackForInfo:(NSDictionary *)info{
    FNFreshBuryStartTempVariable
    page_id = self.trackModel.page_id;
    col_position = info[FNFreshBuryParamsColPositionKeyValue];
    page_col = self.trackModel.page_col;
    track_type = self.trackModel.track_type.length > 0 ? self.trackModel.track_type : @"2";
    col_pos_content = info[FNFreshBuryParamsColPosContentKeyValue];
    FNFreshBuryEndSendTempVariable
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self initData];
    }
    return self;
}

- (void)initData {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(actionFNNotificationName_RefreshBrandJoin:) name:FNNotificationName_RefreshBrandJoin object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(actionFNNotificationName_showAgainBrandJoin:) name:FNNotificationName_showAgainBrandJoin object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(actionFNNotificationName_brandGiftsAlertAppear:) name:FNNotificationName_brandGiftsAlertAppear object:nil];
}

#pragma mark -<actionNotification>
- (void)actionFNNotificationName_RefreshBrandJoin:(NSNotification *)notification {
    if (self.initiationSuccess){
        self.initiationSuccess();
    }
}

- (void)actionFNNotificationName_showAgainBrandJoin:(NSNotification *)notification {
    
    [self brandMemberAlertControllerWithBrandId:self.brandId IsBrandPage:self.IsBrandPage alertAppearClosure:self.alertAppearBlock detailClosure:self.detailBlock joinClosure:self.joinClosure closeClosure:self.closeClosure joinSuccessClosure:self.joinClosure showAgain:@"1" buryingPointObject:self.trackModel];
}

- (void)actionFNNotificationName_brandGiftsAlertAppear:(NSNotification *)notification {
    if(self.trackModel){
        self.trackModel.page_col = @"184025";
        self.trackModel.page_id = self.trackModel.page_id;
        self.trackModel.track_type = @"6";
        [self eventTrackForInfo:@{
            FNFreshBuryParamsColPosContentKeyValue: self.trackModel.col_pos_content
        }];
    }
}

- (void)dealloc{
    [self removeAllObserver];
}

@end
