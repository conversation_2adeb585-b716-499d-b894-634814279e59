//
//  FNFreshBrandShopPopInitiationManager.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/2/7.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNMediator+FNBrandMemberModule.h"
#import "FNFreshBuryEnumerationKeysString.h"
NS_ASSUME_NONNULL_BEGIN

@interface FNFreshBrandShopPopInitiationManager : NSObject

- (void)brandMemberAlertControllerWithBrandId:(NSString *)brandId IsBrandPage:(NSString *)isBrandPage alertAppearClosure:(void(^)(void))alertAppearBlock detailClosure:(void(^)(void))detailBlock  joinClosure:(void(^)(void))joinBlock closeClosure:(void(^)(void))closeBlock joinSuccessClosure:(void(^)(void))joinSuccessBlock showAgain:(NSString *)showAgain buryingPointObject:(FNFreshBaseTrackModel *)buryingPointObject;
/// 入会成功回调
@property (nonatomic,copy)void(^initiationSuccess)(void);

- (void)removeAllObserver;

@end

NS_ASSUME_NONNULL_END
