//
//  FNFreshBranShopBrandCellModel.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBranShopBrandCellModel.h"

/**
 1.入会入会品牌展示
 */
@implementation FNFreshBranShopBrandCellModel


+ (FNFreshBranShopBrandCellModel *)cellModelForTitleString:(NSString *)titleString contentString:(NSString *)contentString logoString:(NSString *)logoString bgColorString:(NSString *)bgColorString brandId:(NSString *)brandId {
    
    FNFreshBranShopBrandCellModel *cellModel = [[FNFreshBranShopBrandCellModel alloc]init];
    cellModel.titleString = titleString;
    cellModel.contentString = contentString;
    cellModel.logoString = logoString;
    cellModel.bgColorString = bgColorString;
    cellModel.brandId = brandId;
    return cellModel;
    
}

@end

/**
 2.横幅BANNER
 */
@implementation FNFreshBranShopBannerCellModel

+ (FNFreshBranShopBannerCellModel *)cellModelForUrlString:(NSString *)imageNameString{
    FNFreshBranShopBannerCellModel *cellModel = [FNFreshBranShopBannerCellModel new];
    cellModel.imageNameString = imageNameString;
    return cellModel;
}

- (CGFloat)scale{
    return self.itemModel.scale;
}

@end

/**
 3.券模块
 */
@implementation FNFreshBranShopCouponModuleCellModel

+ (FNFreshBranShopCouponModuleCellModel *)cellModelForImageString:(NSString *)imageNameString  title:(NSString *)titleString subTitle: (NSString *)subTitleString progress: (CGFloat )progress bottomSignStr:(NSString *)bottomSignString bottomMargin: (CGFloat)bottomMargin{
    
    FNFreshBranShopCouponModuleCellModel *cellModel = [FNFreshBranShopCouponModuleCellModel new];
    cellModel.imageNameString = imageNameString;
    cellModel.titleString = titleString;
    cellModel.subTitleString = subTitleString;
    cellModel.progress = progress;
    cellModel.bottomSignString = bottomSignString;
    cellModel.bottomMargin = bottomMargin;
    return cellModel;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.rightButtonStyle = FNFreshBranShopCouponModuleCellRightButtonStyle_canClick;
        self.rightBtnTitleString = @"立即领取";
    }
    return self;
}

- (NSString *)rightBtnTitleString{
    if (self.itemModel.statue == 2) {
        return @"已领取";
    }
    return @"立即领取";
}

- (FNFreshBranShopCouponModuleCellRightButtonStyle)rightButtonStyle{
    if (self.itemModel.statue == 2) {
        return FNFreshBranShopCouponModuleCellRightButtonStyle_noClick;
    }
    return FNFreshBranShopCouponModuleCellRightButtonStyle_canClick;
}

@end

/**
 4.品牌陈列
 */
@implementation FNFreshBranShopBrandListCellModel

+ (FNFreshBranShopBrandListCellModel *)cellModelForImageNameString:(NSString *)imageNameString titleString:(NSString *)titleString contentString:(NSString *)contentString bottomBtnString:(NSString *)bottomBtnString bgColorString:(NSString *)bgColorString{
    FNFreshBranShopBrandListCellModel *cellModel = [FNFreshBranShopBrandListCellModel new];
    cellModel.imageNameString = imageNameString;
    cellModel.titleString = titleString;
    cellModel.contentString = contentString;
    cellModel.bottomBtnString = bottomBtnString;
    cellModel.bgColorString = bgColorString;
    return cellModel;
}

@end

/**
 5.头部cellModel
 */
@implementation FNFreshBrandShopHeaderCellModel


+ (FNFreshBrandShopHeaderCellModel *)cellModelForTitle:(NSString *)titleString subTitle: (NSString *)subTitleString rightTitle: (NSString *)rightTitleString isShowArrow:(BOOL)isShowArrow bgColorString: (NSString *)bgColorString bgEdgeInsets:(UIEdgeInsets)bgEdgeInsets{
    FNFreshBrandShopHeaderCellModel *cellModel = [FNFreshBrandShopHeaderCellModel new];
    cellModel.titleString = titleString;
    cellModel.subTitleString = subTitleString;
    cellModel.rightTitleString = rightTitleString;
    cellModel.isShowArrow = isShowArrow;
    cellModel.bgColorString = bgColorString;
    cellModel.bgEdgeInsets = bgEdgeInsets;
    return cellModel;
}


@end
