//
//  FNFreshBranShopBrandCellModel.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshBrandShopPageResponseModel.h"
NS_ASSUME_NONNULL_BEGIN

/**
 1.入会入会品牌展示
 */
@interface FNFreshBranShopBrandCellModel : NSObject

@property (nonatomic,strong)FNFreshBrandShopPageJoinBrandBrandInfoResponseModel *itemModel;

@property (nonatomic,copy)NSString *brandId;

@property (nonatomic,copy)NSString *titleString;

@property (nonatomic,copy)NSString *contentString;

@property (nonatomic,copy)NSString *logoString;

@property (nonatomic,copy)NSString *bgColorString;

+ (FNFreshBranShopBrandCellModel *)cellModelForTitleString:(NSString *)titleString contentString:(NSString *)contentString logoString:(NSString *)logoString bgColorString:(NSString *)bgColorString brandId:(NSString *)brandId;


@end

/**
 2.横幅BANNER
 */
@interface FNFreshBranShopBannerCellModel : NSObject

@property (nonatomic,strong)FNFreshBrandShopPagePicInfoModel *itemModel;

@property (nonatomic,strong)NSString *imageNameString;

@property (nonatomic,assign)CGFloat scale;

+ (FNFreshBranShopBannerCellModel *)cellModelForUrlString:(NSString *)imageNameString;

@end

/**
 3.券模块
 */

typedef NS_ENUM(NSUInteger,FNFreshBranShopCouponModuleCellRightButtonStyle){
    FNFreshBranShopCouponModuleCellRightButtonStyle_canClick = 100,
    FNFreshBranShopCouponModuleCellRightButtonStyle_noClick
};

@interface FNFreshBranShopCouponModuleCellModel : NSObject

@property (nonatomic,strong)FNFreshBrandShopPageVoucherInfoModel *itemModel;
// 券图
@property (nonatomic,strong)NSString *imageNameString;
// 券标题
@property (nonatomic,strong)NSString *titleString;
// 适用范围
@property (nonatomic,strong)NSString *subTitleString;
//进度条
@property (nonatomic,assign)CGFloat progress;
//会员提示
@property (nonatomic,strong)NSString *bottomSignString;
// 默认是 6
@property (nonatomic,assign)CGFloat bottomMargin;
// 右边按钮 文案,立即领取,已领取
@property (nonatomic,strong)NSString *rightBtnTitleString;
// 右边按钮 状态
@property (nonatomic,assign)FNFreshBranShopCouponModuleCellRightButtonStyle rightButtonStyle;

+ (FNFreshBranShopCouponModuleCellModel *)cellModelForImageString:(NSString *)imageNameString  title:(NSString *)titleString subTitle: (NSString *)subTitleString progress: (CGFloat )progress bottomSignStr:(NSString *)bottomSignString bottomMargin: (CGFloat)bottomMargin;

@end

/**
 4.品牌陈列
 */
@interface FNFreshBranShopBrandListCellModel : NSObject

@property (nonatomic,strong)FNFreshBrandShopPageAllBrandInfoResponseModel *itemModel;

@property (nonatomic,strong)NSString *imageNameString;

@property (nonatomic,strong)NSString *titleString;

@property (nonatomic,strong)NSString *contentString;

@property (nonatomic,strong)NSString *bottomBtnString;

@property (nonatomic,strong)NSString *bgColorString;

+ (FNFreshBranShopBrandListCellModel *)cellModelForImageNameString:(NSString *)imageNameString titleString:(NSString *)titleString contentString:(NSString *)contentString bottomBtnString:(NSString *)bottomBtnString bgColorString:(NSString *)bgColorString;

@end

/**
 5.头部cellModel
 */
@interface FNFreshBrandShopHeaderCellModel : NSObject

/**
 标题
 */
@property (nonatomic,strong)NSString *titleString;
/**
 副标题
 */
@property (nonatomic,strong)NSString *subTitleString;
/**
 查看按钮文案 默认为空@""
 */
@property (nonatomic,strong)NSString *rightTitleString;
/**
 查看按钮图片,默认不显示 false
 */
@property (nonatomic,assign)BOOL isShowArrow;

/**
 背景颜色
 */
@property (nonatomic,strong)NSString *bgColorString;

///**
// 左边间距,默认是 0
// */
//@property (nonatomic,assign)CGFloat leftMargin;

@property (nonatomic,assign)UIEdgeInsets bgEdgeInsets;


+ (FNFreshBrandShopHeaderCellModel *)cellModelForTitle:(NSString *)titleString subTitle: (NSString *)subTitleString rightTitle: (NSString *)rightTitleString isShowArrow:(BOOL)isShowArrow bgColorString: (NSString *)bgColorString bgEdgeInsets:(UIEdgeInsets)bgEdgeInsets;

@end

NS_ASSUME_NONNULL_END
