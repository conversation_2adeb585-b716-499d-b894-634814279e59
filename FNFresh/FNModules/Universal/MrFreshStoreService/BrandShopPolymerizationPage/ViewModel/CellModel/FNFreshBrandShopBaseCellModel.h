//
//  FNFreshBrandShopBaseCellModel.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshBrandShopHeaderFile.h"
#import "FNFreshBranShopBrandCellModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface FNFreshBrandShopBaseCellModel : NSObject

@property (nonatomic,assign)FNFreshBrandShopDisplayCellType cellType;

@property (nonatomic,assign)CGFloat cellHeight;

@property (nonatomic,assign)UIEdgeInsets cellEdgeInset;

@property (nonatomic,assign)UIRectCorner corner;

@property (nonatomic,assign)CGFloat cornerRadius;

/**
 1.品牌显示
 */
@property (nonatomic,copy)NSArray<FNFreshBranShopBrandCellModel *> *brandShowCellModel;

/**
 2.横幅BANNER
 */
@property (nonatomic,strong)FNFreshBranShopBannerCellModel *bannerCellModel;

/**
 3.券模块
 */
@property (nonatomic,strong)FNFreshBranShopCouponModuleCellModel *couponModuleCellModel;

/**
 4.品牌陈列
 */
@property (nonatomic,strong)FNFreshBranShopBrandListCellModel *brandListCellModel;

/**
 5.header cellModel 数据
 */
@property (nonatomic,strong)FNFreshBrandShopHeaderCellModel *headerCellModel;



@end

NS_ASSUME_NONNULL_END
