//
//  FNFreshBrandShopPolymerizationPageViewModel.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshBaseViewModel.h"
#import "FNFreshBrandShopBaseCellModel.h"
#import "FNFreshBrandShopGroupModel.h"

NS_ASSUME_NONNULL_BEGIN

/// 获取indexPath
extern FNFreshOperationEnum const FNFreshOperationEnumGetIndexPath;
/// 打开url
extern FNFreshOperationEnum const FNFreshOperationEnumOpenUrl;
/// 打开品牌会场页面 活 商详弹框
extern FNFreshOperationEnum const FNFreshOperationEnumEnterBrandVenueOrPopPage;
/// 打开品牌会场页面
extern FNFreshOperationEnum const FNFreshOperationEnumEnterBrandVenuePage;
///  商详弹框
extern FNFreshOperationEnum const FNFreshOperationEnumEnterShoppinDetailPopPage;
///  刷新界面
extern FNFreshOperationEnum const FNFreshOperationEnumRefreshPage;


@interface FNFreshBrandShopPolymerizationPageViewModel : FNFreshBaseViewModel


#pragma mark <request>
- (void)requestData;

#pragma mark <action>
/**
 1.入会品牌展示 事件名字
 */
// 品牌区块点击
- (void)actionBrandUnitClickForObject:(id)object userInfo:(NSDictionary *)userInfo;
// 品牌区块 更多按钮点击
- (void)actionBrandHeaderBrowseClickForObject:(id)object userInfo:(NSDictionary *)userInfo;
- (void)actionBrandUnitAtOnceClickForObject:(id)object userInfo:(NSDictionary *)userInfo ;
/**
 2.横幅BANNER
 */
// banner 点击
- (void)actionBannerClickForObject:(id)object userInfo:(NSDictionary *)userInfo;
/**
 3.券模块
 */
// 立即领取 点击
- (void)actionBrandAtOnceGetClickForObject:(id)object userInfo:(NSDictionary *)userInfo;
/**
 4.品牌陈列
 */
// 立即入会/立即查看 点击
- (void)actionBrandAtOnceBrowseOrGetClickForObject:(id)object userInfo:(NSDictionary *)userInfo;

#pragma mark <datasource>
- (NSInteger)numberOfSections;
- (NSInteger)numberOfRowsInSection:(NSInteger)section;
- (FNFreshBrandShopBaseCellModel *)cellModel:(NSIndexPath *)indexPath;
- (FNFreshBrandShopGroupModel *)groupModelForSection:(NSInteger)section;

/// 背景url
@property (nonatomic,strong,readonly)NSString *headBannerUrl;


@end

NS_ASSUME_NONNULL_END
