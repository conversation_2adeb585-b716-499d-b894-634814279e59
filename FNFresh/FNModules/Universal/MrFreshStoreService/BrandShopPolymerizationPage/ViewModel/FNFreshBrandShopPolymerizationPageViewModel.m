//
//  FNFreshBrandShopPolymerizationPageViewModel.m
//  FNFresh
//
//  Created by <PERSON>D<PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopPolymerizationPageViewModel.h"
#import "FNFreshBrandShopServices.h"
#import "FNFreshBrandShopPageRequestParamsModel.h"
#import "UIResponder+FNHumanInteraction.h"
#import "FNFreshBrandShopTrackDataTool.h"
/// 获取indexPath
FNFreshOperationEnum const FNFreshOperationEnumGetIndexPath = @"FNFreshOperationEnumGetIndexPath";
FNFreshOperationEnum const FNFreshOperationEnumOpenUrl = @"FNFreshOperationEnumOpenUrl";
/// 打开品牌会场页面
FNFreshOperationEnum const FNFreshOperationEnumEnterBrandVenueOrPopPage = @"FNFreshOperationEnumEnterBrandVenueOrPopPage";
/// 打开品牌会场页面
FNFreshOperationEnum const FNFreshOperationEnumEnterBrandVenuePage = @"FNFreshOperationEnumEnterBrandVenuePage";
///  商详弹框
FNFreshOperationEnum const FNFreshOperationEnumEnterShoppinDetailPopPage = @"FNFreshOperationEnumEnterShoppinDetailPopPage";
///  刷新界面
FNFreshOperationEnum const FNFreshOperationEnumRefreshPage = @"FNFreshOperationEnumRefreshPage";

@interface FNFreshBrandShopPolymerizationPageViewModel()

@property (nonatomic,strong)NSMutableArray<FNFreshBrandShopGroupModel *> *groupModelArray;
@property (nonatomic,strong)FNFreshBrandShopPageResponseModel *responseObject;
@end

@implementation FNFreshBrandShopPolymerizationPageViewModel

#pragma mark <handle data>
- (void)handleBrandShopPageForResponse:(FNFreshBrandShopPageResponseModel *)responseObject{
    self.responseObject = responseObject;
    [self clearData];
    //1.入会品牌展示
    [self handleBrandShowData];
    //2.券模块 ,横幅BANNER
    [self handleCouponsAndBannerModuleData];
    //3.品牌陈列
    [self handleBrandListData];
    
}


- (void)handleBrandShowData {
    
    if (self.responseObject.joinBrand.joinBrandList.count <= 0) return;
    
    FNFreshBrandShopBaseCellModel *headerCellModel = [FNFreshBrandShopBaseCellModel new];
    headerCellModel.cellType = FNFreshBrandShopDisplayCellType_cellHeader;
    headerCellModel.headerCellModel = [FNFreshBrandShopHeaderCellModel cellModelForTitle:self.responseObject.joinBrand.title subTitle:@"" rightTitle:@"查看" isShowArrow:YES bgColorString:@"#A6FFFFFF" bgEdgeInsets:UIEdgeInsetsMake(12, 12, 6, 0)];
    headerCellModel.cellEdgeInset = UIEdgeInsetsMake(0, 0, 0, 0);
    headerCellModel.cornerRadius = 10;
    headerCellModel.corner = UIRectCornerTopLeft | UIRectCornerTopRight;
    
    FNFreshBrandShopGroupModel *groupHeaderModel = [self createGroupModelForType:FNFreshBrandShopDisplayGroup_header cellModelArray:@[headerCellModel]];
    [self.groupModelArray addObject:groupHeaderModel];

    FNFreshBrandShopBaseCellModel *cellModel = [FNFreshBrandShopBaseCellModel new];
    cellModel.cellType = FNFreshBrandShopDisplayCellType_brandModuleDisplay;
    cellModel.cornerRadius = 10;
    cellModel.corner = UIRectCornerBottomLeft | UIRectCornerBottomRight;
    FNFreshBrandShopGroupModel *groupModel = [self createGroupModelForType:FNFreshBrandShopDisplayGroup_brandModuleDisplay cellModelArray:@[cellModel]];
    NSMutableArray *mulArray = [NSMutableArray array];
    for (FNFreshBrandShopPageJoinBrandBrandInfoResponseModel *itemModel in self.responseObject.joinBrand.joinBrandList) {
        [mulArray addObject:[FNFreshBranShopBrandCellModel cellModelForTitleString:itemModel.name contentString:itemModel.tip logoString:itemModel.logo bgColorString:itemModel.color brandId:itemModel.brandId]];
    }
    cellModel.brandShowCellModel = mulArray;
    [self.groupModelArray addObject:groupModel];
    //添加间距组
    [self.groupModelArray addObject:[self createMarginGroup]];
}

- (void)handleCouponsAndBannerModuleData {
    
    for (FNFreshBrandShopPageModuleInfoResponseModel *item in self.responseObject.moduleList) {
        [self handleBannerDataForModel:item];
        [self handleCouponModuleDataForModel:item];
    }
}

- (void)handleCouponModuleDataForModel:(FNFreshBrandShopPageModuleInfoResponseModel *)item{
    if (item.customType != FNFreshBrandShopPageModuleInfoType_coupons) return;
    if (item.moduleTitle.length <= 0) return;
    FNFreshBrandShopBaseCellModel *headerCellModel = [FNFreshBrandShopBaseCellModel new];
    headerCellModel.cellType = FNFreshBrandShopDisplayCellType_cellHeader;
    headerCellModel.headerCellModel = [FNFreshBrandShopHeaderCellModel cellModelForTitle:item.moduleTitle subTitle:item.moduleSubTitle rightTitle:@"" isShowArrow:NO bgColorString:@"#F4E3CE" bgEdgeInsets:UIEdgeInsetsMake(0, 12, 0, 0)];
    headerCellModel.cellEdgeInset = UIEdgeInsetsMake(10, 0, 10, 0);
    headerCellModel.cornerRadius = 10;
    headerCellModel.corner = UIRectCornerTopLeft | UIRectCornerTopRight;
    FNFreshBrandShopGroupModel *groupHeaderModel = [self createGroupModelForType:FNFreshBrandShopDisplayGroup_header cellModelArray:@[headerCellModel]];
    [self.groupModelArray addObject:groupHeaderModel];
    
    NSMutableArray *cellModelArray = [NSMutableArray array];
    FNFreshBrandShopBaseCellModel *cellModel = nil;
    for (int i = 0; i < item.voucherList.count; i++) {
        FNFreshBrandShopPageVoucherInfoModel *voucherInfoModel = [item.voucherList safeObjectAtIndex:i];
        cellModel = [FNFreshBrandShopBaseCellModel new];
        cellModel.cellType = FNFreshBrandShopDisplayCellType_couponList;
        cellModel.couponModuleCellModel = [FNFreshBranShopCouponModuleCellModel cellModelForImageString:voucherInfoModel.couponImg title:voucherInfoModel.couponName subTitle:voucherInfoModel.scopeDescription progress:voucherInfoModel.progress bottomSignStr:voucherInfoModel.tip bottomMargin: i == item.voucherList.count - 1 ? 10 : 6];
        cellModel.couponModuleCellModel.itemModel = voucherInfoModel;
        [cellModelArray addObject:cellModel];
    }
    
    cellModel.cornerRadius = 10;
    cellModel.corner = UIRectCornerBottomLeft | UIRectCornerBottomRight;

    FNFreshBrandShopGroupModel *groupModel = [self createGroupModelForType:FNFreshBrandShopDisplayGroup_couponList cellModelArray:cellModelArray];
    [self.groupModelArray addObject:groupModel];
    
    //添加间距组
    [self.groupModelArray addObject:[self createMarginGroup]];
}


- (void)handleBannerDataForModel:(FNFreshBrandShopPageModuleInfoResponseModel *)item {
    if (item.customType != FNFreshBrandShopPageModuleInfoType_banner) return;
    if  (item.picList.count <= 0) return;
    FNFreshBrandShopBaseCellModel *cellModel = [FNFreshBrandShopBaseCellModel new];
    cellModel.cellType = FNFreshBrandShopDisplayCellType_banner;
    cellModel.bannerCellModel = [FNFreshBranShopBannerCellModel cellModelForUrlString:item.picList.firstObject.imgUrl];
    cellModel.bannerCellModel.itemModel = item.picList.firstObject;
    FNFreshBrandShopGroupModel *groupModel = [self createGroupModelForType:FNFreshBrandShopDisplayGroup_banner cellModelArray:@[cellModel]];
    cellModel.cornerRadius = 10;
    cellModel.corner = UIRectCornerAllCorners;

    [self.groupModelArray addObject:groupModel];
    
    //添加间距组
    [self.groupModelArray addObject:[self createMarginGroup]];
}
- (void)handleBrandListData {
    
    if (self.responseObject.allBrand.allBrandList.count <= 0) return;
    if (self.responseObject.allBrand.title.length <= 0) return;
    NSArray<FNFreshBrandShopPageAllBrandInfoResponseModel *> *allBrandList = self.responseObject.allBrand.allBrandList;
    FNFreshBrandShopBaseCellModel *headerCellModel = [FNFreshBrandShopBaseCellModel new];
    headerCellModel.cellType = FNFreshBrandShopDisplayCellType_cellHeader;
    headerCellModel.headerCellModel = [FNFreshBrandShopHeaderCellModel cellModelForTitle:self.responseObject.allBrand.title subTitle:self.responseObject.allBrand.subTitle rightTitle:@"" isShowArrow:NO bgColorString:@"#00000000" bgEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 0)];
    headerCellModel.cellEdgeInset = UIEdgeInsetsMake(2, 0, 8, 0);
    FNFreshBrandShopGroupModel *groupHeaderModel = [self createGroupModelForType:FNFreshBrandShopDisplayGroup_header cellModelArray:@[headerCellModel]];
    [self.groupModelArray addObject:groupHeaderModel];
    
    
    NSMutableArray *mulCellModelArray = [NSMutableArray array];
    FNFreshBrandShopBaseCellModel *cellModel = nil;
    for (int i = 0; i < allBrandList.count; i++) {
        FNFreshBrandShopPageAllBrandInfoResponseModel *brandListItem = allBrandList[i];
        cellModel = [FNFreshBrandShopBaseCellModel new];
        cellModel.cellType = FNFreshBrandShopDisplayCellType_brandList;
        cellModel.cornerRadius = 10;
        cellModel.corner = UIRectCornerAllCorners;
        cellModel.brandListCellModel = [FNFreshBranShopBrandListCellModel cellModelForImageNameString:brandListItem.logo titleString:brandListItem.name contentString:brandListItem.tip bottomBtnString:brandListItem.statusDescString bgColorString:brandListItem.color];
        cellModel.brandListCellModel.itemModel = brandListItem;
        [mulCellModelArray addObject:cellModel];
    }
    FNFreshBrandShopGroupModel *groupModel = [self createGroupModelForType:FNFreshBrandShopDisplayGroup_brandList cellModelArray:mulCellModelArray];
    [self.groupModelArray addObject:groupModel];
    
    //添加间距组
    [self.groupModelArray addObject:[self createMarginGroup]];
}

- (void)clearData{
    [self.groupModelArray removeAllObjects];
}

- (void)handleAtOnceInitiationForModel:(FNFreshBrandShopPageAcOnceGetResponseModel *)responseModel itemModel:(FNFreshBrandShopPageVoucherInfoModel *)itemModel {
    if (responseModel){
        NSString *colPositionString = responseModel.isJoin == 0 ? @"1" : @"2";
        [FNFreshBrandShopTrackDataTool eventTrackFor:FNFreshBrandShopTrackType_clickCouponsModule info:@{
            FNFreshBuryParamsColPositionKeyValue:colPositionString
        }];
        
        //这里我需要刷新界面更具state
        itemModel.statue = responseModel.status == 0 ? 1 : 2;
        if (itemModel.statue == 2) {
            self.operationAction(FNFreshOperationEnumRefreshPage, nil);
            return;
        }
        
        if (responseModel.isJoin == 0) {
            //没有入会
            if (self.operationAction){
                self.operationAction(FNFreshOperationEnumEnterShoppinDetailPopPage, itemModel);
            }
        }else {
            //入会成功
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self requestData];
            });
        }
    }
}



#pragma mark <Deal func>

- (FNFreshBrandShopGroupModel *)createGroupModelForType:(FNFreshBrandShopDisplayGroup)groupType cellModelArray:(NSArray *)cellModelArray {
    FNFreshBrandShopGroupModel *groupModel = [FNFreshBrandShopGroupModel new];
    groupModel.groupType = groupType;
    groupModel.cellModelArray = cellModelArray;
    return groupModel;
}

- (FNFreshBrandShopGroupModel *)createMarginGroup {
    FNFreshBrandShopBaseCellModel *cellModel = [FNFreshBrandShopBaseCellModel new];
    cellModel.cellType = FNFreshBrandShopDisplayCellType_marginCell;
    cellModel.cellHeight= 12;
    FNFreshBrandShopGroupModel *groupModel = [self createGroupModelForType:FNFreshBrandShopDisplayGroup_marginEmpty cellModelArray:@[cellModel]];
    return groupModel;
}

#pragma mark <request>
- (void)requestData{
    FNFreshBrandShopPageRequestParamsModel *paramsModel = [FNFreshBrandShopPageRequestParamsModel new];
    paramsModel.storeId = FNFreshUser.shareInstance.shopId;
    [self triggerLoading:YES];
    [FNFreshBrandShopServices requestBrandShopPageDataWithParameter:paramsModel success:^(FNFreshBrandShopPageResponseModel *responseObject, BOOL isCache) {
        [self handleBrandShopPageForResponse:responseObject];
        [self triggerUpdateForSuccess:YES code:responseObject.errorCode errorDescrption:responseObject.errorDesc];
        [self triggerLoading:NO];
    } failure:^(FNFreshBaseResponseModel *responseObject, NSError *error) {
        [self triggerUpdateForSuccess:NO code:responseObject.errorCode errorDescrption:responseObject.errorDesc];
        [self triggerLoading:NO];
        [self triggerToast:responseObject.errorDesc];
    }];
}

- (void)requestAtOnceInitiationForParamsModel:(FNFreshBrandShopPageAcOnceGetRequestParamsModel *)paramsModel itemModel:(FNFreshBrandShopPageVoucherInfoModel *)itemModel {
    [self triggerLoading:YES];
    [FNFreshBrandShopServices requestAtOnceGetWithParameter:paramsModel success:^(FNFreshBrandShopPageAcOnceGetResponseModel *responseObject, BOOL isCache) {
        [self handleAtOnceInitiationForModel:responseObject itemModel:itemModel];
        [self triggerLoading:NO];
        [self triggerToast:responseObject.errorDesc];
    } failure:^(FNFreshBrandShopPageAcOnceGetResponseModel *responseObject, NSError *error) {
        [self handleAtOnceInitiationForModel:responseObject itemModel:itemModel];
        [self triggerUpdateForSuccess:NO code:responseObject.errorCode errorDescrption:responseObject.errorDesc];
        [self triggerLoading:NO];
        [self triggerToast:responseObject.errorDesc];
    }];
    
}



#pragma mark <action>
/**
 1.入会品牌展示 事件名字
 */
// 品牌区块点击
- (void)actionBrandUnitClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSLog(@"品牌区块点击");
}

// 品牌区块头部 查看按钮点击
- (void)actionBrandHeaderBrowseClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSLog(@"品牌区块头部 查看按钮点击");
}
- (void)actionBrandUnitAtOnceClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSLog(@"品牌区块 每个按钮,立即查看点击");
    if (!userInfo) return;
    NSInteger index = [userInfo[[FNFreshUserInfoConstKey index]] intValue];
    FNFreshBrandShopPageJoinBrandBrandInfoResponseModel *itemModel = [self.responseObject.joinBrand.joinBrandList safeObjectAtIndex:index];
    if (self.operationAction){
        self.operationAction(FNFreshOperationEnumEnterBrandVenuePage, itemModel);
    }
    
    [FNFreshBrandShopTrackDataTool eventTrackFor:FNFreshBrandShopTrackType_clickBrandVenueModule info:@{
        FNFreshBuryParamsColPositionKeyValue:itemModel.name
    }];

}
/**
 2.横幅BANNER
 */
// banner 点击
- (void)actionBannerClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSLog(@"banner 点击");
    NSIndexPath *indexPath = self.operationReturnAction(FNFreshOperationEnumGetIndexPath,object);
    FNFreshBrandShopBaseCellModel *cellModel = [self cellModel:indexPath];
    FNFreshBrandShopPagePicInfoModel *itemModel = cellModel.bannerCellModel.itemModel;
    if (self.operationAction){
        self.operationAction(FNFreshOperationEnumOpenUrl, itemModel.linkUrl);
    }
    
    [FNFreshBrandShopTrackDataTool eventTrackFor:FNFreshBrandShopTrackType_clickBannerModule info:nil];
}
/**
 3.券模块
 */
// 立即领取 点击
- (void)actionBrandAtOnceGetClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSLog(@"立即领取 点击");
    NSIndexPath *indexPath = self.operationReturnAction(FNFreshOperationEnumGetIndexPath,object);
    FNFreshBrandShopBaseCellModel *cellModel = [self cellModel:indexPath];
    FNFreshBrandShopPageVoucherInfoModel *itemModel = cellModel.couponModuleCellModel.itemModel;
    //按钮修改后不能够点击,我们就需要返回
    if (cellModel.couponModuleCellModel.rightButtonStyle == FNFreshBranShopCouponModuleCellRightButtonStyle_noClick) {
        return;
    }
    FNFreshBrandShopPageAcOnceGetRequestParamsModel *paramsModel = [FNFreshBrandShopPageAcOnceGetRequestParamsModel new];
    paramsModel.brandId = itemModel.brandId;
    paramsModel.couponId = itemModel.couponId;
    paramsModel.storeId = [FNFreshUser shareInstance].shopId;
    [self requestAtOnceInitiationForParamsModel:paramsModel itemModel:itemModel];

}
/**
 4.品牌陈列
 */
// 立即入会/立即查看 点击
- (void)actionBrandAtOnceBrowseOrGetClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSLog(@"立即入会/立即查看 点击");
    NSIndexPath *indexPath = self.operationReturnAction(FNFreshOperationEnumGetIndexPath,object);
    FNFreshBrandShopBaseCellModel *cellModel = [self cellModel:indexPath];
    FNFreshBrandShopPageAllBrandInfoResponseModel *itemModel = cellModel.brandListCellModel.itemModel;
    if (self.operationAction){
        self.operationAction(FNFreshOperationEnumEnterBrandVenueOrPopPage, itemModel);
    }
    
}

#pragma mark <datasource>

- (NSInteger)numberOfSections{
    return self.groupModelArray.count;
}

- (NSInteger)numberOfRowsInSection:(NSInteger)section{
    FNFreshBrandShopGroupModel *groupModel = [self.groupModelArray safeObjectAtIndex:section];
    return groupModel.cellModelArray.count;
}

- (FNFreshBrandShopBaseCellModel *)cellModel:(NSIndexPath *)indexPath{
    FNFreshBrandShopGroupModel *groupModel = [self.groupModelArray safeObjectAtIndex:indexPath.section];
    return [groupModel.cellModelArray safeObjectAtIndex:indexPath.row];
}

- (FNFreshBrandShopGroupModel *)groupModelForSection:(NSInteger)section{
    return [self.groupModelArray safeObjectAtIndex:section];
}

- (NSString *)headBannerUrl{
    return self.responseObject.headBannerUrl ? self.responseObject.headBannerUrl : @"";
}


#pragma mark <lazy>
- (NSMutableArray<FNFreshBrandShopGroupModel *> *)groupModelArray{
    if (!_groupModelArray){
        _groupModelArray = [NSMutableArray array];
    }
    return _groupModelArray;
}

@end
