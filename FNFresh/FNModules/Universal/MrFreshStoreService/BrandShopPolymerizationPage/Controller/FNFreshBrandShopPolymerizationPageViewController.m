//
//  FNFreshBrandShopPolymerizationPageViewController.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopPolymerizationPageViewController.h"
#import "FNFreshBrandShopShowCell.h"
#import "FNFreshBrandShopBannerCell.h"
#import "FNFreshBrandShopBrandListCell.h"
#import "FNFreshBrandShopCouponModuleListCel.h"
#import "FNFreshBrandShopPolymerizationPageViewModel.h"
#import "UIViewController+FNNavigationBarHidden.h"
#import "UIDevice+FNScreenSize.h"
#import "UIViewController+FNFreshEmptyDataSet.h"
#import "FNFreshBrandShopHeaderCell.h"
#import "FNFreshBrandShopNavView.h"
#import "SDAnimatedImageView+WebCache.h"
#import "FNFreshMemberCardContainerVC.h"
#import "FNFreshTabBarController.h"
#import "FNFreshUrlRouter.h"
#import "FNMediator+FNBrandMemberModule.h"
#import "FNFreshBrandShopTrackDataTool.h"
#import "UIViewController+FNFreshEmptyDataSet.h"
#import "FNBrandMemberWidget.h"
#import "FNFreshBrandShopPopInitiationManager.h"
#import "UIDevice+FNScreenSize.h"
#import "FNFreshImageLabelStyleView.h"
@interface FNFreshBrandShopPolymerizationPageViewController ()<UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout,FNFreshBrandShopNavViewDelegate,FNHumanInteractionTransition>
@property (nonatomic,strong)SDAnimatedImageView *bgImageView;
@property (nonatomic,strong)FNFreshGradationView *gradientBackgroundView;
@property (nonatomic,strong)FNFreshBrandShopNavView *navView;
@property (nonatomic,strong)UICollectionView *collectionView;
@property (nonatomic,strong)UICollectionViewFlowLayout *layout;

@property (nonatomic,strong)FNFreshBrandShopPolymerizationPageViewModel *viewModel;

@property (nonatomic,strong)FNFreshBrandShopPopInitiationManager *popInitiationManager;

@property (nonatomic,strong)FNFreshImageLabelStyleView *backButton;

@end

@implementation FNFreshBrandShopPolymerizationPageViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self addChildView];
    [self initData];
    [self setBindEvent];
    [self loadData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.fnPreferNavigationBarHidden = YES;
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    self.fnPreferNavigationBarHidden = YES;
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

#pragma mark -<request>
- (void)loadData {
    [self.viewModel requestData];
}


#pragma mark -<process>

- (void)initData {
    [FNFreshBrandShopTrackDataTool eventTrackFor:FNFreshBrandShopTrackType_showBrandShopPage info:nil];
}

- (void)addChildView {
    self.view.backgroundColor = [UIColor fn_colorWithHex:@"#FFFFFF"];
    
    [self.view addSubview:self.bgImageView];
    
    [self.view addSubview:self.gradientBackgroundView];
    [self.gradientBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(self.view);
        make.height.mas_equalTo(80);
        make.top.mas_equalTo(self.view.mas_top).mas_offset(220);
    }];
    
    [self.bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.mas_equalTo(self.view);
        make.bottom.mas_equalTo(self.gradientBackgroundView);
    }];
    
    [self.view addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(self.view);
    }];
    CGFloat navHeight = [UIDevice fnNavigationFullHeight];

    [self.view addSubview:self.backButton];
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.view.mas_leading).mas_offset(12);
        make.top.mas_equalTo(self.view.mas_top).mas_offset(51);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
    
    [self.view addSubview:self.navView];
    [self.navView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.mas_equalTo(self.view);
        make.height.mas_equalTo(80);
    }];
}

#pragma mark -<setBindEvent>
- (void)setBindEvent {
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(actionFNNotificationName_RefreshBrandJoin:) name:FNNotificationName_RefreshBrandJoin object:nil];
    
    __weak typeof(self) weakSelf = self;
    self.viewModel.triggerLoading = ^(BOOL isLoading) {
        isLoading ? [weakSelf startProgress] : [weakSelf stopProgress];
    };
    
    self.viewModel.triggerToast = ^(NSString * _Nonnull msg) {
        [weakSelf startProgressText: msg];
    };
    
    self.viewModel.triggerUpdate = ^(BOOL success, NSNumber *code, NSString * _Nonnull errorDescrption) {
        if (success){
            [weakSelf refreshPage];
        } else {
            // 这里需要显示网络错误页面
            [weakSelf fnFresh_addNetworkErrorEmptyDataSetWithTarget:weakSelf.collectionView errorCode:code refreshEvent:^{
                [weakSelf loadData];
            }];
        }
    };
    
    self.viewModel.operationAction = ^(FNFreshOperationEnum  _Nonnull operationType, id data) {
        [weakSelf actionOperationForEventName:operationType data:data];
    };
    
    self.viewModel.operationReturnAction = ^id _Nullable(FNFreshOperationEnum  _Nonnull operationType, id  _Nullable data) {
        if ([operationType isEqualToString:FNFreshOperationEnumGetIndexPath]){
            return [weakSelf.collectionView indexPathForCell:data];
        }
        return nil;
    };
}

#pragma mark notification

- (void)actionFNNotificationName_RefreshBrandJoin:(NSNotification *)notification {
    [self loadData];
}

#pragma mark -<refresh>
- (void)refreshPage {
    if ([self.viewModel numberOfSections]){
        [self updateBgImageView];
        [self.collectionView reloadData];
    } else {
        [self fnFresh_addEmptyDataSetWithTarget:self.collectionView image:[UIImage fnFresh_imageNamed:@"pic_empty_5"] title:@"这里什么也没有~" buttonTitle:nil buttonEvent:nil];
    }
}

- (void)updateBgImageView {
    [self.bgImageView sd_setImageWithURL:[NSURL URLWithString:[self.viewModel headBannerUrl]] placeholderImage:[UIImage imageNamed:@"store_service_brandShop_bgView"]];
}

#pragma mark -<UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return [self.viewModel numberOfSections];
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    return [self.viewModel numberOfRowsInSection:section];
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath{
    
    FNFreshBrandShopBaseCellModel *cellModel = [self.viewModel cellModel:indexPath];
    
    switch (cellModel.cellType) {
            
        case FNFreshBrandShopDisplayCellType_marginCell:
        {
            FNFreshBaseMarginCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshMarginEmptyCollectionViewCell identifierDescString] forIndexPath:indexPath];
            return cell;
        }
        case FNFreshBrandShopDisplayCellType_cellHeader:
        {
            FNFreshBrandShopHeaderCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshBrandShopHeaderCell identifierDescString] forIndexPath:indexPath];
            cell.titleString = cellModel.headerCellModel.titleString;
            cell.subTitleString = cellModel.headerCellModel.subTitleString;
            cell.rightTitleString = cellModel.headerCellModel.rightTitleString;
            cell.isShowArrow = cellModel.headerCellModel.isShowArrow;
            cell.bgColorString = cellModel.headerCellModel.bgColorString;
            cell.bgEdgeInsets = cellModel.headerCellModel.bgEdgeInsets;
            cell.cornerRadius = cellModel.cornerRadius;
            cell.corner = cellModel.corner;
            return cell;            
        }
            
        case FNFreshBrandShopDisplayCellType_brandModuleDisplay:
            {
                FNFreshBrandShopShowCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshBrandShopShowCell identifierDescString] forIndexPath:indexPath];
                cell.cornerRadius = cellModel.cornerRadius;
                cell.corner = cellModel.corner;
                cell.dataModel = cellModel.brandShowCellModel;
                return cell;
            }
        case FNFreshBrandShopDisplayCellType_banner:
            {
                FNFreshBrandShopBannerCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshBrandShopBannerCell identifierDescString] forIndexPath:indexPath];
                cell.cornerRadius = cellModel.cornerRadius;
                cell.corner = cellModel.corner;
                cell.dataModel = cellModel.bannerCellModel;
                return cell;
            }
        case FNFreshBrandShopDisplayCellType_couponList:
            {
                FNFreshBrandShopCouponModuleListCel *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshBrandShopCouponModuleListCel identifierDescString] forIndexPath:indexPath];
                cell.dataModel = cellModel.couponModuleCellModel;
                cell.cornerRadius = cellModel.cornerRadius;
                cell.corner = cellModel.corner;
                return cell;
            }
        case FNFreshBrandShopDisplayCellType_brandList:
            {
                FNFreshBrandShopBrandListCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshBrandShopBrandListCell identifierDescString] forIndexPath:indexPath];
                cell.cornerRadius = cellModel.cornerRadius;
                cell.corner = cellModel.corner;
                cell.dataModel = cellModel.brandListCellModel;
                return cell;
            }
            
        default:
            break;
    }
    
    UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[UICollectionViewCell identifierDescString] forIndexPath:indexPath];
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    
    FNFreshBrandShopBaseCellModel *cellModel = [self.viewModel cellModel:indexPath];
    if (cellModel.cellHeight){
        return CGSizeMake(self.view.bounds.size.width - 24, cellModel.cellHeight);
    }
    UICollectionViewCell *calCell = nil;
    switch (cellModel.cellType) {
        case FNFreshBrandShopDisplayCellType_marginCell:
        {
            FNFreshBaseMarginCollectionViewCell *cell = [FNFreshMarginEmptyCollectionViewCell new];
            calCell = cell;
            break;
        }
        case FNFreshBrandShopDisplayCellType_cellHeader:
        {
            FNFreshBrandShopHeaderCell *cell = [FNFreshBrandShopHeaderCell new];
            cell.titleString = cellModel.headerCellModel.titleString;
            cell.subTitleString = cellModel.headerCellModel.subTitleString;
            cell.rightTitleString = cellModel.headerCellModel.rightTitleString;
            cell.isShowArrow = cellModel.headerCellModel.isShowArrow;
            cell.bgColorString = cellModel.headerCellModel.bgColorString;
            cell.edgeInset = cellModel.cellEdgeInset;
            cell.bgEdgeInsets = cellModel.headerCellModel.bgEdgeInsets;
            calCell = cell;
            break;
        }
            
        case FNFreshBrandShopDisplayCellType_brandModuleDisplay:
            {
                FNFreshBrandShopShowCell *cell = [FNFreshBrandShopShowCell new];
                calCell = cell;
                break;
            }
        case FNFreshBrandShopDisplayCellType_banner:
            {
                FNFreshBrandShopBannerCell *cell = [FNFreshBrandShopBannerCell new];
                calCell = cell;
                cell.dataModel = cellModel.bannerCellModel;
                break;
            }
        case FNFreshBrandShopDisplayCellType_couponList:
            {
                FNFreshBrandShopCouponModuleListCel *cell = [FNFreshBrandShopCouponModuleListCel new];
                calCell = cell;
                cell.dataModel = cellModel.couponModuleCellModel;
                break;
            }
        case FNFreshBrandShopDisplayCellType_brandList:
            {
                FNFreshBrandShopBrandListCell *cell = [FNFreshBrandShopBrandListCell new];
                calCell = cell;
                return CGSizeMake((self.view.bounds.size.width - 36 ) * 0.5, 126);
                break;
            }
        default:
            break;
    }
    CGSize fitSize = [calCell.contentView systemLayoutSizeFittingSize:CGSizeMake(self.view.bounds.size.width - 24, 30) withHorizontalFittingPriority:UILayoutPriorityRequired verticalFittingPriority:UILayoutPriorityFittingSizeLevel];
    cellModel.cellHeight = fitSize.height;
    return CGSizeMake(self.view.bounds.size.width - 24, fitSize.height);
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    // 设置 cell 之间的最小间距
    FNFreshBrandShopGroupModel *groupModel = [self.viewModel groupModelForSection:section];
    switch (groupModel.groupType) {
        case FNFreshBrandShopDisplayGroup_brandList:
            return 9;
        case FNFreshBrandShopDisplayGroup_brandModuleDisplay:
        case FNFreshBrandShopDisplayGroup_banner:
        case FNFreshBrandShopDisplayGroup_couponList:
            return 0;
        default:
            return 0;
    }
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    FNFreshBrandShopGroupModel *groupModel = [self.viewModel groupModelForSection:section];
    switch (groupModel.groupType) {
        case FNFreshBrandShopDisplayGroup_brandList:
            return 10;
        case FNFreshBrandShopDisplayGroup_brandModuleDisplay:
        case FNFreshBrandShopDisplayGroup_banner:
        case FNFreshBrandShopDisplayGroup_couponList:
            return 0;
        default:
            return 0;
    }
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0, 12, 0, 12);
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath{
    
    FNFreshBrandShopBaseCellModel *cellModel = [self.viewModel cellModel:indexPath];
    switch (cellModel.cellType) {
            
        case FNFreshBrandShopDisplayCellType_brandModuleDisplay:
            {
                [FNFreshBrandShopTrackDataTool eventTrackFor:FNFreshBrandShopTrackType_showBrandVenueModule info:nil];
                return;
            }
        case FNFreshBrandShopDisplayCellType_banner:
            {
                [FNFreshBrandShopTrackDataTool eventTrackFor:FNFreshBrandShopTrackType_showBannerModule info:nil];
                return;
            }
        case FNFreshBrandShopDisplayCellType_couponList:
            {
                [FNFreshBrandShopTrackDataTool eventTrackFor:FNFreshBrandShopTrackType_showCouponsModule info:nil];

                return;
            }
        case FNFreshBrandShopDisplayCellType_brandList:
            {
                return;
            }
            
        default:
            break;
    }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView{
    CGFloat offsetY = scrollView.contentOffset.y + 114;
    NSLog(@"offsetY:%f",offsetY);
    CGFloat navHeight = 80;
    if (offsetY > 0){
        CGFloat alpha = ABS(offsetY) * 1.0 / navHeight;
        if(alpha > 1){
            alpha = 1;
        }
        NSLog(@"offsetA:%f",alpha);
        self.navView.alpha = alpha;
    } else {
        self.navView.alpha = 0;
    }
}

#pragma mark -<FNFreshBrandShopNavView>

- (void)backClick{
    [super fn_backButtonClicked:nil];
}


#pragma mark -<lazy>

- (SDAnimatedImageView *)bgImageView{
    if (!_bgImageView){
        _bgImageView = [SDAnimatedImageView new];
        _bgImageView.alpha = 0.6;
        _bgImageView.contentMode = UIViewContentModeScaleAspectFill;
        _bgImageView.clipsToBounds = YES;
    }
    return _bgImageView;
}

- (FNFreshGradationView *)gradientBackgroundView{
    if (!_gradientBackgroundView){
        _gradientBackgroundView = [[FNFreshGradationView alloc] initWithFrame:CGRectZero];
        _gradientBackgroundView.hidden = NO;
        _gradientBackgroundView.userInteractionEnabled = NO;
        _gradientBackgroundView.attributeGradientDataModel = [FNFreshGradientViewDataModel gradientDataModelForStartPoint:CGPointMake(0.5, 0) endPoint:CGPointMake(0.5, 1) location: @[@(0), @(1.0f)] colorStrings:@[@"#00FFFFFF",@"#FFFFFFFF"]];
    }
    return _gradientBackgroundView;
}


- (FNFreshBrandShopNavView *)navView{
    if (!_navView){
        _navView = [FNFreshBrandShopNavView new];
        _navView.backgroundColor = [UIColor whiteColor];
        _navView.alpha = 0;
        _navView.delegate = self;
    }
    return _navView;
}
- (FNFreshImageLabelStyleView *)backButton{
    if (!_backButton){
        _backButton = [FNFreshImageLabelStyleView new];//,
        _backButton.style = FNFreshImageLabelStyleOnlyImage;
        [_backButton setImage:[UIImage fnFresh_imageNamed:@"brand_navbar_back_white"] forState:UIControlStateNormal];
        _backButton.frame = CGRectMake(0, 0, 30, 30);
        _backButton.exclusiveTouch = YES;
        [_backButton addTarget:self action:@selector(backClick) forControlEvents:UIControlEventTouchUpInside];
    }
    return _backButton;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:self.layout];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.scrollEnabled = YES;
        _collectionView.bounces = YES;
        _collectionView.alwaysBounceVertical = YES;
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.contentInset = UIEdgeInsetsMake(114, 0, 8, 0);
        _collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        [_collectionView registerClass:[UICollectionViewCell class]
            forCellWithReuseIdentifier:[UICollectionViewCell identifierDescString]];
        [_collectionView registerClass:[FNFreshMarginEmptyCollectionViewCell class] forCellWithReuseIdentifier:[FNFreshMarginEmptyCollectionViewCell identifierDescString]];
        [_collectionView registerClass:[FNFreshBrandShopShowCell class] forCellWithReuseIdentifier:[FNFreshBrandShopShowCell identifierDescString]];
        [_collectionView registerClass:[FNFreshBrandShopBannerCell class] forCellWithReuseIdentifier:[FNFreshBrandShopBannerCell identifierDescString]];
        [_collectionView registerClass:[FNFreshBrandShopCouponModuleListCel class] forCellWithReuseIdentifier:[FNFreshBrandShopCouponModuleListCel identifierDescString]];
        [_collectionView registerClass:[FNFreshBrandShopBrandListCell class] forCellWithReuseIdentifier:[FNFreshBrandShopBrandListCell identifierDescString]];
        [_collectionView registerClass:[FNFreshBrandShopHeaderCell class] forCellWithReuseIdentifier:[FNFreshBrandShopHeaderCell identifierDescString]];
    }
    return _collectionView;
}

- (UICollectionViewFlowLayout *)layout {
    if (!_layout) {
        _layout = [UICollectionViewFlowLayout new];
        _layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    }
    return _layout;
}

- (FNFreshBrandShopPolymerizationPageViewModel *)viewModel{
    if (!_viewModel){
        _viewModel = [FNFreshBrandShopPolymerizationPageViewModel new];
    }
    return _viewModel;
}

- (FNFreshBrandShopPopInitiationManager *)popInitiationManager{
    if (!_popInitiationManager){
        _popInitiationManager = [FNFreshBrandShopPopInitiationManager new];
    }
    return _popInitiationManager;
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self name:FNNotificationName_RefreshBrandJoin object:nil];
}

#pragma mark -<action>
- (void)actionOperationForEventName:(FNFreshOperationEnum _Nonnull)operation data:(id)data {
    if ([operation isEqualToString:FNFreshOperationEnumOpenUrl]){
        NSString *url = data;
        [[FNFreshUrlRouter new] jumpControllerWithRemoteURLString:url completion:nil];
    }
    
    else if ([operation isEqualToString:FNFreshOperationEnumEnterBrandVenueOrPopPage]){
        FNFreshBrandShopPageAllBrandInfoResponseModel *itemModel = data;
        if (itemModel.status == 0){
            //未入会
            [self.popInitiationManager brandMemberAlertControllerWithBrandId:itemModel.brandId IsBrandPage:@"0" alertAppearClosure:nil detailClosure:nil joinClosure:nil closeClosure:nil joinSuccessClosure:nil showAgain:@"0" buryingPointObject:nil];
            
        } else if (itemModel.status == 1){
            //已入会  _Nullable
            [[FNMediator sharedInstance] brandMemberInitWithBrandId:itemModel.brandId promptSwitch:NO inLoginedState:^(UIViewController * _Nonnull vc) {
                [FNFreshTabBarController pushViewController: vc animated:YES];
            }];
        }
    }
    else if ([operation isEqualToString:FNFreshOperationEnumEnterBrandVenuePage]){
        FNFreshBrandShopPageJoinBrandBrandInfoResponseModel *itemModel = data;
        [[FNMediator sharedInstance] brandMemberInitWithBrandId:itemModel.brandId promptSwitch:NO inLoginedState:^(UIViewController * _Nonnull vc) {
            [FNFreshTabBarController pushViewController: vc animated:YES];
        }];

    }
    else if ([operation isEqualToString:FNFreshOperationEnumEnterShoppinDetailPopPage]){
        FNFreshBrandShopPageVoucherInfoModel *itemModel = data;
        [self.popInitiationManager brandMemberAlertControllerWithBrandId:itemModel.brandId IsBrandPage:@"0" alertAppearClosure:nil detailClosure:nil joinClosure:nil closeClosure:nil joinSuccessClosure:nil showAgain:@"0" buryingPointObject:nil];
    }
    
    else if ([operation isEqualToString:FNFreshOperationEnumRefreshPage]){
        [self refreshPage];
    }
    
    
    
    
}

#pragma mark -<FNHumanInteractionTransition>

- (void)humanInteractionTransition:(NSString *)name object:(id)object userInfo:(NSDictionary *)userInfo{

    if ([name isEqualToString:FNFreshBrandShopEventNameEnumBrandUnitClickValue]){
        [self actionBrandUnitClickForObject:object userInfo:userInfo];
    }
    
    else if ([name isEqualToString:FNFreshBrandShopEventNameEnumBrandHeaderBrowseClickValue]){
        [self actionBrandUnitMoreClickForObject:object userInfo:userInfo];
    }
    else if ([name isEqualToString:FNFreshBrandShopEventNameEnumBrandUnitAtOnceBrowseClickValue]){
        [self actionBrandUnitAtOnceClickForObject:object userInfo:userInfo];
    }
    else if ([name isEqualToString:FNFreshBrandShopEventNameEnumBannerClickValue]){
        [self actionBannerClickForObject:object userInfo:userInfo];
    }
    else if ([name isEqualToString:FNFreshBrandShopEventNameEnumAtOnceGetClickValue]){
        [self actionBrandAtOnceGetClickForObject:object userInfo:userInfo];
    }
    else if ([name isEqualToString:FNFreshBrandShopEventNameEnumAtOnceBrowseOrGetClickValue]){
        [self actionBrandAtOnceBrowseOrGetClickForObject:object userInfo:userInfo];
    }

}

/**
 1.入会品牌展示 事件名字
 */
// 品牌区块点击
- (void)actionBrandUnitClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    [self.viewModel actionBrandUnitClickForObject:object userInfo:userInfo];
}

// 品牌区块 更多按钮点击
- (void)actionBrandUnitMoreClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    [self.viewModel actionBrandHeaderBrowseClickForObject:object userInfo:userInfo];
    FNFreshMemberCardContainerVC *vc = [[FNFreshMemberCardContainerVC alloc] initWithBarCodeVcWithEnterType:0 memCardVCType:FNBrandMemberCardVCType cardId: @""];
    [FNFreshTabBarController pushViewController:vc animated:YES];
}

- (void)actionBrandUnitAtOnceClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    [self.viewModel actionBrandUnitAtOnceClickForObject:object userInfo:userInfo];
}
/**
 2.横幅BANNER
 */
// banner 点击
- (void)actionBannerClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    [self.viewModel actionBannerClickForObject:object userInfo:userInfo];
}
/**
 3.券模块
 */
// 立即领取 点击
- (void)actionBrandAtOnceGetClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    [self.viewModel actionBrandAtOnceGetClickForObject:object userInfo:userInfo];
}
/**
 4.品牌陈列
 */
// 立即入会/立即查看 点击
- (void)actionBrandAtOnceBrowseOrGetClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    [self.viewModel actionBrandAtOnceBrowseOrGetClickForObject:object userInfo:userInfo];
}

@end
