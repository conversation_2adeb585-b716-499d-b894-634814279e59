//
//  FNFreshBrandShopPageRequestParamsModel.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopPageRequestParamsModel.h"
/// 主页面请求
@implementation FNFreshBrandShopPageRequestParamsModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"storeId":@"body.storeId"
    };
}

@end

/// 立即领取请求
@implementation FNFreshBrandShopPageAcOnceGetRequestParamsModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"brandId":@"body.brandId",
        @"couponId":@"body.couponId",
        @"storeId":@"body.storeId"
    };
}

@end
