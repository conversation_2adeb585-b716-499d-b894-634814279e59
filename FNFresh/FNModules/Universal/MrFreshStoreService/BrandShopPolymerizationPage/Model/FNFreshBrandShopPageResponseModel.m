//
//  FNFreshBrandShopPageResponseModel.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBrandShopPageResponseModel.h"

/// 主页面请求
@implementation FNFreshBrandShopPageResponseModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"headBannerUrl":@"body.headBannerUrl",
        @"joinBrand":@"body.joinBrand",
        @"allBrand":@"body.allBrand",
        @"moduleList":@"body.moduleList"
    };
}

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"joinBrand": [FNFreshBrandShopPageJoinBrandResponseModel class],
        @"allBrand":[FNFreshBrandShopPageAllBrandResponseModel class],
        @"moduleList":[FNFreshBrandShopPageModuleInfoResponseModel class]
    };
}

@end

@implementation FNFreshBrandShopPageJoinBrandResponseModel

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"joinBrandList": [FNFreshBrandShopPageJoinBrandBrandInfoResponseModel class]
    };
}

@end

@implementation FNFreshBrandShopPageJoinBrandBrandInfoResponseModel

@end

@implementation FNFreshBrandShopPageAllBrandResponseModel

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"allBrandList": [FNFreshBrandShopPageAllBrandInfoResponseModel class]
    };
}



@end

@implementation FNFreshBrandShopPageAllBrandInfoResponseModel

- (NSString *)statusDescString{
    if (self.status == 0){
        return @"立即入会";
    } else if (self.status == 1){
        return @"立即查看";
    }
    return @"";
    
}

@end

@implementation FNFreshBrandShopPageModuleInfoResponseModel

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"picList": [FNFreshBrandShopPagePicInfoModel class],
        @"voucherList":[FNFreshBrandShopPageVoucherInfoModel class]
    };
}

- (FNFreshBrandShopPageModuleInfoType)customType{
    return (FNFreshBrandShopPageModuleInfoType)self.type;
}

@end

@implementation FNFreshBrandShopPagePicInfoModel

- (CGFloat)scale{
    if(self.width > 0 && self.height > 0){
        return (self.width * 1.0) / (self.height * 1.0);
    }
    return 351.0 / 89;
}


@end

@implementation FNFreshBrandShopPageVoucherInfoModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        /// 设置默认值
        self.statue = 1;
    }
    return self;
}


@end

/// 立即领取请求
@implementation FNFreshBrandShopPageAcOnceGetResponseModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"isJoin":@"body.isJoin",
        @"status":@"body.status"
    };
}

@end
