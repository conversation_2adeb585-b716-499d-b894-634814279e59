//
//  FNFreshBrandShopPageResponseModel.h
//  FNFresh
//
//  Created by WangDan on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBaseResponseModel.h"

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSUInteger,FNFreshBrandShopPageModuleInfoType) {
    FNFreshBrandShopPageModuleInfoType_banner = 1 ,// banner 模块
    FNFreshBrandShopPageModuleInfoType_coupons = 2 // 优惠券模块
};

@class FNFreshBrandShopPageJoinBrandResponseModel,
FNFreshBrandShopPageJoinBrandBrandInfoResponseModel,
FNFreshBrandShopPageAllBrandResponseModel,
FNFreshBrandShopPageAllBrandInfoResponseModel,
FNFreshBrandShopPageModuleInfoResponseModel,
FNFreshBrandShopPagePicInfoModel,
FNFreshBrandShopPageVoucherInfoModel;
/// 主页面请求
@interface FNFreshBrandShopPageResponseModel : FNFreshBaseResponseModel
//头部氛围图
@property (nonatomic,strong)NSString *headBannerUrl;
//入会品牌
@property (nonatomic,strong)FNFreshBrandShopPageJoinBrandResponseModel *joinBrand;
//品牌陈列
@property (nonatomic,strong)FNFreshBrandShopPageAllBrandResponseModel *allBrand;
//模块列表
@property (nonatomic,strong)NSArray<FNFreshBrandShopPageModuleInfoResponseModel *> *moduleList;

@end

@interface FNFreshBrandShopPageJoinBrandResponseModel : NSObject

//标题
@property (nonatomic,strong)NSString *title;

//品牌列表
@property (nonatomic,strong)NSArray<FNFreshBrandShopPageJoinBrandBrandInfoResponseModel *> *joinBrandList;

@end

@interface FNFreshBrandShopPageJoinBrandBrandInfoResponseModel : NSObject

//品牌id
@property (nonatomic,strong)NSString *brandId;

//品牌logo
@property (nonatomic,strong)NSString *logo;

//品牌名称
@property (nonatomic,strong)NSString *name;

//色值
@property (nonatomic,strong)NSString *color;

//品牌文案
@property (nonatomic,strong)NSString *tip;

@end

@interface FNFreshBrandShopPageAllBrandResponseModel: NSObject

/// 标题
@property (nonatomic,strong)NSString *title;

/// 副标题
@property (nonatomic,strong)NSString *subTitle;

/// 品牌logo
@property (nonatomic,strong)NSArray<FNFreshBrandShopPageAllBrandInfoResponseModel *> *allBrandList;


@end

@interface FNFreshBrandShopPageAllBrandInfoResponseModel : NSObject

/// 品牌id
@property (nonatomic,strong)NSString *brandId;

/// 品牌logo
@property (nonatomic,strong)NSString *logo;

/// 品牌名称
@property (nonatomic,strong)NSString *name;

/// 色值
@property (nonatomic,strong)NSString *color;

/// 文案
@property (nonatomic,strong)NSString *tip;

/// 0-未入会,1-已入会
@property (nonatomic,assign)NSInteger status;
@property (nonatomic,strong,readonly)NSString *statusDescString;

@end

@interface FNFreshBrandShopPageModuleInfoResponseModel : NSObject

/**
 模块类型
 1-横幅banner模块
 2-劵模块
 */
@property (nonatomic,assign)NSInteger type;
@property (nonatomic,assign)FNFreshBrandShopPageModuleInfoType customType;
//模块id
@property (nonatomic,strong)NSString *moduleId;
//序号
@property (nonatomic,assign)NSInteger sn;
//模块名称
@property (nonatomic,strong)NSString *moduleName;
//模块标题
@property (nonatomic,strong)NSString *moduleTitle;
//模块副标题
@property (nonatomic,strong)NSString *moduleSubTitle;
//图片列表
@property (nonatomic,strong)NSArray<FNFreshBrandShopPagePicInfoModel *> *picList;
//劵列表
@property (nonatomic,strong)NSArray<FNFreshBrandShopPageVoucherInfoModel *> *voucherList;

@end

@interface FNFreshBrandShopPagePicInfoModel : NSObject

// 图片链接
@property (nonatomic,strong)NSString *imgUrl;

// 跳转链接
@property (nonatomic,strong)NSString *linkUrl;

// 图片宽度
@property (nonatomic,assign)NSInteger width;

// 图片高度
@property (nonatomic,assign)NSInteger height;

/// 图片的宽高比  width / height
@property (nonatomic,assign,readonly)CGFloat scale;

@end

@interface FNFreshBrandShopPageVoucherInfoModel : NSObject

// 劵活动id
@property (nonatomic,strong)NSString *couponId;

// 劵标题
@property (nonatomic,strong)NSString *couponName;

// 劵图片
@property (nonatomic,strong)NSString *couponImg;

// 适用范围
@property (nonatomic,strong)NSString *scopeDescription;

// 进度，整数
@property (nonatomic,assign)CGFloat progress;

// XX双会员专享提示
@property (nonatomic,strong)NSString *tip;

// 品牌id
@property (nonatomic,strong)NSString *brandId;

// 是否已经领取,这个是用户自己定义的字段,不是后端返回的  1: 立即领取  2:已领取  默认是 1
@property (nonatomic,assign)NSInteger statue;

@end



/// 立即领取请求
@interface FNFreshBrandShopPageAcOnceGetResponseModel : FNFreshBaseResponseModel

/**
 0未入会,1-已入会
 只要传了品牌id都会返回
 */
@property (nonatomic,assign)NSInteger isJoin;
/**
 券状态 0：未领取1：已领取
 */
@property (nonatomic,assign)NSInteger status;

@end


NS_ASSUME_NONNULL_END
