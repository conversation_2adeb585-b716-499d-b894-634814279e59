//
//  FNFreshBrandShopPageRequestParamsModel.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/22.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshBaseParameterModel.h"

NS_ASSUME_NONNULL_BEGIN

/// 主页面请求
@interface FNFreshBrandShopPageRequestParamsModel : FNFreshBaseParameterModel

//门店id
@property (nonatomic,strong)NSString *storeId;

@end

/// 立即领取请求
@interface FNFreshBrandShopPageAcOnceGetRequestParamsModel : FNFreshBaseParameterModel

///品牌id
@property (nonatomic,strong)NSString *brandId;
///劵活动id
@property (nonatomic,strong)NSString *couponId;
//门店id
@property (nonatomic,strong)NSString *storeId;

@end




NS_ASSUME_NONNULL_END
