//
//  FNFreshNewShoppingStreetChildCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2022/8/25.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshNewShoppingStreetChildCell.h"

@interface FNFreshNewShoppingStreetChildCell()
@property (weak, nonatomic) IBOutlet UIImageView *itemImageView;
@property (weak, nonatomic) IBOutlet UILabel *itemTitleLabel;

@end

@implementation FNFreshNewShoppingStreetChildCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.itemImageView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
}

- (void)setDataWith:(FNFreshStoreServiceBannerItemModel *)model {
    [self.itemImageView fn_setImageWithURL:[NSURL URLWithString:model.img]
                               placeholder:[UIImage imageNamed:@"home_fake_selected"]];
    self.itemTitleLabel.text = model.title;
}


@end
