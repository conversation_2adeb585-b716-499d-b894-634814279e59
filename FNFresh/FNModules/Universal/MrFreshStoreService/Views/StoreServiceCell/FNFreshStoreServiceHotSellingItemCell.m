//
//  FNFreshStoreServiceHotSellingItemCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/1/10.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceHotSellingItemCell.h"
#import "FNFreshStoreServiceGoodsItemModel.h"
#import "UIFont+FontType.h"

@interface FNFreshStoreServiceHotSellingItemCell()
@property (weak, nonatomic) IBOutlet UIImageView *topImageView;

@property (weak, nonatomic) IBOutlet UIImageView *firstImageView;
@property (weak, nonatomic) IBOutlet UILabel *firstTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *firstPriceLabel;

@property (weak, nonatomic) IBOutlet UIImageView *secImageView;
@property (weak, nonatomic) IBOutlet UILabel *secTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *secPriceLabel;

@property (weak, nonatomic) IBOutlet UIImageView *thirdImageView;
@property (weak, nonatomic) IBOutlet UILabel *thirdTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *thirdPriceLabel;

@end

@implementation FNFreshStoreServiceHotSellingItemCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.contentView.backgroundColor = [UIColor whiteColor];
    self.firstImageView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
    self.secImageView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
    self.thirdImageView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
}

- (void)setDataWith:(FNFreshStoreServiceHotSellingItemModel *)model {
    
    [self.topImageView fn_setImageWithURL:[NSURL URLWithString:model.title]
                              placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];
      
    if (model.goodsList.count >= 3) {
        FNFreshStoreServiceGoodsItemModel *goodsItem = [model.goodsList safeObjectAtIndex:0];
        [self.firstImageView fn_setImageWithURL:[NSURL URLWithString:goodsItem.goodsImg]
                                    placeholder:[UIImage imageNamed:@"home_fake_selected"]];
        self.firstTitleLabel.text = goodsItem.goodsName;
//        [self.firstPriceLabel fresh_setPrice:goodsItem.price unit:@"" priceTypeKey:kFD_6 priceColor:[UIColor fn_colorWithColorKey:kFNThemeKey] isUnderLine:NO isAddObliqueLine:NO screenRatio:Ratio];
        [self updatePriceLabel:self.firstPriceLabel showPrice:goodsItem.price];
        
        goodsItem = [model.goodsList safeObjectAtIndex:1];
        [self.secImageView fn_setImageWithURL:[NSURL URLWithString:goodsItem.goodsImg]
                                  placeholder:[UIImage imageNamed:@"home_fake_selected"]];
        self.secTitleLabel.text = goodsItem.goodsName;
//        [self.secPriceLabel fresh_setPrice:goodsItem.price unit:@"" priceTypeKey:kFD_6 priceColor:[UIColor fn_colorWithColorKey:kFNThemeKey] isUnderLine:NO isAddObliqueLine:NO screenRatio:Ratio];
        [self updatePriceLabel:self.secPriceLabel showPrice:goodsItem.price];
        
        goodsItem = [model.goodsList safeObjectAtIndex:2];
        [self.thirdImageView fn_setImageWithURL:[NSURL URLWithString:goodsItem.goodsImg]
                                    placeholder:[UIImage imageNamed:@"home_fake_selected"]];
        self.thirdTitleLabel.text = goodsItem.goodsName;
//        [self.thirdPriceLabel fresh_setPrice:goodsItem.price unit:@"" priceTypeKey:kFD_6 priceColor:[UIColor fn_colorWithColorKey:kFNThemeKey] isUnderLine:NO isAddObliqueLine:NO screenRatio:Ratio];
        [self updatePriceLabel:self.thirdPriceLabel showPrice:goodsItem.price];
    }
}

- (void)updatePriceLabel:(UILabel *)label showPrice:(NSString *)price {
    NSDictionary *priceParams = @{
        kPriceTypeKey: kFD_6,
        kPriceColorKey: [UIColor fn_colorWithColorKey:kFNPriceKey],
        kPriceUnderLineKey:@(NO),
        kPriceAddObliqueLineKey:@(NO),
        kMoneyMarkFontKey:[UIFont fnHarmonyFontOfSize:14*Ratio weight:UIFontWeightBold],
        kIntegerFontKey:[UIFont fnHarmonyFontOfSize:14*Ratio weight:UIFontWeightBold],
        kDecimalFontKey:[UIFont fnHarmonyFontOfSize:14*Ratio weight:UIFontWeightBold]
    };
    [label fresh_setPrice:price params:priceParams];
}



@end
