//
//  FNFreshStoreServiceApplyAndPosterCell.m
//  FNFresh
//
//  Created by xn on 2022/1/21.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceApplyAndPosterCell.h"

@interface FNFreshStoreServiceApplyAndPosterCell()

@property (weak, nonatomic) IBOutlet UIImageView *leftImageView;
@property (weak, nonatomic) IBOutlet UIImageView *rightImageView;
@property (copy, nonatomic) NSArray<FNFreshStoreServiceBannerItemModel *> *picList;
@end


@implementation FNFreshStoreServiceApplyAndPosterCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.leftImageView.userInteractionEnabled = true;
    UITapGestureRecognizer *leftTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleClickLeftImageItem)];
    [self.leftImageView addGestureRecognizer:leftTap];
    
    self.rightImageView.userInteractionEnabled = true;
    UITapGestureRecognizer *rightTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleClickRightImageItem)];
    [self.rightImageView addGestureRecognizer:rightTap];
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    self.picList = sectionModel.storeServiceModel.picList;
    if (self.picList.count >= 2) {
        FNFreshStoreServiceBannerItemModel *itemFirst = self.picList.firstObject;
        [self.leftImageView fn_setImageWithURL:[NSURL URLWithString:itemFirst.img]
                                   placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];

        FNFreshStoreServiceBannerItemModel *itemLast = self.picList.lastObject;
        [self.rightImageView fn_setImageWithURL:[NSURL URLWithString:itemLast.img]
                                    placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];
    }
}


- (void)handleClickLeftImageItem {
    if (self.picList.count >= 2) {
        [self handleClickItem:self.picList.firstObject.url isApply:true];
    }
}

- (void)handleClickRightImageItem {
    if (self.picList.count >= 2) {
        [self handleClickItem:self.picList.lastObject.url isApply:false];
    }
}

- (void)handleClickItem:(NSString *)url isApply:(BOOL)isApply {
    if (self.delegate && [self.delegate respondsToSelector:@selector(storeServiceCellDidClickApplyAndPosterWithUrl:isApply:)]) {
        [self.delegate storeServiceCellDidClickApplyAndPosterWithUrl:url isApply:isApply];
    }
}

@end
