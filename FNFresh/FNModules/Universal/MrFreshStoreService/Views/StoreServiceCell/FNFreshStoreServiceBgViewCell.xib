<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceBgViewCell">
            <rect key="frame" x="0.0" y="0.0" width="351" height="189"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="351" height="189"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="PoO-nM-6ll">
                        <rect key="frame" x="0.0" y="0.0" width="351" height="42"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="42" id="4h2-zW-U3u"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yDK-7j-Lry">
                        <rect key="frame" x="0.0" y="42" width="351" height="147"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="uf9-EP-Vjp">
                                <rect key="frame" x="8" y="0.0" width="335" height="139"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="cgv-GS-Rdj">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </collectionView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="uf9-EP-Vjp" firstAttribute="top" secondItem="yDK-7j-Lry" secondAttribute="top" id="95H-Y9-3ft"/>
                            <constraint firstItem="uf9-EP-Vjp" firstAttribute="leading" secondItem="yDK-7j-Lry" secondAttribute="leading" constant="8" id="NoU-X5-vn8"/>
                            <constraint firstAttribute="trailing" secondItem="uf9-EP-Vjp" secondAttribute="trailing" constant="8" id="rdg-4d-sdg"/>
                            <constraint firstAttribute="bottom" secondItem="uf9-EP-Vjp" secondAttribute="bottom" constant="8" id="ztA-i3-RRn"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oQe-u2-rsG">
                        <rect key="frame" x="10" y="21" width="331" height="0.0"/>
                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                <real key="value" value="14"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="PoO-nM-6ll" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="E8V-tp-sjQ"/>
                <constraint firstAttribute="trailing" secondItem="oQe-u2-rsG" secondAttribute="trailing" constant="10" id="EoS-Ka-ijd"/>
                <constraint firstItem="oQe-u2-rsG" firstAttribute="centerY" secondItem="PoO-nM-6ll" secondAttribute="centerY" id="Gnw-AD-Hof"/>
                <constraint firstItem="PoO-nM-6ll" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="KQZ-ww-Xeu"/>
                <constraint firstAttribute="bottom" secondItem="yDK-7j-Lry" secondAttribute="bottom" id="NDJ-QM-boo"/>
                <constraint firstItem="yDK-7j-Lry" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="TVO-6W-VH8"/>
                <constraint firstAttribute="trailing" secondItem="yDK-7j-Lry" secondAttribute="trailing" id="VRd-e0-qzQ"/>
                <constraint firstItem="yDK-7j-Lry" firstAttribute="top" secondItem="PoO-nM-6ll" secondAttribute="bottom" id="WpU-mz-adq"/>
                <constraint firstItem="oQe-u2-rsG" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="nhe-Zg-f1O"/>
                <constraint firstAttribute="trailing" secondItem="PoO-nM-6ll" secondAttribute="trailing" id="y6i-ma-kYx"/>
            </constraints>
            <size key="customSize" width="320" height="189"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="10"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="bgView" destination="yDK-7j-Lry" id="tcr-h3-rbw"/>
                <outlet property="collectionView" destination="uf9-EP-Vjp" id="PDd-P9-8PG"/>
                <outlet property="titleLabel" destination="oQe-u2-rsG" id="gpN-ta-9mR"/>
                <outlet property="topImageView" destination="PoO-nM-6ll" id="buh-PJ-Qam"/>
                <outlet property="topImgHeightConstraint" destination="4h2-zW-U3u" id="Vhw-k4-rTP"/>
            </connections>
            <point key="canvasLocation" x="-52.173913043478265" y="57.254464285714285"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
