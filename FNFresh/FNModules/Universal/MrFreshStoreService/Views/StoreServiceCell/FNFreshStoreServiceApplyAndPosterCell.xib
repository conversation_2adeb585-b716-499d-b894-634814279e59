<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceApplyAndPosterCell">
            <rect key="frame" x="0.0" y="0.0" width="256" height="87"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="256" height="87"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="98f-4R-hPe">
                        <rect key="frame" x="132" y="0.0" width="124" height="87"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="21b-Pz-hlq">
                        <rect key="frame" x="0.0" y="0.0" width="124" height="87"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                </subviews>
            </view>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="98f-4R-hPe" secondAttribute="bottom" id="4l4-zG-RQx"/>
                <constraint firstItem="98f-4R-hPe" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="Gm4-y7-X1i"/>
                <constraint firstAttribute="trailing" secondItem="98f-4R-hPe" secondAttribute="trailing" id="gtv-wV-gmH"/>
                <constraint firstItem="98f-4R-hPe" firstAttribute="leading" secondItem="21b-Pz-hlq" secondAttribute="trailing" constant="8" id="kKO-Ey-pU7"/>
                <constraint firstItem="21b-Pz-hlq" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="mgX-BQ-8RM"/>
                <constraint firstAttribute="bottom" secondItem="21b-Pz-hlq" secondAttribute="bottom" id="wAP-4z-2l2"/>
                <constraint firstItem="21b-Pz-hlq" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="wBi-e6-i3U"/>
                <constraint firstItem="21b-Pz-hlq" firstAttribute="width" secondItem="98f-4R-hPe" secondAttribute="width" id="xFT-ts-ydX"/>
            </constraints>
            <size key="customSize" width="256" height="87"/>
            <connections>
                <outlet property="leftImageView" destination="21b-Pz-hlq" id="6fP-OZ-kYf"/>
                <outlet property="rightImageView" destination="98f-4R-hPe" id="gR9-Dl-y80"/>
            </connections>
            <point key="canvasLocation" x="149.27536231884059" y="57.254464285714285"/>
        </collectionViewCell>
    </objects>
</document>
