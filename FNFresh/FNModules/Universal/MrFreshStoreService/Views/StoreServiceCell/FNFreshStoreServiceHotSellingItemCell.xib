<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceHotSellingItemCell">
            <rect key="frame" x="0.0" y="0.0" width="164" height="230"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="164" height="230"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Dea-Ma-0so">
                        <rect key="frame" x="0.0" y="0.0" width="164" height="52"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="Dea-Ma-0so" secondAttribute="height" multiplier="164:52" id="KxR-RN-6jS"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eah-qk-pwP">
                        <rect key="frame" x="0.0" y="52" width="164" height="59.5"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="8PZ-Qo-raS">
                                <rect key="frame" x="12" y="5" width="49.5" height="49.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="8PZ-Qo-raS" secondAttribute="height" multiplier="1:1" id="ouJ-Y7-Tv8"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SyG-tz-WLE">
                                <rect key="frame" x="65.5" y="10" width="88.5" height="14.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                        <real key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Z2W-47-KDD">
                                <rect key="frame" x="65.5" y="29.5" width="35.5" height="17"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg_ranking_red" translatesAutoresizingMaskIntoConstraints="NO" id="F5w-1w-vhB">
                                <rect key="frame" x="4" y="0.0" width="18" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="8Zr-AJ-WBo">
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="fitScreen" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </constraint>
                                    <constraint firstAttribute="width" constant="18" id="XPg-Bc-sgU">
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="fitScreen" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </constraint>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bKJ-ZQ-DDw">
                                <rect key="frame" x="4" y="0.0" width="18" height="17"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="13"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                        <real key="value" value="13"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="SyG-tz-WLE" firstAttribute="leading" secondItem="8PZ-Qo-raS" secondAttribute="trailing" constant="4" id="3eI-kH-vSt"/>
                            <constraint firstItem="8PZ-Qo-raS" firstAttribute="leading" secondItem="eah-qk-pwP" secondAttribute="leading" constant="12" id="CvZ-EY-0mP"/>
                            <constraint firstItem="8PZ-Qo-raS" firstAttribute="top" secondItem="eah-qk-pwP" secondAttribute="top" constant="5" id="HuF-Ze-nhY"/>
                            <constraint firstItem="SyG-tz-WLE" firstAttribute="top" secondItem="eah-qk-pwP" secondAttribute="top" constant="10" id="Kvi-jh-BEa"/>
                            <constraint firstItem="bKJ-ZQ-DDw" firstAttribute="top" secondItem="F5w-1w-vhB" secondAttribute="top" id="Oeb-lg-sEz"/>
                            <constraint firstItem="bKJ-ZQ-DDw" firstAttribute="trailing" secondItem="F5w-1w-vhB" secondAttribute="trailing" id="RGE-aF-3jc"/>
                            <constraint firstItem="bKJ-ZQ-DDw" firstAttribute="leading" secondItem="F5w-1w-vhB" secondAttribute="leading" id="Rj1-mD-Yzn"/>
                            <constraint firstItem="F5w-1w-vhB" firstAttribute="leading" secondItem="eah-qk-pwP" secondAttribute="leading" constant="4" id="Rlj-2u-oRR"/>
                            <constraint firstAttribute="trailing" secondItem="SyG-tz-WLE" secondAttribute="trailing" constant="10" id="Z8u-X7-pBD"/>
                            <constraint firstItem="bKJ-ZQ-DDw" firstAttribute="bottom" secondItem="F5w-1w-vhB" secondAttribute="bottom" constant="-3" id="fCt-Zc-49y"/>
                            <constraint firstItem="Z2W-47-KDD" firstAttribute="top" secondItem="SyG-tz-WLE" secondAttribute="bottom" constant="5" id="ho7-yM-NGk"/>
                            <constraint firstItem="F5w-1w-vhB" firstAttribute="top" secondItem="eah-qk-pwP" secondAttribute="top" id="jXN-6D-HEr"/>
                            <constraint firstItem="Z2W-47-KDD" firstAttribute="leading" secondItem="SyG-tz-WLE" secondAttribute="leading" id="nyK-5q-2r0"/>
                            <constraint firstAttribute="bottom" secondItem="8PZ-Qo-raS" secondAttribute="bottom" constant="5" id="p3l-5f-d8w"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Q7h-iT-NfE">
                        <rect key="frame" x="0.0" y="111.5" width="164" height="59"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="i7R-a1-4zs">
                                <rect key="frame" x="10" y="5" width="49.5" height="49"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="i7R-a1-4zs" secondAttribute="height" multiplier="1:1" id="m82-31-JfL"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hbh-Wu-a3M">
                                <rect key="frame" x="63.5" y="8" width="88.5" height="14.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                        <real key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pwf-pz-D39">
                                <rect key="frame" x="63.5" y="27.5" width="35.5" height="17"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg_ranking_orange" translatesAutoresizingMaskIntoConstraints="NO" id="y4N-KX-fPo">
                                <rect key="frame" x="4" y="0.0" width="18" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="07r-k1-5PL">
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="fitScreen" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </constraint>
                                    <constraint firstAttribute="width" constant="18" id="Wpw-z7-v84">
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="fitScreen" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </constraint>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eeF-Qi-8PH">
                                <rect key="frame" x="4" y="0.0" width="18" height="17"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="13"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                        <real key="value" value="13"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="pwf-pz-D39" firstAttribute="leading" secondItem="Hbh-Wu-a3M" secondAttribute="leading" id="08q-UA-GCT"/>
                            <constraint firstItem="eeF-Qi-8PH" firstAttribute="top" secondItem="y4N-KX-fPo" secondAttribute="top" id="0HF-fV-ixG"/>
                            <constraint firstItem="eeF-Qi-8PH" firstAttribute="leading" secondItem="y4N-KX-fPo" secondAttribute="leading" id="3lX-CB-798"/>
                            <constraint firstItem="y4N-KX-fPo" firstAttribute="leading" secondItem="Q7h-iT-NfE" secondAttribute="leading" constant="4" id="6He-ks-ElC"/>
                            <constraint firstItem="y4N-KX-fPo" firstAttribute="top" secondItem="Q7h-iT-NfE" secondAttribute="top" id="7Sm-Ze-VW7"/>
                            <constraint firstItem="eeF-Qi-8PH" firstAttribute="trailing" secondItem="y4N-KX-fPo" secondAttribute="trailing" id="Bsl-dA-HaN"/>
                            <constraint firstItem="i7R-a1-4zs" firstAttribute="leading" secondItem="Q7h-iT-NfE" secondAttribute="leading" constant="10" id="Mh1-iu-lC9"/>
                            <constraint firstItem="eeF-Qi-8PH" firstAttribute="bottom" secondItem="y4N-KX-fPo" secondAttribute="bottom" constant="-3" id="XvD-bq-Fna"/>
                            <constraint firstItem="pwf-pz-D39" firstAttribute="top" secondItem="Hbh-Wu-a3M" secondAttribute="bottom" constant="5" id="aZA-wO-6yV"/>
                            <constraint firstItem="Hbh-Wu-a3M" firstAttribute="leading" secondItem="i7R-a1-4zs" secondAttribute="trailing" constant="4" id="cto-Sy-O7u"/>
                            <constraint firstItem="i7R-a1-4zs" firstAttribute="top" secondItem="Q7h-iT-NfE" secondAttribute="top" constant="5" id="fIs-7h-0Yg"/>
                            <constraint firstItem="Hbh-Wu-a3M" firstAttribute="top" secondItem="Q7h-iT-NfE" secondAttribute="top" constant="8" id="fWm-5R-Bgs"/>
                            <constraint firstAttribute="trailing" secondItem="Hbh-Wu-a3M" secondAttribute="trailing" constant="12" id="wtk-C3-COL"/>
                            <constraint firstAttribute="bottom" secondItem="i7R-a1-4zs" secondAttribute="bottom" constant="5" id="xsO-Zh-vf8"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zAH-20-YEm">
                        <rect key="frame" x="0.0" y="170.5" width="164" height="59.5"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="BAM-Se-yUn">
                                <rect key="frame" x="10" y="5" width="49.5" height="49.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="BAM-Se-yUn" secondAttribute="height" multiplier="1:1" id="Y9Z-66-7lm"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="55H-da-zwe">
                                <rect key="frame" x="63.5" y="4" width="88.5" height="14.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                        <real key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KEo-KE-4Qd">
                                <rect key="frame" x="63.5" y="23.5" width="35.5" height="17"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg_ranking_yellow" translatesAutoresizingMaskIntoConstraints="NO" id="jGT-8U-VVB">
                                <rect key="frame" x="4" y="0.0" width="18" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="FTA-tK-oxj">
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="fitScreen" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </constraint>
                                    <constraint firstAttribute="width" constant="18" id="pCd-NJ-8tr">
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="fitScreen" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </constraint>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="3" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9US-sb-yh3">
                                <rect key="frame" x="4" y="-3" width="18" height="23"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="13"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                        <real key="value" value="13"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="BAM-Se-yUn" firstAttribute="leading" secondItem="zAH-20-YEm" secondAttribute="leading" constant="10" id="0b9-0A-v2A"/>
                            <constraint firstItem="KEo-KE-4Qd" firstAttribute="top" secondItem="55H-da-zwe" secondAttribute="bottom" constant="5" id="4h6-bt-s3u"/>
                            <constraint firstItem="jGT-8U-VVB" firstAttribute="leading" secondItem="9US-sb-yh3" secondAttribute="leading" id="9TP-QJ-OaI"/>
                            <constraint firstItem="55H-da-zwe" firstAttribute="top" secondItem="zAH-20-YEm" secondAttribute="top" constant="4" id="DDl-KX-Raw"/>
                            <constraint firstItem="BAM-Se-yUn" firstAttribute="top" secondItem="zAH-20-YEm" secondAttribute="top" constant="5" id="DmQ-4J-t3T"/>
                            <constraint firstItem="jGT-8U-VVB" firstAttribute="top" secondItem="zAH-20-YEm" secondAttribute="top" id="GQ9-mj-GLq"/>
                            <constraint firstItem="55H-da-zwe" firstAttribute="leading" secondItem="BAM-Se-yUn" secondAttribute="trailing" constant="4" id="LHu-Lt-xZE"/>
                            <constraint firstItem="jGT-8U-VVB" firstAttribute="top" secondItem="9US-sb-yh3" secondAttribute="top" constant="3" id="NQL-ez-pnm"/>
                            <constraint firstItem="jGT-8U-VVB" firstAttribute="leading" secondItem="zAH-20-YEm" secondAttribute="leading" constant="4" id="aPv-px-hqb"/>
                            <constraint firstItem="KEo-KE-4Qd" firstAttribute="leading" secondItem="55H-da-zwe" secondAttribute="leading" id="clg-8g-i2c"/>
                            <constraint firstItem="jGT-8U-VVB" firstAttribute="bottom" secondItem="9US-sb-yh3" secondAttribute="bottom" id="gOn-zo-ZkQ"/>
                            <constraint firstAttribute="trailing" secondItem="55H-da-zwe" secondAttribute="trailing" constant="12" id="nD1-EV-fdc"/>
                            <constraint firstAttribute="bottom" secondItem="BAM-Se-yUn" secondAttribute="bottom" constant="5" id="nf4-5Y-Ixr"/>
                            <constraint firstItem="jGT-8U-VVB" firstAttribute="trailing" secondItem="9US-sb-yh3" secondAttribute="trailing" id="oMN-aE-IK2"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="zAH-20-YEm" firstAttribute="top" secondItem="Q7h-iT-NfE" secondAttribute="bottom" id="5Yu-qx-lyK"/>
                <constraint firstAttribute="trailing" secondItem="Dea-Ma-0so" secondAttribute="trailing" id="6cT-SR-c1p"/>
                <constraint firstItem="Q7h-iT-NfE" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="D44-Bh-Jod"/>
                <constraint firstAttribute="trailing" secondItem="Q7h-iT-NfE" secondAttribute="trailing" id="Dce-mZ-5t4"/>
                <constraint firstItem="Q7h-iT-NfE" firstAttribute="height" secondItem="eah-qk-pwP" secondAttribute="height" id="EEa-bA-dsp"/>
                <constraint firstItem="eah-qk-pwP" firstAttribute="top" secondItem="Dea-Ma-0so" secondAttribute="bottom" id="Ow6-Dt-Qnl"/>
                <constraint firstItem="Dea-Ma-0so" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="cyg-TN-SjW"/>
                <constraint firstItem="Q7h-iT-NfE" firstAttribute="width" secondItem="eah-qk-pwP" secondAttribute="width" id="dNf-iw-Ipi"/>
                <constraint firstItem="Dea-Ma-0so" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="dTo-Gi-X81"/>
                <constraint firstItem="zAH-20-YEm" firstAttribute="height" secondItem="eah-qk-pwP" secondAttribute="height" id="e54-l8-Xq8"/>
                <constraint firstAttribute="bottom" secondItem="zAH-20-YEm" secondAttribute="bottom" id="kaG-4v-bdN"/>
                <constraint firstItem="Q7h-iT-NfE" firstAttribute="top" secondItem="eah-qk-pwP" secondAttribute="bottom" id="mgB-ZU-fSZ"/>
                <constraint firstAttribute="trailing" secondItem="zAH-20-YEm" secondAttribute="trailing" id="oL5-Ls-Q2m"/>
                <constraint firstItem="zAH-20-YEm" firstAttribute="width" secondItem="eah-qk-pwP" secondAttribute="width" id="pEt-qK-0Ov"/>
                <constraint firstItem="eah-qk-pwP" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="qFa-dR-vQH"/>
                <constraint firstAttribute="trailing" secondItem="eah-qk-pwP" secondAttribute="trailing" id="tGS-R0-7J6"/>
                <constraint firstItem="zAH-20-YEm" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="ycc-QT-7jw"/>
            </constraints>
            <size key="customSize" width="186" height="230"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="10"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="firstImageView" destination="8PZ-Qo-raS" id="w4G-4n-Jrz"/>
                <outlet property="firstPriceLabel" destination="Z2W-47-KDD" id="zsM-mo-ZFm"/>
                <outlet property="firstTitleLabel" destination="SyG-tz-WLE" id="gSl-2n-TGY"/>
                <outlet property="secImageView" destination="i7R-a1-4zs" id="2Hy-4F-Cik"/>
                <outlet property="secPriceLabel" destination="pwf-pz-D39" id="vev-Sj-jYI"/>
                <outlet property="secTitleLabel" destination="Hbh-Wu-a3M" id="otF-24-Gxa"/>
                <outlet property="thirdImageView" destination="BAM-Se-yUn" id="GvV-ap-CY4"/>
                <outlet property="thirdPriceLabel" destination="KEo-KE-4Qd" id="iy3-AZ-4RU"/>
                <outlet property="thirdTitleLabel" destination="55H-da-zwe" id="DwC-59-oQy"/>
                <outlet property="topImageView" destination="Dea-Ma-0so" id="UXf-jk-bMr"/>
            </connections>
            <point key="canvasLocation" x="236.23188405797103" y="52.901785714285715"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="bg_ranking_orange" width="18" height="20"/>
        <image name="bg_ranking_red" width="18" height="20"/>
        <image name="bg_ranking_yellow" width="18" height="20"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
