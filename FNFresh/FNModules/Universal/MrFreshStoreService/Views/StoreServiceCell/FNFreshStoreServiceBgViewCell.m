//
//  FNFreshStoreServiceBgViewCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/1/10.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceBgViewCell.h"
#import "FNFreshStoreServiceHotSellingItemCell.h"
#import "FNFreshNewShoppingStreetChildCell.h"

@interface FNFreshStoreServiceBgViewCell()<UICollectionViewDelegate,UICollectionViewDataSource>
@property (weak, nonatomic) IBOutlet UIImageView *topImageView;
@property (weak, nonatomic) IBOutlet UIView *bgView;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topImgHeightConstraint;

@property (copy,nonatomic) NSArray *dataArray;
@property (assign, nonatomic) FNFreshStoreHomeSectionType itemCellType;
@property (assign, nonatomic) CGFloat contentViewHeight;
@property (copy, nonatomic) NSString *toast;

@end

@implementation FNFreshStoreServiceBgViewCell

- (void)awakeFromNib {
    [super awakeFromNib];

    // Initialization code
    [self initlializeCollectionView];
}

- (void)initlializeCollectionView {
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.scrollEnabled = false;
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
    
    NSArray *cellsName = @[
        NSStringFromClass([FNFreshStoreServiceHotSellingItemCell class]),
        NSStringFromClass([FNFreshNewShoppingStreetChildCell class])
    ];
    
    for (NSString *cellClassName in cellsName) {
        [self.collectionView registerNib:[UINib nibWithNibName:cellClassName
                                                        bundle:[FNFreshBundleHandler fnFreshBundle]]
              forCellWithReuseIdentifier:cellClassName];
    }
    
    [self.collectionView registerClass:[UICollectionViewCell class] forCellWithReuseIdentifier:@"StoreServiceBgViewDefaultCell"];
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    FNFreshStoreServiceModel *itemModel = sectionModel.storeServiceModel;
    self.topImgHeightConstraint.constant = ceilf((SCREEN_WIDTH - 24)*42/351);
    self.titleLabel.text = @"";
    self.collectionView.backgroundColor = [UIColor whiteColor];
    [self.topImageView fn_setImageWithURL:[NSURL URLWithString:itemModel.bannerUrl]
                              placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];
    if (itemModel.color.length > 0) {
        self.bgView.backgroundColor = [UIColor hexString:itemModel.color];
    } else {
        self.bgView.backgroundColor = [UIColor whiteColor];
    }
    self.itemCellType = sectionModel.sectionType;
    self.contentViewHeight = sectionModel.contentViewHeight;
    self.collectionView.backgroundColor = [UIColor whiteColor];
    self.collectionView.layer.cornerRadius = 10;

    //模块类型（ 背景头部 1 积分栏位+轮播 2 今日出炉 3 开业礼包 4 今日上新 5
    //今日省钱 6 今日大牌 7 活动报名+促销海报 8 商店街 9 排行榜 10 横幅BANNER 11）
    
    switch (self.itemCellType) {
        case FNFreshMrFreshStoreTypeHotSelling:
            //排行榜模块
            self.collectionView.backgroundColor = [UIColor clearColor];
            self.dataArray = itemModel.pondList;
            break;
            //新版商店街
        case FNFreshMrFreshStoreTypeShoppingStreetNew:
            //新版商店街
            self.collectionView.backgroundColor = [UIColor clearColor];
            self.collectionView.layer.cornerRadius = 0;
            if (sectionModel.hasShoppingStreetIntegralData && itemModel.subTitle.length > 0) {
                self.titleLabel.text = itemModel.subTitle;
                [self.topImageView fn_setImageWithURL:[NSURL URLWithString:@""]
                                          placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];
                self.topImgHeightConstraint.constant = 45;
            }
            self.dataArray = itemModel.picList;
        default:
            break;
    }
    
    [self.collectionView reloadData];
}

#pragma mark UICollectionViewDelegate&UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    switch (self.itemCellType) {
        case FNFreshMrFreshStoreTypeHotSelling: {
            //排行榜模块 10
            FNFreshStoreServiceHotSellingItemCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceHotSellingItemCell class]) forIndexPath:indexPath];
            [cell setDataWith:[self.dataArray safeObjectAtIndex:indexPath.item]];
            return cell;
        }
        case FNFreshMrFreshStoreTypeShoppingStreetNew: {
            //新版商店街 13
            FNFreshNewShoppingStreetChildCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshNewShoppingStreetChildCell class]) forIndexPath:indexPath];
            [cell setDataWith:[self.dataArray safeObjectAtIndex:indexPath.item]];
            return cell;
        }
        default:
            break;
    }
    UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"StoreServiceBgViewDefaultCell" forIndexPath:indexPath];
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    //内容总宽度 SCREEN_WIDTH - 24 - 16
    NSInteger itemsCount = self.dataArray.count;
    switch (self.itemCellType) {
        case FNFreshMrFreshStoreTypeHotSelling://排行榜模块
            return CGSizeMake((floor((SCREEN_WIDTH - 40 - 6)/2)), floor(238*Ratio));
        case FNFreshMrFreshStoreTypeShoppingStreetNew: { //新版商店街
            return CGSizeMake((floor((SCREEN_WIDTH - 40 - 6)/2)), self.contentViewHeight);
        }
        default:
            break;
    }
    return CGSizeMake(0, 0);
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (!self.delegate) {
        return;
    }
    
    switch (self.itemCellType) {
        case FNFreshMrFreshStoreTypeHotSelling: {//排行榜模块
            FNFreshStoreServiceHotSellingItemModel *item = [self.dataArray safeObjectAtIndex:indexPath.item];
            if ([item isKindOfClass: [FNFreshStoreServiceHotSellingItemModel class]] &&
                [self.delegate respondsToSelector:@selector(storeServiceCellDidClickHotSellingItemWithPondId:index:)]) {
                [self.delegate storeServiceCellDidClickHotSellingItemWithPondId:item.pondId index:indexPath.item];
            }
        }
        case FNFreshMrFreshStoreTypeShoppingStreetNew: { //新版商店街
            FNFreshStoreServiceBannerItemModel *item = [self.dataArray safeObjectAtIndex:indexPath.item];
            if ([item isKindOfClass: [FNFreshStoreServiceBannerItemModel class]] &&
                [self.delegate respondsToSelector:@selector(storeServiceCellDidClickNewShoppingStreetItemWithUrl:index:)]) {
                [self.delegate storeServiceCellDidClickNewShoppingStreetItemWithUrl:item.url index:indexPath.item];
            }
        }
            break;
        default:
            break;
    }
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return 0.0f;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    if (self.itemCellType == FNFreshMrFreshStoreTypeHotSelling ||
        self.itemCellType == FNFreshMrFreshStoreTypeShoppingStreetNew) {
        return 6;
    }
    return 0.0f;
}

@end
