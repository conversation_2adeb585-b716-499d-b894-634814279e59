<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshNewShoppingStreetChildCell">
            <rect key="frame" x="0.0" y="0.0" width="300" height="153"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="300" height="153"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="cTR-dK-CdU">
                        <rect key="frame" x="0.0" y="0.0" width="300" height="126"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qzx-q4-mf3">
                        <rect key="frame" x="0.0" y="132" width="300" height="16"/>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.20000001789999999" green="0.20000001789999999" blue="0.20000001789999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                <real key="value" value="13"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="cTR-dK-CdU" secondAttribute="trailing" id="0Vz-yB-3dx"/>
                <constraint firstItem="cTR-dK-CdU" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="7l4-sB-hLU"/>
                <constraint firstItem="cTR-dK-CdU" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="FcC-aE-npy"/>
                <constraint firstAttribute="bottom" secondItem="Qzx-q4-mf3" secondAttribute="bottom" constant="5" id="QIx-QL-THz"/>
                <constraint firstItem="Qzx-q4-mf3" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="YfQ-2p-Npb"/>
                <constraint firstAttribute="trailing" secondItem="Qzx-q4-mf3" secondAttribute="trailing" id="enT-DQ-Ova"/>
                <constraint firstItem="Qzx-q4-mf3" firstAttribute="top" secondItem="cTR-dK-CdU" secondAttribute="bottom" constant="6" id="yYa-QU-yhB"/>
            </constraints>
            <size key="customSize" width="300" height="153"/>
            <connections>
                <outlet property="itemImageView" destination="cTR-dK-CdU" id="3xL-sd-oRo"/>
                <outlet property="itemTitleLabel" destination="Qzx-q4-mf3" id="oEI-zI-tJ5"/>
            </connections>
            <point key="canvasLocation" x="133.33333333333334" y="80.691964285714278"/>
        </collectionViewCell>
    </objects>
</document>
