//
//  FNFreshStoreServiceBannerAndAdsCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2024/7/24.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceBannerAndAdsCell.h"
#import "SDAnimatedImageView+WebCache.h"
#import "FNFreshCommonBanner.h"
#import "FNFreshUrlRouter.h"
#import "NSArray+Operation.h"
#import "FNFreshStoreServiceTrackDataTool.h"
#import "FNFreshStoreServiceEventNameString.h"

@interface FNFreshStoreServiceBannerAndAdsCell()
@property (weak, nonatomic) IBOutlet FNFreshCommonBanner *bannerView;
@property (weak, nonatomic) IBOutlet SDAnimatedImageView *adImageView;

@property (nonatomic, strong) NSString *adsJumpUrl;
@property (nonatomic,strong) NSNumber *sectionType;

@end


@implementation FNFreshStoreServiceBannerAndAdsCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.bannerView.backgroundColor = [UIColor whiteColor];
    
    WS(weakSelf);
    self.bannerView.tappedBlock = ^(NSInteger selectedIndex, NSString * _Nullable linkUrl) {
        [weakSelf handleTapBannerItemIndex:selectedIndex linkUrl:linkUrl];
    };
    self.bannerView.bannerChangedBlock = ^(NSInteger idx) {
    };
//    banner.edgeInsets = UIEdgeInsetsMake(8, 18, 8, 18);
    
    self.adImageView.userInteractionEnabled = true;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapAdImageView)];
    [self.adImageView addGestureRecognizer:tap];
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    
    self.sectionType = [NSNumber numberWithInteger:sectionModel.sectionType];
    [self.adImageView fn_setImageWithURL:[NSURL URLWithString:sectionModel.storeServiceModel.pic.img] placeholder:[UIImage imageWithColor:[UIColor whiteColor]] completed:nil];
    self.adsJumpUrl = sectionModel.storeServiceModel.pic.url;

    NSArray *bannerList = [sectionModel.storeServiceModel.picList _map:^FNFreshCommonBannerModel *(FNFreshStoreServiceBannerItemModel* banner) {
        FNFreshCommonBannerModel *model = [FNFreshCommonBannerModel new];
        model.img = banner.img;
        model.url = banner.url;
        return model;
    }];
    self.bannerView.bannerList = bannerList;
}

- (void)handleTapAdImageView {
    if (self.adsJumpUrl.length > 0) {
        [self touchActionName:FNFreshStoreServiceEventOpenUrl object:nil userInfo:@{
            FNFreshUserInfoConstKey.content:self.adsJumpUrl,
            FNFreshUserInfoConstKey.type:self.sectionType}];
    }
}

- (void)handleTapBannerItemIndex:(NSInteger)index linkUrl:(NSString *)linkUrl {
    if (linkUrl.length > 0) {
    [self touchActionName:FNFreshStoreServiceEventOpenUrl object:nil userInfo:@{
                    FNFreshUserInfoConstKey.content:linkUrl,
                    FNFreshUserInfoConstKey.type:self.sectionType,
                    FNFreshUserInfoConstKey.index: [NSNumber numberWithInteger:index+1]}];
    }
}



@end
