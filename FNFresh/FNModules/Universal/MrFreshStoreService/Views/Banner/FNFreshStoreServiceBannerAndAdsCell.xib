<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceBannerAndAdsCell">
            <rect key="frame" x="0.0" y="0.0" width="321" height="230"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="321" height="230"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="urb-tX-upc" customClass="FNFreshCommonBanner">
                        <rect key="frame" x="0.0" y="0.0" width="156.66666666666666" height="230"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SHo-Mb-7Ed" customClass="SDAnimatedImageView">
                        <rect key="frame" x="164.66666666666663" y="0.0" width="156.33333333333337" height="230"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="SHo-Mb-7Ed" secondAttribute="bottom" id="6Oe-AM-91a"/>
                <constraint firstItem="SHo-Mb-7Ed" firstAttribute="height" secondItem="urb-tX-upc" secondAttribute="height" id="Ctl-i9-PO4"/>
                <constraint firstItem="urb-tX-upc" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="FIZ-Gz-3wF"/>
                <constraint firstItem="SHo-Mb-7Ed" firstAttribute="width" secondItem="urb-tX-upc" secondAttribute="width" id="HTR-e9-WjW"/>
                <constraint firstAttribute="bottom" secondItem="urb-tX-upc" secondAttribute="bottom" id="bqf-Ly-tho"/>
                <constraint firstItem="urb-tX-upc" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="k7l-ez-Oe2"/>
                <constraint firstItem="SHo-Mb-7Ed" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="v5S-NV-56x"/>
                <constraint firstItem="SHo-Mb-7Ed" firstAttribute="leading" secondItem="urb-tX-upc" secondAttribute="trailing" constant="8" id="xDb-Yh-uPQ"/>
                <constraint firstAttribute="trailing" secondItem="SHo-Mb-7Ed" secondAttribute="trailing" id="yWW-M4-TUe"/>
            </constraints>
            <size key="customSize" width="321" height="230"/>
            <connections>
                <outlet property="adImageView" destination="SHo-Mb-7Ed" id="Yeg-Pb-QZD"/>
                <outlet property="bannerView" destination="urb-tX-upc" id="vbl-zX-R41"/>
            </connections>
            <point key="canvasLocation" x="232.82442748091603" y="-4.9295774647887329"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
