//
//  FNFreshStoreServiceCarouseCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2024/7/24.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceCarouseCell.h"
#import "FNFreshUrlRouter.h"
#import "NSArray+Operation.h"
#import "FNFreshStoreServiceTrackDataTool.h"


@implementation FNFreshStoreServiceCarouseCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.bannerView.backgroundColor = [UIColor whiteColor];
    self.bannerView.tappedBlock = ^(NSInteger selectedIndex, NSString * _Nullable linkUrl) {
        [FNFreshStoreServiceTrackDataTool agentClickCarouserItem:selectedIndex+1];
        [[FNFreshUrlRouter new] jumpControllerWithRemoteURLString:linkUrl params:nil completion:nil];
    };
    self.bannerView.bannerChangedBlock = ^(NSInteger idx) {
        [FNFreshStoreServiceTrackDataTool agentCarouserDidAppear:idx];
    };
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    NSArray *bannerList = [sectionModel.storeServiceModel.picList _map:^FNFreshCommonBannerModel *(FNFreshStoreServiceBannerItemModel* banner) {
        FNFreshCommonBannerModel *model = [FNFreshCommonBannerModel new];
        model.img = banner.img;
        model.url = banner.url;
        return model;
    }];
    self.bannerView.bannerList = bannerList;
}

@end
