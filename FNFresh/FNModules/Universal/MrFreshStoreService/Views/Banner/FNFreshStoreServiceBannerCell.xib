<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceBannerCell">
            <rect key="frame" x="0.0" y="0.0" width="320" height="118"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="320" height="118"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="wm3-f2-nGw" customClass="SDAnimatedImageView">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="118"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstItem="wm3-f2-nGw" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="8Qv-E9-V8o"/>
                <constraint firstItem="wm3-f2-nGw" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="HUi-4T-qRc"/>
                <constraint firstAttribute="trailing" secondItem="wm3-f2-nGw" secondAttribute="trailing" id="gdg-qo-GaZ"/>
                <constraint firstAttribute="bottom" secondItem="wm3-f2-nGw" secondAttribute="bottom" id="xk7-7M-Tue"/>
            </constraints>
            <size key="customSize" width="320" height="118"/>
            <connections>
                <outlet property="bannerImgView" destination="wm3-f2-nGw" id="V2Q-Xn-Gfh"/>
            </connections>
            <point key="canvasLocation" x="129" y="56"/>
        </collectionViewCell>
    </objects>
</document>
