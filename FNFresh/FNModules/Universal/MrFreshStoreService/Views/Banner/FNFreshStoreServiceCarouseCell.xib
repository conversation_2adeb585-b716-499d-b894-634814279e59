<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceCarouseCell">
            <rect key="frame" x="0.0" y="0.0" width="248" height="143"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="248" height="143"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wDy-a9-Yfz" customClass="FNFreshCommonBanner">
                        <rect key="frame" x="0.0" y="0.0" width="248" height="143"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="wDy-a9-Yfz" secondAttribute="trailing" id="LRg-lu-YMq"/>
                <constraint firstItem="wDy-a9-Yfz" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="M1E-Yk-9KR"/>
                <constraint firstAttribute="bottom" secondItem="wDy-a9-Yfz" secondAttribute="bottom" id="a0G-W4-yOH"/>
                <constraint firstItem="wDy-a9-Yfz" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="cuI-eR-dbK"/>
            </constraints>
            <size key="customSize" width="248" height="143"/>
            <connections>
                <outlet property="bannerView" destination="wDy-a9-Yfz" id="POB-0W-NXS"/>
            </connections>
            <point key="canvasLocation" x="177.09923664122135" y="-13.028169014084508"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
