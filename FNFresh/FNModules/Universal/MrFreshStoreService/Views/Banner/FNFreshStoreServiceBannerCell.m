//
//  FNFreshStoreServiceBannerCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2022/8/29.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceBannerCell.h"
#import "SDAnimatedImageView+WebCache.h"

@interface FNFreshStoreServiceBannerCell()
@property (weak, nonatomic) IBOutlet SDAnimatedImageView *bannerImgView;

@end

@implementation FNFreshStoreServiceBannerCell

- (void)awakeFromNib {
    [super awakeFromNib];
}

//- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
//    if (sectionModel.storeServiceModel.pic.img.length > 0) {
//        CGFloat imageViewWidth = SCREEN_WIDTH - 24;
//        WS(weakSelf);
//        [self.bannerImgView fn_setImageWithURL:[NSURL URLWithString:sectionModel.storeServiceModel.pic.img] placeholder:[UIImage imageWithColor:[UIColor whiteColor]] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
//            if (image == nil) return;
//
//            CGFloat h = floorf((CGFloat)image.size.height / image.size.width * imageViewWidth);
//            if (sectionModel.cellHeight == h) {
//                return;
//            }
//            sectionModel.cellHeight = h;
//            if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector((storeServiceCellBannerUpdateHeight:indexPath:))]) {
//                [weakSelf.delegate storeServiceCellBannerUpdateHeight:weakSelf indexPath:sectionModel.indexPath];
//            }
//        }];
//    }
//}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    if (sectionModel.storeServiceModel.pic.img.length > 0) {

//        if (sectionModel.cellHeight > 0){
//            [self.bannerImgView fn_setImageWithURL:[NSURL URLWithString:sectionModel.storeServiceModel.pic.img] placeholder:[UIImage imageWithColor:[UIColor whiteColor]] completed:nil];
//        } else {
            CGFloat imageViewWidth = SCREEN_WIDTH - 24;
            WS(weakSelf);
            [self.bannerImgView fn_setImageWithURL:[NSURL URLWithString:sectionModel.storeServiceModel.pic.img] placeholder:[UIImage imageWithColor:[UIColor whiteColor]] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
                
                if (image == nil) return;
        
                CGFloat h = floorf((CGFloat)image.size.height / image.size.width * imageViewWidth);
                if (sectionModel.cellHeight == h)  return;
                
                sectionModel.cellHeight = h;
                
                if (weakSelf.delegate && [weakSelf.delegate respondsToSelector:@selector((storeServiceCellBannerUpdateHeight:indexPath:))]) {
                    // 使用了异步图片加载，并在加载完成后尝试更新 cellHeight，这可能导致 Cell 在不可见时被修改，触发 iOS 18 的检查。
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [weakSelf.delegate storeServiceCellBannerUpdateHeight:weakSelf indexPath:sectionModel.indexPath];
                    });
                }
            }];
        }
//    }
}

@end
