//
//  FNFreshStoreServiceSectionModel.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/1/17.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshStoreServiceModel.h"
#import "FNFreshStoreServiceResponseModel.h"
#import "FNFreshNewStoreHomeInfoResponseModel.h"
#import "FNFreshStoreServicesHeaderAddressCellDataModel.h"
#import "FNFreshStoreServiceModuleTitleModel.h"
#import "FNCreditChannelResponseModel.h"


typedef NS_ENUM(NSUInteger,FNFreshStoreServiceKingKongCellUnFoldType) {
    FNFreshStoreServiceKingKongCellUnFoldType_hidden = 100,//隐藏
    FNFreshStoreServiceKingKongCellUnFoldType_unFlod, //折叠
    FNFreshStoreServiceKingKongCellUnFoldType_allShow //展开
};

@interface FNFreshStoreServiceSectionModel : NSObject

@property (assign, nonatomic) FNFreshStoreHomeSectionType sectionType;
@property (strong, nonatomic) FNFreshStoreServiceModel *storeServiceModel;
@property (strong, nonatomic) FNFreshStoreServiceResponseModel *responseModel;
@property (strong, nonatomic) FNFreshNewStoreHomeInfoResponseModel *nStoreResponseModel; //新店频道页

@property (nonatomic,strong)FNFreshStoreServicesHeaderAddressCellDataModel *topAddressHeaderDataModel;
@property (nonatomic,strong)FNFreshStoreServiceModuleTitleModel *moduleHeaderDataModel;
@property (nonatomic,strong)FNCreditOperationsModel *operationsModel;

@property (assign, nonatomic) CGFloat contentViewHeight;
@property (assign, nonatomic) CGFloat cellWidth;
@property (assign, nonatomic) CGFloat cellHeight;

@property (assign, nonatomic) NSIndexPath *indexPath;

//今日省钱专用
@property (copy, nonatomic) NSArray *saveMoneyItemsType;

//商店街联合模块有 积分兑换模块
@property (assign, nonatomic) BOOL hasShoppingStreetIntegralData;
//商店街联合模块有 商店街数据
@property (assign, nonatomic) BOOL hasShoppingStreetData;

@property (nonatomic,assign)FNFreshStoreServiceKingKongCellUnFoldType unFlodStyle;

@end


typedef enum : NSUInteger {
    FNFreshStoreServiceSaveMoneyHorizontalCoupon,//横向滑动的优惠券
    FNFreshStoreServiceSaveMoneyCouponsAndLimit, //优惠券和今日限定
    FNFreshStoreServiceSaveMoneyPicActivity,     //图片资源
} FNFreshStoreServiceSaveMoneyType;

//今日省钱
@interface FNFreshStoreServiceSaveMoneyItemModel: NSObject

@property (assign, nonatomic) FNFreshStoreServiceSaveMoneyType itemType;
@property (strong, nonatomic) FNFreshStoreServiceCouponCenterModel *couponCenter;//领券中心
@property (strong, nonatomic) FNFreshStoreServiceTodayLimitModel *limited;//当日限定
@property (copy, nonatomic) NSArray<FNFreshStoreServiceBannerItemModel *> *picList;

@end
