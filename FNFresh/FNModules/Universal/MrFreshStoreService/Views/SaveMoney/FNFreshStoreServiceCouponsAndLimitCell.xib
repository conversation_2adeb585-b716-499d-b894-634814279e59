<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceCouponsAndLimitCell">
            <rect key="frame" x="0.0" y="0.0" width="381" height="153"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="381" height="153"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4xY-uk-yZg">
                        <rect key="frame" x="0.0" y="0.0" width="187.5" height="153"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="今日神券" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SWC-Ga-PvU">
                                <rect key="frame" x="8" y="0.0" width="57.5" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="TOz-9K-jVH"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                <color key="textColor" red="0.199973762" green="0.2000150383" blue="0.19997116919999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                        <real key="value" value="14"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=" 省钱利器 " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dof-V3-JRr">
                                <rect key="frame" x="71.5" y="7" width="51.5" height="16"/>
                                <color key="backgroundColor" red="0.97841697930000004" green="0.3674263358" blue="0.35715115069999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="16" id="eu0-YR-9xD"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="8"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                        <real key="value" value="11"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gQa-8d-l5q" customClass="FNFreshNewCouponCenterChildView">
                                <rect key="frame" x="8" y="32" width="83" height="115"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1VX-zX-Kwm" customClass="FNFreshNewCouponCenterChildView">
                                <rect key="frame" x="97" y="32" width="82.5" height="115"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="gQa-8d-l5q" secondAttribute="bottom" constant="6" id="5wY-dN-YBl"/>
                            <constraint firstItem="gQa-8d-l5q" firstAttribute="leading" secondItem="4xY-uk-yZg" secondAttribute="leading" constant="8" id="BDB-Jq-TTF"/>
                            <constraint firstItem="SWC-Ga-PvU" firstAttribute="leading" secondItem="4xY-uk-yZg" secondAttribute="leading" constant="8" id="Rzs-D9-WjT"/>
                            <constraint firstItem="gQa-8d-l5q" firstAttribute="top" secondItem="4xY-uk-yZg" secondAttribute="top" constant="32" id="dik-TV-lQ8"/>
                            <constraint firstItem="1VX-zX-Kwm" firstAttribute="top" secondItem="gQa-8d-l5q" secondAttribute="top" id="etg-aM-1Qm"/>
                            <constraint firstItem="Dof-V3-JRr" firstAttribute="centerY" secondItem="SWC-Ga-PvU" secondAttribute="centerY" id="fQK-6D-xc2"/>
                            <constraint firstItem="1VX-zX-Kwm" firstAttribute="bottom" secondItem="gQa-8d-l5q" secondAttribute="bottom" id="fbe-Da-z6Y"/>
                            <constraint firstItem="Dof-V3-JRr" firstAttribute="leading" secondItem="SWC-Ga-PvU" secondAttribute="trailing" constant="6" id="gZx-Lo-h2b"/>
                            <constraint firstAttribute="trailing" secondItem="1VX-zX-Kwm" secondAttribute="trailing" constant="8" id="lzq-Qo-CC4"/>
                            <constraint firstItem="1VX-zX-Kwm" firstAttribute="leading" secondItem="gQa-8d-l5q" secondAttribute="trailing" constant="6" id="qIl-fM-ZKv"/>
                            <constraint firstItem="1VX-zX-Kwm" firstAttribute="width" secondItem="gQa-8d-l5q" secondAttribute="width" id="tJc-qm-w69"/>
                            <constraint firstItem="SWC-Ga-PvU" firstAttribute="top" secondItem="4xY-uk-yZg" secondAttribute="top" id="zGv-ZR-KIz"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="6"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ABw-QP-a6B">
                        <rect key="frame" x="193.5" y="0.0" width="187.5" height="153"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="当日限定" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="k6b-zx-Up1">
                                <rect key="frame" x="8" y="0.0" width="57.5" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="2a8-cl-u5A"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                <color key="textColor" red="0.199973762" green="0.2000150383" blue="0.19997116919999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                        <real key="value" value="14"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ifn-Oz-oID">
                                <rect key="frame" x="71.5" y="7" width="52.5" height="16"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_time" translatesAutoresizingMaskIntoConstraints="NO" id="ihd-y5-QUP">
                                        <rect key="frame" x="6" y="2" width="12" height="12"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="12" id="Spc-xV-aQz"/>
                                            <constraint firstAttribute="height" constant="12" id="Ylj-OB-HCi"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7YD-2S-Vyg">
                                        <rect key="frame" x="20" y="1.5" width="28.5" height="13.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                                <real key="value" value="11"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" red="0.97841697930000004" green="0.3674263358" blue="0.35715115069999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstItem="ihd-y5-QUP" firstAttribute="leading" secondItem="ifn-Oz-oID" secondAttribute="leading" constant="6" id="FdT-EE-M3Y"/>
                                    <constraint firstAttribute="height" constant="16" id="Itg-wd-BJW"/>
                                    <constraint firstItem="7YD-2S-Vyg" firstAttribute="leading" secondItem="ihd-y5-QUP" secondAttribute="trailing" constant="2" id="SxI-Rw-Dyp"/>
                                    <constraint firstItem="ihd-y5-QUP" firstAttribute="centerY" secondItem="ifn-Oz-oID" secondAttribute="centerY" id="Xvn-pR-BmG"/>
                                    <constraint firstItem="7YD-2S-Vyg" firstAttribute="centerY" secondItem="ifn-Oz-oID" secondAttribute="centerY" id="hPO-eW-xpr"/>
                                    <constraint firstAttribute="trailing" secondItem="7YD-2S-Vyg" secondAttribute="trailing" constant="4" id="tAs-ZY-Bf7"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="8"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rqb-e7-yWd" customClass="FNFreshStoreServiceGoodsItemView">
                                <rect key="frame" x="8" y="32" width="83" height="115"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dMo-AD-IVK" customClass="FNFreshStoreServiceGoodsItemView">
                                <rect key="frame" x="97" y="32" width="82.5" height="115"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="dMo-AD-IVK" firstAttribute="width" secondItem="Rqb-e7-yWd" secondAttribute="width" id="176-h2-4wn"/>
                            <constraint firstItem="Rqb-e7-yWd" firstAttribute="leading" secondItem="ABw-QP-a6B" secondAttribute="leading" constant="8" id="26N-90-mOq"/>
                            <constraint firstItem="ifn-Oz-oID" firstAttribute="centerY" secondItem="k6b-zx-Up1" secondAttribute="centerY" id="4QJ-oh-P4d"/>
                            <constraint firstItem="k6b-zx-Up1" firstAttribute="top" secondItem="ABw-QP-a6B" secondAttribute="top" id="AnC-zq-405"/>
                            <constraint firstAttribute="trailing" secondItem="dMo-AD-IVK" secondAttribute="trailing" constant="8" id="Boc-3L-UaQ"/>
                            <constraint firstAttribute="bottom" secondItem="Rqb-e7-yWd" secondAttribute="bottom" constant="6" id="Cq2-Ma-eOt"/>
                            <constraint firstItem="dMo-AD-IVK" firstAttribute="bottom" secondItem="Rqb-e7-yWd" secondAttribute="bottom" id="EwX-ht-tdh"/>
                            <constraint firstItem="k6b-zx-Up1" firstAttribute="leading" secondItem="ABw-QP-a6B" secondAttribute="leading" constant="8" id="G4F-l0-9NB"/>
                            <constraint firstItem="Rqb-e7-yWd" firstAttribute="top" secondItem="ABw-QP-a6B" secondAttribute="top" constant="32" id="iHt-4M-lo0"/>
                            <constraint firstItem="ifn-Oz-oID" firstAttribute="leading" secondItem="k6b-zx-Up1" secondAttribute="trailing" constant="6" id="kVC-qq-dPa"/>
                            <constraint firstItem="dMo-AD-IVK" firstAttribute="leading" secondItem="Rqb-e7-yWd" secondAttribute="trailing" constant="6" id="nsK-g8-znf"/>
                            <constraint firstItem="dMo-AD-IVK" firstAttribute="top" secondItem="Rqb-e7-yWd" secondAttribute="top" id="y7O-cM-IdM"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="6"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="4xY-uk-yZg" secondAttribute="bottom" id="91I-LV-sp3"/>
                <constraint firstItem="ABw-QP-a6B" firstAttribute="width" secondItem="4xY-uk-yZg" secondAttribute="width" id="Aa1-Cw-1ww"/>
                <constraint firstItem="ABw-QP-a6B" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="Yb4-en-6dn"/>
                <constraint firstItem="4xY-uk-yZg" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Yqx-Fp-2Ol"/>
                <constraint firstAttribute="bottom" secondItem="ABw-QP-a6B" secondAttribute="bottom" id="av8-88-YmD"/>
                <constraint firstAttribute="trailing" secondItem="ABw-QP-a6B" secondAttribute="trailing" id="hno-Jv-1ex"/>
                <constraint firstItem="ABw-QP-a6B" firstAttribute="leading" secondItem="4xY-uk-yZg" secondAttribute="trailing" constant="6" id="nY9-ub-PPP"/>
                <constraint firstItem="4xY-uk-yZg" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="woQ-ly-id4"/>
            </constraints>
            <size key="customSize" width="381" height="153"/>
            <connections>
                <outlet property="couponCenterSubTitleLabel" destination="Dof-V3-JRr" id="OkG-DM-WBw"/>
                <outlet property="couponCenterTitleLabel" destination="SWC-Ga-PvU" id="Hw0-iy-kHz"/>
                <outlet property="leftCouponView" destination="gQa-8d-l5q" id="iRh-A7-6D3"/>
                <outlet property="leftGoodsView" destination="Rqb-e7-yWd" id="9mF-On-nw8"/>
                <outlet property="limitPurchaseTitleLabel" destination="k6b-zx-Up1" id="hBv-Yg-XXx"/>
                <outlet property="rightCouponView" destination="1VX-zX-Kwm" id="t6q-5j-ixk"/>
                <outlet property="rightGoodsView" destination="dMo-AD-IVK" id="cAb-Sv-tlw"/>
                <outlet property="timeBgView" destination="ifn-Oz-oID" id="Eq3-Wo-BIa"/>
                <outlet property="timeLabel" destination="7YD-2S-Vyg" id="Rw9-T1-TTm"/>
            </connections>
            <point key="canvasLocation" x="425.36231884057975" y="55.245535714285715"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="icon_time" width="12" height="12"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
