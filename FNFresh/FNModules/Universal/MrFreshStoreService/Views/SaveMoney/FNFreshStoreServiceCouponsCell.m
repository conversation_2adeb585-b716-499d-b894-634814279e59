//
//  FNFreshStoreServiceCouponsCell.m
//  FNFresh
//
//  Created by xn on 2022/1/19.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceCouponsCell.h"
#import "FNFreshMrFreshCouponCenterSingleChildCell.h"
#import "FNFreshMrFreshCouponCenterChildCell.h"

@interface FNFreshStoreServiceCouponsCell()
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet UILabel *subTitleLabel;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (copy,nonatomic) NSArray *dataArray;
@property (copy, nonatomic) NSString *hotLinkUrl;     //热区跳转链接
@end

@implementation FNFreshStoreServiceCouponsCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self registerCells];
}

- (void)registerCells {
    NSArray *cellsName = @[
        NSStringFromClass([FNFreshMrFreshCouponCenterSingleChildCell class]),
        NSStringFromClass([FNFreshMrFreshCouponCenterChildCell class]),
    ];
    for (NSString *cellClassName in cellsName) {
        [self.collectionView registerNib:[UINib nibWithNibName:cellClassName
                                                        bundle:[FNFreshBundleHandler fnFreshBundle]]
              forCellWithReuseIdentifier:cellClassName];
    }
}

- (void)setCouponCenter:(FNFreshStoreServiceCouponCenterModel *)couponCenter {
    self.titleLabel.text = couponCenter.moduleName;
    self.subTitleLabel.text = [NSString stringWithFormat:@"  %@  ", couponCenter.moduleSubTitle];
    self.hotLinkUrl = couponCenter.hotLinkUrl;
    self.dataArray = couponCenter.couponList;
    [self.collectionView reloadData];
}

- (IBAction)contentViewClick:(id)sender {
    [self handleClick];
}

- (void)handleClick {
    if (self.handleClickCouponModule) {
        self.handleClickCouponModule(self.hotLinkUrl);
    }
}

#pragma mark - collection view data source and delegate
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.dataArray.count > 1) {
        FNFreshMrFreshCouponCenterSingleChildCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshMrFreshCouponCenterSingleChildCell class]) forIndexPath:indexPath];
        FNMrFreshPopupWindowNewGuidanceItemModel *dataModel = [self.dataArray safeObjectAtIndex:indexPath.item];
        [cell setupWithDataModel:dataModel];
        return cell;
    } else {
        FNFreshMrFreshCouponCenterChildCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshMrFreshCouponCenterChildCell class]) forIndexPath:indexPath];
        FNMrFreshPopupWindowNewGuidanceItemModel *dataModel = [self.dataArray safeObjectAtIndex:indexPath.item];
        [cell setupWithDataModel:dataModel isOffLine:YES];
        return cell;
    }
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [self handleClick];
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.dataArray.count == 1) {
        return CGSizeMake(SCREEN_WIDTH - 40 - 16, self.collectionView.bounds.size.height);
    }
    return CGSizeMake(260*Ratio, self.collectionView.bounds.size.height);
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0, 8, 0, 8);
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 8;
}
- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return 0;
}

@end
