//
//  FNFreshStoreServiceSaveMoneyCell.m
//  FNFresh
//
//  Created by xn on 2022/1/18.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceSaveMoneyCell.h"
#import "FNFreshStoreServiceCouponsCell.h"
#import "FNFreshStoreServicePicResourcesCell.h"
#import "FNFreshStoreServiceCouponsAndLimitCell.h"

@interface FNFreshStoreServiceSaveMoneyCell()<UICollectionViewDelegate,UICollectionViewDataSource>
@property (weak, nonatomic) IBOutlet UIImageView *topImageView;
@property (weak, nonatomic) IBOutlet UIView *bgView;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;


@property (assign,nonatomic) CGFloat contentViewWidth;
@property (copy,nonatomic) NSArray *dataArray;

@end

@implementation FNFreshStoreServiceSaveMoneyCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.contentViewWidth = SCREEN_WIDTH - 40;
    [self initlializeCollectionView];
}

- (void)initlializeCollectionView {
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.scrollEnabled = false;
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
    
    NSArray *cellsName = @[
        NSStringFromClass([FNFreshStoreServiceCouponsCell class]),
        NSStringFromClass([FNFreshStoreServicePicResourcesCell class]),
        NSStringFromClass([FNFreshStoreServiceCouponsAndLimitCell class]),
    ];
    
    for (NSString *cellClassName in cellsName) {
        [self.collectionView registerNib:[UINib nibWithNibName:cellClassName
                                                        bundle:[FNFreshBundleHandler fnFreshBundle]]
              forCellWithReuseIdentifier:cellClassName];
    }
    
    [self.collectionView registerClass:[UICollectionViewCell class] forCellWithReuseIdentifier:@"StoreServiceSaveMoneyDefaultCell"];
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    [self.topImageView fn_setImageWithURL:[NSURL URLWithString:sectionModel.storeServiceModel.bannerUrl]
                              placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];
    
    self.bgView.backgroundColor = [UIColor hexString:sectionModel.storeServiceModel.color];
    
    self.dataArray = sectionModel.saveMoneyItemsType;
    [self.collectionView reloadData];
}

//点击领券中心
- (void)handleClickCouponsCenter:(NSString *)url {
    if (self.delegate && [self.delegate respondsToSelector:@selector(storeServiceCellDidClickCouponCenterWithUrl:)]) {
        [self.delegate storeServiceCellDidClickCouponCenterWithUrl:url];
    }
}

//点击图片资源
- (void)handleClickPicResources:(NSString *)url isRight:(BOOL)isRight {
    if (self.delegate && [self.delegate respondsToSelector:@selector(storeServiceCellDidClickPicResourcesWithUrl:isRightItem:)]) {
        [self.delegate storeServiceCellDidClickPicResourcesWithUrl:url isRightItem:isRight];
    }
}

//点击当日限定商品item
- (void)handleClickLimitGoodsItem:(NSString *)toastMessage {
    if (self.delegate && [self.delegate respondsToSelector:@selector(storeServiceCellDidClickLimitPurchaseGoodsItem:)]) {
        [self.delegate storeServiceCellDidClickLimitPurchaseGoodsItem:toastMessage];
    }
}

#pragma mark UICollectionViewDelegate&UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceSaveMoneyItemModel *item = [self.dataArray safeObjectAtIndex:indexPath.item];
    WS(weakSelf);
    switch (item.itemType) {
        case FNFreshStoreServiceSaveMoneyHorizontalCoupon: {
            FNFreshStoreServiceCouponsCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceCouponsCell class]) forIndexPath:indexPath];
            cell.couponCenter = item.couponCenter;
            cell.handleClickCouponModule = ^(NSString *url) {
                [weakSelf handleClickCouponsCenter:url];
            };
            return cell;
        }
            break;
        case FNFreshStoreServiceSaveMoneyCouponsAndLimit: {
            FNFreshStoreServiceCouponsAndLimitCell*cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceCouponsAndLimitCell class]) forIndexPath:indexPath];
            [cell setDataWithCouponCenterModel:item.couponCenter limitPurchaseModel:item.limited countDowntimeBlock:^(long t) {
                item.limited.countDownTime = t;
            }];
            cell.handleClickCouponModule = ^(NSString *url) {
                [weakSelf handleClickCouponsCenter:url];
            };
            cell.handleClickGoodsItem = ^(NSString *toastMessage) {
                [weakSelf handleClickLimitGoodsItem:toastMessage];
            };
            return cell;
        }
            break;
        case FNFreshStoreServiceSaveMoneyPicActivity: {
            FNFreshStoreServicePicResourcesCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServicePicResourcesCell class]) forIndexPath:indexPath];
            [cell setDateWith:item.picList];
            cell.imgItemClick = ^(NSString *url, BOOL isRight) {
                [weakSelf handleClickPicResources:url isRight:isRight];
            };
            return cell;
        }
            break;
        default:
            break;
    }
    UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"StoreServiceSaveMoneyDefaultCell" forIndexPath:indexPath];
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceSaveMoneyItemModel *item = [self.dataArray safeObjectAtIndex:indexPath.item];
//    //内容总宽度 SCREEN_WIDTH - 24 - 16
    switch (item.itemType) {
        case FNFreshStoreServiceSaveMoneyHorizontalCoupon: {
            return CGSizeMake(self.contentViewWidth, floor(107*Ratio));
        }
        case FNFreshStoreServiceSaveMoneyCouponsAndLimit: {
            return CGSizeMake(self.contentViewWidth, floor(117*Ratio)+32);
        }
        case FNFreshStoreServiceSaveMoneyPicActivity: {
            return CGSizeMake(self.contentViewWidth, floor(128*Ratio));
        }
        default:
            break;
    }
    return CGSizeMake(0, 0);
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return 0.0f;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 6.0f;
}
@end
