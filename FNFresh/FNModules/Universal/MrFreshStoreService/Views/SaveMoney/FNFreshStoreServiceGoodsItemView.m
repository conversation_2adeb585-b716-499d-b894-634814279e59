//
//  FNFreshStoreServiceGoodsItemView.m
//  FNFresh
//
//  Created by xn on 2022/1/20.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceGoodsItemView.h"
#import "FNTagLabel.h"
#import <FNTag.h>
#import "UIFont+FontType.h"

@interface FNFreshStoreServiceGoodsItemView()

@property(nonatomic, strong) UIImageView *goodsImgView;
@property(nonatomic, strong) FNTagLabel *goodsTagLabel;
@property(nonatomic, strong) UILabel *goodsNameLabel;
@property(nonatomic, strong) UILabel *goodsPriceLabel;

@end

@implementation FNFreshStoreServiceGoodsItemView

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        [self setupBasicViews];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupBasicViews];
    }
    return self;
}

- (void)setupBasicViews {
    WS(weakSelf);
    self.goodsImgView = [UIImageView new];
    self.goodsImgView.layer.cornerRadius = 10;
    self.goodsImgView.clipsToBounds = true;
    self.goodsImgView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
    [self addSubview:self.goodsImgView];
    [self.goodsImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(-16));
        make.top.left.equalTo(@(0));
        make.height.equalTo(weakSelf.goodsImgView.mas_width).multipliedBy(1);
    }];
    
    self.goodsTagLabel = [FNTagLabel new];
    self.goodsTagLabel.layer.borderColor = [UIColor hex:@"#FF2B1F"].CGColor;
    self.goodsTagLabel.layer.borderWidth = 0.5;
    self.goodsTagLabel.layer.cornerRadius = 3.0;
    self.goodsTagLabel.textColor =  [UIColor hex:@"#FF2B1F"];
    self.goodsTagLabel.font = [UIFont fnSystemRatioFontOfSize:10];
    self.goodsTagLabel.textAlignment = NSTextAlignmentCenter;
    self.goodsTagLabel.backgroundColor = [UIColor whiteColor];
    self.goodsTagLabel.clipsToBounds = true;
    [self addSubview:self.goodsTagLabel];
    [self.goodsTagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.equalTo(weakSelf.goodsImgView);
        make.height.equalTo(@(14*Ratio));
    }];
    
    self.goodsNameLabel = [UILabel new];
    self.goodsNameLabel.font = [UIFont fnSystemRatioFontOfSize:12];
    self.goodsNameLabel.numberOfLines = 2;
    self.goodsNameLabel.textColor = [UIColor blackColor];
    [self addSubview:self.goodsNameLabel];
    [self.goodsNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@(0));
        make.top.equalTo(weakSelf.goodsImgView.mas_bottom).offset(4);
    }];
    
    self.goodsPriceLabel = [UILabel new];
    [self addSubview:self.goodsPriceLabel];
    [self.goodsPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@(0));
        make.bottom.equalTo(@(0));
    }];
}

- (void)setDataWithGoodsModel:(FNFreshStoreServiceGoodsItemModel *)model {
    [self.goodsImgView fn_setImageWithURL:[NSURL URLWithString:model.goodsImg]
                              placeholder:[UIImage fnFresh_imageNamed:@"home_fake_selected"]];
    self.goodsNameLabel.text = model.goodsName;
//    [self.goodsPriceLabel fresh_setPrice:model.price unit:@"" priceTypeKey:kFD_4 priceColor:[UIColor fn_colorWithColorKey:kFNThemeKey] isUnderLine:NO isAddObliqueLine:NO screenRatio:Ratio];
    self.goodsTagLabel.text = [NSString stringWithFormat:@" %@ ",model.label];
    
    NSDictionary *priceParams = @{
           kPriceTypeKey: kFD_4,
           kPriceColorKey: [UIColor fn_colorWithColorKey:kFNPriceKey],
           kPriceUnderLineKey:@(NO),
           kPriceAddObliqueLineKey:@(NO),
           kMoneyMarkFontKey:[UIFont fnHarmonyFontOfSize:11*Ratio weight:UIFontWeightBold],
           kIntegerFontKey:[UIFont fnHarmonyFontOfSize:16*Ratio weight:UIFontWeightBold],
           kDecimalFontKey:[UIFont fnHarmonyFontOfSize:11*Ratio weight:UIFontWeightBold]
       };
    [self.goodsPriceLabel fresh_setPrice:model.price params:priceParams];

}

@end
