//
//  FNFreshStoreServiceCouponsAndLimitCell.h
//  FNFresh
//
//  Created by xn on 2022/1/20.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshStoreServiceModel.h"

@interface FNFreshStoreServiceCouponsAndLimitCell : UICollectionViewCell

@property (nonatomic, copy) void(^handleClickCouponModule)(NSString *url);
@property (nonatomic, copy) void(^handleClickGoodsItem)(NSString *toastMessage);

- (void)setDataWithCouponCenterModel:(FNFreshStoreServiceCouponCenterModel *)couponCenterModel
                  limitPurchaseModel:(FNFreshStoreServiceTodayLimitModel *)limitPurchaseModel
                  countDowntimeBlock:(void (^)(long t))countDowntimeBlock;

@end

