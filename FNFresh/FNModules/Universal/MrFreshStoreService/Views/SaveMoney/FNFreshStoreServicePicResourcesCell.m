//
//  FNFreshStoreServicePicResourcesCell.m
//  FNFresh
//
//  Created by xn on 2022/1/19.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServicePicResourcesCell.h"

@interface FNFreshStoreServicePicResourcesCell()

@property (weak, nonatomic) IBOutlet UILabel *leftTitleLabel;
@property (weak, nonatomic) IBOutlet UIImageView *leftImageView;

@property (weak, nonatomic) IBOutlet UILabel *rightTitleLabel;
@property (weak, nonatomic) IBOutlet UIImageView *rightImageView;

@property (strong, nonatomic) NSArray<FNFreshStoreServiceBannerItemModel *> *picList;

@end

@implementation FNFreshStoreServicePicResourcesCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.leftImageView.userInteractionEnabled = true;
    UITapGestureRecognizer *leftTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleClickLeftImageItem)];
    [self.leftImageView addGestureRecognizer:leftTap];
    
    self.rightImageView.userInteractionEnabled = true;
    UITapGestureRecognizer *rightTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleClickRightImageItem)];
    [self.rightImageView addGestureRecognizer:rightTap];
    
    self.leftImageView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
    self.rightImageView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
}

- (void)setDateWith:(NSArray<FNFreshStoreServiceBannerItemModel *> *)picList {
    self.picList = picList;
    if (picList.count >= 2) {
        FNFreshStoreServiceBannerItemModel *itemFirst = picList.firstObject;
        NSString *firstTitle = itemFirst.title;
        if (itemFirst.title.length > 5) {
            firstTitle = [NSString stringWithFormat:@"%@", [firstTitle substringToIndex:5]];
        }
        self.leftTitleLabel.text = firstTitle;
        [self.leftImageView fn_setImageWithURL:[NSURL URLWithString:itemFirst.img]
                                   placeholder:[UIImage fnFresh_imageNamed:@"home_fake_selected"]];

        FNFreshStoreServiceBannerItemModel *itemSec = [picList safeObjectAtIndex:1];
        NSString *secTitle = itemSec.title;
        if (itemSec.title.length > 5) {
            secTitle = [NSString stringWithFormat:@"%@", [secTitle substringToIndex:5]];
        }
        self.rightTitleLabel.text = secTitle;
        [self.rightImageView fn_setImageWithURL:[NSURL URLWithString:itemSec.img]
                                    placeholder:[UIImage fnFresh_imageNamed:@"home_fake_selected"]];
    }
}

- (void)handleClickLeftImageItem {
    if (self.imgItemClick) {
        if (self.picList.count >= 1) {
            self.imgItemClick(self.picList.firstObject.url,false);
        }
    }
}

- (void)handleClickRightImageItem {
    if (self.imgItemClick) {
        if (self.picList.count >= 2) {
            FNFreshStoreServiceBannerItemModel *itemSec = [self.picList safeObjectAtIndex:1];
            self.imgItemClick(itemSec.url,true);
        }
    }
}


@end
