<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceSaveMoneyCell">
            <rect key="frame" x="0.0" y="0.0" width="369" height="206"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="369" height="206"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ipJ-gm-paa">
                        <rect key="frame" x="0.0" y="0.0" width="369" height="44"/>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cma-an-SM5">
                        <rect key="frame" x="0.0" y="44" width="369" height="162"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="5jy-VJ-p4R">
                                <rect key="frame" x="8" y="0.0" width="353" height="154"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="zG8-cf-Jvj">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </collectionView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="5jy-VJ-p4R" secondAttribute="trailing" constant="8" id="2FT-dU-LYQ"/>
                            <constraint firstAttribute="bottom" secondItem="5jy-VJ-p4R" secondAttribute="bottom" constant="8" id="FRf-S7-jfe"/>
                            <constraint firstItem="5jy-VJ-p4R" firstAttribute="leading" secondItem="cma-an-SM5" secondAttribute="leading" constant="8" id="dh9-o5-JNh"/>
                            <constraint firstItem="5jy-VJ-p4R" firstAttribute="top" secondItem="cma-an-SM5" secondAttribute="top" id="oPv-dr-jUZ"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="ipJ-gm-paa" firstAttribute="width" secondItem="ipJ-gm-paa" secondAttribute="height" multiplier="351:42" id="2dj-ZX-pXj"/>
                <constraint firstItem="ipJ-gm-paa" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="30o-0K-IlM"/>
                <constraint firstItem="ipJ-gm-paa" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="C0r-VW-ykk"/>
                <constraint firstAttribute="bottom" secondItem="cma-an-SM5" secondAttribute="bottom" id="DId-et-GUu"/>
                <constraint firstItem="cma-an-SM5" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="W2O-dk-G74"/>
                <constraint firstAttribute="trailing" secondItem="cma-an-SM5" secondAttribute="trailing" id="WIW-v2-lfx"/>
                <constraint firstAttribute="trailing" secondItem="ipJ-gm-paa" secondAttribute="trailing" id="c2p-Oj-mT5"/>
                <constraint firstItem="cma-an-SM5" firstAttribute="top" secondItem="ipJ-gm-paa" secondAttribute="bottom" id="zaK-vX-s0K"/>
            </constraints>
            <size key="customSize" width="369" height="206"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="10"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="bgView" destination="cma-an-SM5" id="oXC-bo-AH3"/>
                <outlet property="collectionView" destination="5jy-VJ-p4R" id="bhQ-sl-MB7"/>
                <outlet property="topImageView" destination="ipJ-gm-paa" id="HcU-o1-8gH"/>
            </connections>
            <point key="canvasLocation" x="225.36231884057972" y="40.178571428571423"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
