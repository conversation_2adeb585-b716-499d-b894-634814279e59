<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServicePicResourcesCell">
            <rect key="frame" x="0.0" y="0.0" width="717" height="157"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="717" height="157"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bix-Dp-rND">
                        <rect key="frame" x="8" y="0.0" width="347.5" height="149"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ekC-8K-8vh">
                                <rect key="frame" x="0.0" y="34" width="347.5" height="115"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ehP-Ek-rLe">
                                <rect key="frame" x="0.0" y="10" width="36" height="17"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                <color key="textColor" red="0.199973762" green="0.2000150383" blue="0.19997116919999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                        <real key="value" value="14"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="ekC-8K-8vh" secondAttribute="trailing" id="2m0-tn-h4R"/>
                            <constraint firstItem="ekC-8K-8vh" firstAttribute="leading" secondItem="bix-Dp-rND" secondAttribute="leading" id="6ZI-uf-Al0"/>
                            <constraint firstItem="ehP-Ek-rLe" firstAttribute="top" secondItem="bix-Dp-rND" secondAttribute="top" constant="10" id="RhM-Jm-N17"/>
                            <constraint firstItem="ekC-8K-8vh" firstAttribute="top" secondItem="bix-Dp-rND" secondAttribute="top" constant="34" id="Spf-zK-Biq"/>
                            <constraint firstItem="ehP-Ek-rLe" firstAttribute="leading" secondItem="bix-Dp-rND" secondAttribute="leading" id="aNg-aL-s59"/>
                            <constraint firstAttribute="bottom" secondItem="ekC-8K-8vh" secondAttribute="bottom" id="zDD-bv-fvT"/>
                        </constraints>
                    </view>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8aN-z7-M2f">
                        <rect key="frame" x="361.5" y="0.0" width="347.5" height="149"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="inm-UW-56A">
                                <rect key="frame" x="0.0" y="10" width="36" height="17"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                                <color key="textColor" red="0.199973762" green="0.2000150383" blue="0.19997116919999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                        <real key="value" value="14"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="EiU-eI-Mtd">
                                <rect key="frame" x="0.0" y="34" width="347.5" height="115"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="10"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="EiU-eI-Mtd" firstAttribute="leading" secondItem="8aN-z7-M2f" secondAttribute="leading" id="4ok-Ji-tFR"/>
                            <constraint firstAttribute="bottom" secondItem="EiU-eI-Mtd" secondAttribute="bottom" id="7Dq-az-J8v"/>
                            <constraint firstAttribute="trailing" secondItem="EiU-eI-Mtd" secondAttribute="trailing" id="8vx-Am-xZ7"/>
                            <constraint firstItem="inm-UW-56A" firstAttribute="top" secondItem="8aN-z7-M2f" secondAttribute="top" constant="10" id="VbM-kw-krb"/>
                            <constraint firstItem="EiU-eI-Mtd" firstAttribute="top" secondItem="8aN-z7-M2f" secondAttribute="top" constant="34" id="VfV-qP-r7n"/>
                            <constraint firstItem="inm-UW-56A" firstAttribute="leading" secondItem="8aN-z7-M2f" secondAttribute="leading" id="iEy-yc-iae"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="bix-Dp-rND" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="GbK-EG-HnK"/>
                <constraint firstItem="8aN-z7-M2f" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="a2v-JF-eGr"/>
                <constraint firstItem="8aN-z7-M2f" firstAttribute="width" secondItem="bix-Dp-rND" secondAttribute="width" id="b46-sf-tpw"/>
                <constraint firstAttribute="trailing" secondItem="8aN-z7-M2f" secondAttribute="trailing" constant="8" id="e5O-Q6-bNu"/>
                <constraint firstAttribute="bottom" secondItem="bix-Dp-rND" secondAttribute="bottom" constant="8" id="mVj-5I-Hfl"/>
                <constraint firstItem="bix-Dp-rND" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="8" id="wKj-w6-hby"/>
                <constraint firstAttribute="bottom" secondItem="8aN-z7-M2f" secondAttribute="bottom" constant="8" id="wiT-Me-9iI"/>
                <constraint firstItem="8aN-z7-M2f" firstAttribute="leading" secondItem="bix-Dp-rND" secondAttribute="trailing" constant="6" id="wqV-Uq-pIm"/>
            </constraints>
            <size key="customSize" width="717" height="157"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="6"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="leftImageView" destination="ekC-8K-8vh" id="6W0-Je-pxB"/>
                <outlet property="leftTitleLabel" destination="ehP-Ek-rLe" id="8n6-GC-iQf"/>
                <outlet property="rightImageView" destination="EiU-eI-Mtd" id="R5G-nS-45e"/>
                <outlet property="rightTitleLabel" destination="inm-UW-56A" id="ZxY-cL-qAy"/>
            </connections>
            <point key="canvasLocation" x="418.11594202898556" y="87.388392857142847"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
