//
//  FNFreshStoreServiceCouponsAndLimitCell.m
//  FNFresh
//
//  Created by xn on 2022/1/20.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceCouponsAndLimitCell.h"
#import "FNFreshNewCouponFadeInOutView.h"
#import "FNFreshStoreServiceGoodsItemView.h"
#import "FNFreshNewCouponCenterChildView.h"

@interface FNFreshStoreServiceCouponsAndLimitCell()
@property (weak, nonatomic) IBOutlet UILabel *couponCenterTitleLabel;
@property (weak, nonatomic) IBOutlet UILabel *couponCenterSubTitleLabel;
//@property (weak, nonatomic) IBOutlet FNFreshNewCouponFadeInOutView *couponsView;
@property (weak, nonatomic) IBOutlet FNFreshNewCouponCenterChildView *leftCouponView;
@property (weak, nonatomic) IBOutlet FNFreshNewCouponCenterChildView *rightCouponView;

@property (weak, nonatomic) IBOutlet UILabel *limitPurchaseTitleLabel;
@property (weak, nonatomic) IBOutlet UIView *timeBgView;
@property (weak, nonatomic) IBOutlet UILabel *timeLabel;
@property (weak, nonatomic) IBOutlet FNFreshStoreServiceGoodsItemView *leftGoodsView;
@property (weak, nonatomic) IBOutlet FNFreshStoreServiceGoodsItemView *rightGoodsView;

//当日限定
@property (copy, nonatomic) NSString *toast;     //当日限定toast
@property (copy, nonatomic) NSArray<FNFreshStoreServiceGoodsItemModel *> *goodsList;

//优惠券
@property (copy, nonatomic) NSString *couponCenterHotLinkUrl;     //热区跳转链接
@property (copy, nonatomic) NSArray<FNMrFreshPopupWindowNewGuidanceItemModel *> *couponList;

@property (strong, nonatomic) NSTimer *timer;
@property (assign, nonatomic) NSUInteger goodsIndex;
@property (assign, nonatomic) NSUInteger couponIndex;
@property (assign, nonatomic) BOOL isCouponAnimation;
@property (assign, nonatomic) BOOL isGoodsAnimation;

//活动倒计时
@property (nonatomic, strong) dispatch_source_t activityTimer;

@end

@implementation FNFreshStoreServiceCouponsAndLimitCell

- (void)awakeFromNib {
    [super awakeFromNib];
        
    self.leftGoodsView.userInteractionEnabled = true;
    UITapGestureRecognizer *letTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleClickLimitModuleGoodsItem)];
    [self.leftGoodsView addGestureRecognizer:letTap];
    
    self.rightGoodsView.userInteractionEnabled = true;
    UITapGestureRecognizer *rightTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleClickLimitModuleGoodsItem)];
    [self.rightGoodsView addGestureRecognizer:rightTap];
    
    self.leftCouponView.userInteractionEnabled = true;
    UITapGestureRecognizer *letCouponTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleClickCouponCenterModule)];
    [self.leftCouponView addGestureRecognizer:letCouponTap];
    
    self.rightCouponView.userInteractionEnabled = true;
    UITapGestureRecognizer *rightCouponTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(handleClickCouponCenterModule)];
    [self.rightCouponView addGestureRecognizer:rightCouponTap];
}

- (void)setDataWithCouponCenterModel:(FNFreshStoreServiceCouponCenterModel *)couponCenterModel
                  limitPurchaseModel:(FNFreshStoreServiceTodayLimitModel *)limitPurchaseModel
                  countDowntimeBlock:(void (^)(long t))countDowntimeBlock {
    self.toast = limitPurchaseModel.toast;
    self.couponCenterHotLinkUrl = couponCenterModel.hotLinkUrl;
    
    //领券中心
    self.couponCenterTitleLabel.text = couponCenterModel.moduleName;
    if (couponCenterModel.moduleSubTitle.length > 0) {
        self.couponCenterSubTitleLabel.text = [NSString stringWithFormat:@"  %@  ", couponCenterModel.moduleSubTitle];
    } else {
        self.couponCenterSubTitleLabel.text = @"";
    }
    if (couponCenterModel.couponList.count % 2) {
        self.couponList = [couponCenterModel.couponList subarrayWithRange:NSMakeRange(0, couponCenterModel.couponList.count - 1)];
    } else {
        self.couponList = couponCenterModel.couponList;
    }
    self.couponIndex = 0;
    [self dealWithCouponList];
    
    //今日限定
    self.limitPurchaseTitleLabel.text = limitPurchaseModel.title;
    if (limitPurchaseModel.goodsList.count % 2) {
        self.goodsList = [limitPurchaseModel.goodsList subarrayWithRange:NSMakeRange(0, limitPurchaseModel.goodsList.count - 1)];
    } else {
        self.goodsList = limitPurchaseModel.goodsList;
    }
    self.goodsIndex = 0;
    [self dealWithGoodsList];
    
    self.isCouponAnimation = self.couponList.count > 2;
    self.isGoodsAnimation = self.goodsList.count > 2;
    if (self.isCouponAnimation || self.isGoodsAnimation) {
        [self createTimer];
    } else {
        [self invalidateTimer];
    }
    
    //活动倒计时
    __block long countNum = limitPurchaseModel.countDownTime;
   
    WS(weakSelf)
    if (countNum <= 0) {
        self.timeBgView.hidden = YES;
        [self cancelActivityTimer];
        return;
    }
    
    self.timeBgView.hidden = NO;
    self.timeLabel.text = [self convertTimeToTimeString:countNum];
    [self setupTimerWithBlock:^{
        if (countNum > 1) {
            countNum --;
            countDowntimeBlock(countNum);
            dispatch_async(dispatch_get_main_queue(), ^{
                weakSelf.timeLabel.text = [weakSelf convertTimeToTimeString:countNum];
            });
        } else {
            [weakSelf activityTimeOutAction];
            [weakSelf cancelActivityTimer];
        }
    }];
}
   
//点击领券中心模块
- (void)handleClickCouponCenterModule {
    if (self.handleClickCouponModule) {
        self.handleClickCouponModule(self.couponCenterHotLinkUrl);
    }
}

//点击限定商品
- (void)handleClickLimitModuleGoodsItem {
    if (self.handleClickGoodsItem) {
        self.handleClickGoodsItem(self.toast);
    }
}

#pragma mark animation
- (void)dealWithCouponList {
    if (self.couponIndex + 1 >= self.couponList.count) {
        self.couponIndex = 0;
    }
    self.leftCouponView.coupon = [self.couponList safeObjectAtIndex:self.couponIndex];
    self.rightCouponView.coupon = [self.couponList safeObjectAtIndex:self.couponIndex + 1];
    self.couponIndex += 2;
}

- (void)dealWithGoodsList {
    if (self.goodsIndex + 1 >= self.goodsList.count) {
        self.goodsIndex = 0;
    }
    [self.leftGoodsView setDataWithGoodsModel:[self.goodsList safeObjectAtIndex:self.goodsIndex]];
    [self.rightGoodsView setDataWithGoodsModel:[self.goodsList safeObjectAtIndex:self.goodsIndex + 1]];
    self.goodsIndex += 2;
}

- (void)dealAnimationCompletion {
    if (self.isGoodsAnimation) {
        [self dealWithGoodsList];
    }
    if (self.isCouponAnimation) {
        [self dealWithCouponList];
    }
}

- (void)createTimer {
    if (self.timer.isValid) {
        return;
    }
    self.timer = [NSTimer scheduledTimerWithTimeInterval:3 target:self selector:@selector(timerRun) userInfo:nil repeats:YES];
}

- (void)invalidateTimer {
    if (!self.timer.isValid) {
        return;
    }
    [self.timer invalidate];
    self.timer = nil;
}

- (void)timerRun {
    [UIView animateWithDuration:0.35 animations:^{
        if (self.isCouponAnimation) {
            self.leftCouponView.transform = CGAffineTransformMakeScale(0.89, 0.89);
            self.leftCouponView.alpha = 0;
            self.rightCouponView.transform = CGAffineTransformMakeScale(0.89, 0.89);
            self.rightCouponView.alpha = 0;
        }
        if (self.isGoodsAnimation) {
            self.leftGoodsView.transform = CGAffineTransformMakeScale(0.89, 0.89);
            self.leftGoodsView.alpha = 0;
            self.rightGoodsView.transform = CGAffineTransformMakeScale(0.89, 0.89);
            self.rightGoodsView.alpha = 0;
        }
    } completion:^(BOOL finished) {
        [self dealAnimationCompletion];
        [UIView animateWithDuration:0.35 animations:^{
            if (self.isCouponAnimation) {
                self.leftCouponView.transform = CGAffineTransformIdentity;
                self.leftCouponView.alpha = 1;
                self.rightCouponView.transform = CGAffineTransformIdentity;
                self.rightCouponView.alpha = 1;
            }
            if (self.isGoodsAnimation) {
                self.leftGoodsView.transform = CGAffineTransformIdentity;
                self.leftGoodsView.alpha = 1;
                self.rightGoodsView.transform = CGAffineTransformIdentity;
                self.rightGoodsView.alpha = 1;
            }
        }];
    }];
}

//限时活动倒计时
- (void)setupTimerWithBlock:(dispatch_block_t)block {
    [self cancelActivityTimer];
    
    self.activityTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0));
    dispatch_source_set_timer(self.activityTimer, DISPATCH_TIME_NOW, 1 * NSEC_PER_SEC, 0 * NSEC_PER_SEC);
    dispatch_source_set_event_handler(self.activityTimer, block);
    dispatch_resume(self.activityTimer);
}


- (void)cancelActivityTimer {
    if (self.activityTimer) {
        dispatch_source_cancel(self.activityTimer);
        self.activityTimer = nil;
    }
}

- (void)activityTimeOutAction {
    WS(weakSelf)
    dispatch_async(dispatch_get_main_queue(), ^{
        weakSelf.timeBgView.hidden = YES;
    });
}

//时间戳变为格式时间
- (NSString *)convertTimeToTimeString:(long)time {
    long long seconds = time;
    NSString *str_hour = [NSString stringWithFormat:@"%02lld",seconds/3600];
    NSString *str_minute = [NSString stringWithFormat:@"%02lld",(seconds%3600)/60];
    NSString *str_second = [NSString stringWithFormat:@"%02lld",seconds%60];
    return [NSString stringWithFormat:@"%@:%@:%@",str_hour, str_minute, str_second];
}

@end
