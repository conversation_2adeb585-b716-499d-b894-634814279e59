<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceCouponsCell">
            <rect key="frame" x="0.0" y="0.0" width="392" height="169"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="392" height="169"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Oko-vN-tsU">
                        <rect key="frame" x="0.0" y="0.0" width="392" height="169"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="今日神券" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lKn-JR-EWA">
                                <rect key="frame" x="8" y="8" width="53.5" height="16"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="13"/>
                                <color key="textColor" red="0.199973762" green="0.2000150383" blue="0.19997116919999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                        <real key="value" value="14"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=" 省钱利器 " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rk0-wJ-QCO">
                                <rect key="frame" x="67.5" y="8" width="51.5" height="16"/>
                                <color key="backgroundColor" red="0.97841697930000004" green="0.3674263358" blue="0.35715115069999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="16" id="yRi-jT-dPl"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="8"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                        <real key="value" value="11"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_index_more_01" translatesAutoresizingMaskIntoConstraints="NO" id="SPY-D6-tLE">
                                <rect key="frame" x="372" y="10" width="12" height="12"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="12" id="Nxn-As-Gmr"/>
                                    <constraint firstAttribute="height" constant="12" id="qNu-xA-B4T"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="更多" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uth-Mb-UP9">
                                <rect key="frame" x="345" y="9" width="25" height="14.5"/>
                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                <color key="textColor" red="0.199973762" green="0.2000150383" blue="0.19997116919999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                        <real key="value" value="12"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qHE-0v-Dbn">
                                <rect key="frame" x="0.0" y="0.0" width="392" height="169"/>
                                <connections>
                                    <action selector="contentViewClick:" destination="gTV-IL-0wX" eventType="touchUpInside" id="w7i-A8-6Sp"/>
                                </connections>
                            </button>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="rB2-mj-yoQ">
                                <rect key="frame" x="0.0" y="34" width="392" height="127"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="Kzh-6q-dA7">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <connections>
                                    <outlet property="dataSource" destination="gTV-IL-0wX" id="K9b-mF-xLQ"/>
                                    <outlet property="delegate" destination="gTV-IL-0wX" id="3Iv-Ia-WDh"/>
                                </connections>
                            </collectionView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="rB2-mj-yoQ" secondAttribute="trailing" id="0T3-sJ-LNM"/>
                            <constraint firstItem="Rk0-wJ-QCO" firstAttribute="centerY" secondItem="lKn-JR-EWA" secondAttribute="centerY" id="2Gw-z7-RkC"/>
                            <constraint firstItem="lKn-JR-EWA" firstAttribute="leading" secondItem="Oko-vN-tsU" secondAttribute="leading" constant="8" id="3Ow-rB-zSW"/>
                            <constraint firstItem="qHE-0v-Dbn" firstAttribute="leading" secondItem="Oko-vN-tsU" secondAttribute="leading" id="E7a-4s-bw7"/>
                            <constraint firstItem="Rk0-wJ-QCO" firstAttribute="leading" secondItem="lKn-JR-EWA" secondAttribute="trailing" constant="6" id="JMq-G8-sAQ"/>
                            <constraint firstAttribute="bottom" secondItem="qHE-0v-Dbn" secondAttribute="bottom" id="M0T-xZ-xqQ"/>
                            <constraint firstItem="lKn-JR-EWA" firstAttribute="top" secondItem="Oko-vN-tsU" secondAttribute="top" constant="8" id="ROh-zm-j3n"/>
                            <constraint firstAttribute="bottom" secondItem="rB2-mj-yoQ" secondAttribute="bottom" constant="8" id="UPV-Ac-jfj"/>
                            <constraint firstAttribute="trailing" secondItem="SPY-D6-tLE" secondAttribute="trailing" constant="8" id="en5-k5-pjZ"/>
                            <constraint firstItem="SPY-D6-tLE" firstAttribute="centerY" secondItem="lKn-JR-EWA" secondAttribute="centerY" id="i7H-PF-fNI"/>
                            <constraint firstItem="rB2-mj-yoQ" firstAttribute="leading" secondItem="Oko-vN-tsU" secondAttribute="leading" id="pSd-Pg-ZUH"/>
                            <constraint firstItem="rB2-mj-yoQ" firstAttribute="top" secondItem="Oko-vN-tsU" secondAttribute="top" constant="34" id="qLM-pD-DTr"/>
                            <constraint firstItem="qHE-0v-Dbn" firstAttribute="top" secondItem="Oko-vN-tsU" secondAttribute="top" id="raa-SP-QKF"/>
                            <constraint firstItem="uth-Mb-UP9" firstAttribute="centerY" secondItem="lKn-JR-EWA" secondAttribute="centerY" id="uhK-go-ouH"/>
                            <constraint firstItem="SPY-D6-tLE" firstAttribute="leading" secondItem="uth-Mb-UP9" secondAttribute="trailing" constant="2" id="v0q-ry-of4"/>
                            <constraint firstAttribute="trailing" secondItem="qHE-0v-Dbn" secondAttribute="trailing" id="y4h-xH-lic"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="Oko-vN-tsU" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="JK1-Ei-MxM"/>
                <constraint firstAttribute="trailing" secondItem="Oko-vN-tsU" secondAttribute="trailing" id="LK4-3w-Rhn"/>
                <constraint firstAttribute="bottom" secondItem="Oko-vN-tsU" secondAttribute="bottom" id="fGe-sU-a0g"/>
                <constraint firstItem="Oko-vN-tsU" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="w6Y-Vj-cE5"/>
            </constraints>
            <size key="customSize" width="392" height="169"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="6"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="collectionView" destination="rB2-mj-yoQ" id="hFP-NB-phY"/>
                <outlet property="subTitleLabel" destination="Rk0-wJ-QCO" id="vyP-4K-0I6"/>
                <outlet property="titleLabel" destination="lKn-JR-EWA" id="PJ3-ZE-YIk"/>
            </connections>
            <point key="canvasLocation" x="566.66666666666674" y="10.379464285714285"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="icon_index_more_01" width="7" height="12"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
