//
//  FNFreshStoreServiceBaseCell.h
//  FNFresh
//
//  Created by xn on 2022/1/17.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshStoreServiceSectionModel.h"

@class FNFreshStoreServiceItemModel;
@class FNFreshStoreServiceBaseCell;

@protocol FNMrFreshStoreServiceCellDelegate <NSObject>

@optional

//今日排行榜 item
- (void)storeServiceCellDidClickHotSellingItemWithPondId:(NSString *)pondId index:(NSInteger)index;

//新版商店街 item
- (void)storeServiceCellDidClickNewShoppingStreetItemWithUrl:(NSString *)url index:(NSInteger)index;

//今日省钱
//点击 领券模块
- (void)storeServiceCellDidClickCouponCenterWithUrl:(NSString *)url;

//点击图片资源
- (void)storeServiceCellDidClickPicResourcesWithUrl:(NSString *)url isRightItem:(BOOL)isRight;

//点击当日限定商品item
- (void)storeServiceCellDidClickLimitPurchaseGoodsItem:(NSString *)toastMessage;

//点击活动报名或者促销海报 FNFreshMrFreshStoreTypeApplyAndPoster
- (void)storeServiceCellDidClickApplyAndPosterWithUrl:(NSString *)url isApply:(BOOL)isApply;

//banner 更新图片高度
- (void)storeServiceCellBannerUpdateHeight:(FNFreshStoreServiceBaseCell *)cell indexPath:(NSIndexPath *)indexPath;

@end


@interface FNFreshStoreServiceBaseCell : UICollectionViewCell

@property (nonatomic, weak) id <FNMrFreshStoreServiceCellDelegate> delegate;
@property (nonatomic, weak) FNFreshStoreServiceSectionModel *sectionModel;

@end


