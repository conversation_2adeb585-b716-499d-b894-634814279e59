//
//  FNFreshStoreServicesHeaderAddressCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/29.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServicesHeaderAddressCell.h"
#import "UILabel+factory.h"
#import "FNFreshStoreServicesHeaderMemberView.h"
#import "UIView+Corners.h"
#import "UIDevice+FNScreenSize.h"
@interface FNFreshStoreServicesHeaderAddressCell()

@property (nonatomic,strong)FNFreshStoreServicesHeaderAddressView *addressView;

@property (nonatomic,strong)FNFreshImageLabelStyleView *phoneView;

@property (nonatomic,strong)FNFreshImageLabelStyleView *infoView;

@property (nonatomic,strong)FNFreshStoreServicesHeaderMemberView *memberView;

@end

@implementation FNFreshStoreServicesHeaderAddressCell

- (void)setDataModel:(FNFreshStoreServicesHeaderAddressCellDataModel *)dataModel{
    _dataModel = dataModel;
    self.isShowPhone = dataModel.isShowPhone;
    self.isShowAddress = dataModel.isShowAddress;
    self.addressView.dataModel = dataModel;
    self.memberView.dataModel = dataModel;
}

- (void)setIsShowPhone:(BOOL)isShowPhone{
    _isShowPhone = isShowPhone;
    self.phoneView.hidden = !isShowPhone;
}

- (void)setIsShowAddress:(BOOL)isShowAddress{
    _isShowAddress = isShowAddress;
    self.addressView.hidden = !isShowAddress;
}

- (void)addChildView{
    [super addChildView];
    
    [self.containerView addSubview:self.addressView];
    [self.addressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.containerView.mas_leading).mas_offset(12);
        make.top.mas_equalTo(self.contentView.mas_top).mas_offset(10);
        make.trailing.mas_lessThanOrEqualTo(self.contentView.mas_trailing).mas_offset(-100);
    }];
    
    [self.containerView addSubview:self.infoView];
    [self.infoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.containerView.mas_top).mas_offset(7);
        make.trailing.mas_equalTo(self.containerView.mas_trailing).mas_offset(-12);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
    
    [self.containerView addSubview:self.phoneView];
    [self.phoneView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.infoView);
        make.trailing.mas_equalTo(self.infoView.mas_leading).mas_offset(-12);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
    
    [self.containerView addSubview:self.memberView];
    [self.memberView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.containerView).mas_offset(12);
        make.trailing.mas_equalTo(self.containerView).mas_offset(-12);
        make.top.mas_equalTo(self.infoView.mas_bottom).mas_offset(88);
        make.height.mas_equalTo(100).priorityHigh();
    }];
    
}

- (void)setBindEvent{
    [super setBindEvent];
    __weak FNFreshStoreServicesHeaderAddressCell *weakSelf = self;
    self.addressView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventAddressModuleClick object:nil userInfo:nil];
    };
    
    self.phoneView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventPhoneButtonClick object:nil userInfo:nil];
    };
    self.infoView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventInfoButtonClick object:nil userInfo:nil];
    };
    
}

- (void)layoutSubviews{
    [super layoutSubviews];
    [self.memberView cutCornerRadius:10 bounds:self.memberView.bounds rectCorner:UIRectCornerTopLeft | UIRectCornerTopRight];
}

- (FNFreshStoreServicesHeaderAddressView *)addressView{
    if (!_addressView){
        _addressView = [FNFreshStoreServicesHeaderAddressView new];
    }
    return _addressView;
}

- (FNFreshImageLabelStyleView *)phoneView{
    if (!_phoneView){
        _phoneView = [FNFreshImageLabelStyleView new];
        _phoneView.style = FNFreshImageLabelStyleOnlyImage;
        [_phoneView setImage:[UIImage imageNamed:@"store_service_icon_phone"] forState:UIControlStateNormal];
    }
    return _phoneView;
}

- (FNFreshImageLabelStyleView *)infoView{
    if (!_infoView){
        _infoView = [FNFreshImageLabelStyleView new];
        _infoView.style = FNFreshImageLabelStyleOnlyImage;
        [_infoView setImage:[UIImage imageNamed:@"store_service_icon_info"] forState:UIControlStateNormal];
    }
    return _infoView;
}

- (FNFreshStoreServicesHeaderMemberView *)memberView{
    if (!_memberView){
        _memberView = [FNFreshStoreServicesHeaderMemberView new];
        _memberView.backgroundColor = [UIColor colorWithColor:[UIColor fn_colorWithHex:@"#FFFFFF"] alpha:0.9];
    }
    return _memberView;
}

@end

@interface FNFreshStoreServicesHeaderAddressView()

@property (nonatomic,strong)FNFreshImageLabelStyleView *addressBtn;

@property (nonatomic,strong)UILabel *addressLabel;

@end

@implementation FNFreshStoreServicesHeaderAddressView


- (void)setDataModel:(FNFreshStoreServicesHeaderAddressCellDataModel *)dataModel{
    _dataModel = dataModel;
    self.titleString = dataModel.titleString;
    self.subTitleString = dataModel.subTitleString;
}

- (void)setTitleString:(NSString *)titleString{
    _titleString = titleString;
    [self.addressBtn setTitle:titleString forState:UIControlStateNormal];
}

- (void)setSubTitleString:(NSString *)subTitleString{
    _subTitleString = subTitleString;
    self.addressLabel.text = subTitleString;
}

- (void)addChildView{
    [super addChildView];
    
    self.clipsToBounds = NO;
    
    [self addSubview:self.addressBtn];
    [self.addressBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.mas_equalTo(self);
        make.trailing.mas_equalTo(self);
    }];
    [self addSubview:self.addressLabel];
    [self.addressLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.bottom.mas_equalTo(self);
        make.width.mas_lessThanOrEqualTo(SCREEN_WIDTH - 28);
        make.top.mas_equalTo(self.addressBtn.mas_bottom).mas_offset(2);
    }];
}

- (void)setBindEvent{
    [super setBindEvent];
    __weak FNFreshStoreServicesHeaderAddressView *weakSelf = self;
    self.addressBtn.clickControlBlock = ^{
        if (weakSelf.clickControlBlock){
            weakSelf.clickControlBlock();
        }
    };
}

- (UILabel *)addressLabel{
    if (!_addressLabel){
        _addressLabel = [UILabel labelWithTextColorString:@"#333333" fontSize:12 fontWeight:UIFontWeightRegular];
        _addressLabel.numberOfLines = 1;
        [_addressLabel setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _addressLabel;
}

- (FNFreshImageLabelStyleView *)addressBtn{
    if (!_addressBtn){
        _addressBtn = [FNFreshImageLabelStyleView new];
        _addressBtn.style = FNFreshImageLabelStyleLeftTitleRightImage;
        _addressBtn.spacing = 2;
        _addressBtn.titleLabel.numberOfLines = 1;
        [_addressBtn setTitleColor:[UIColor fn_colorWithHex:@"#333333"] forState:UIControlStateNormal];
        [_addressBtn setImage:[UIImage imageNamed:@"store_service_icon_arrow_down"] forState:UIControlStateNormal];
        [_addressBtn.titleLabel setFont:[UIFont systemFontOfSize:16 weight:UIFontWeightMedium]];
        [_addressBtn setTitle:@"姑嫂树店" forState:UIControlStateNormal];
        [_addressBtn.titleLabel setContentHuggingPriority:UILayoutPriorityFittingSizeLevel forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _addressBtn;
}

@end
