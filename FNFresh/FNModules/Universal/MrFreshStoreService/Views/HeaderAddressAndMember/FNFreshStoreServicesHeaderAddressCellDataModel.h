//
//  FNFreshStoreServicesHeaderAddressCellDataModel.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/29.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FNFreshStoreServicesHeaderAddressCellDataModel : NSObject

/// 是否显示 phone
@property (nonatomic,assign)BOOL isShowPhone;

/// 是否显示地址模块
@property (nonatomic,assign)BOOL isShowAddress;
/**
 地址
 */
@property (nonatomic,strong)NSString *titleString;
/**
 详细地址
 */
@property (nonatomic,strong)NSString *subTitleString;

/**
 本年已享折扣金额9.99w元
 */
@property (nonatomic,strong)NSString *member_titleString;

/**
 是否登录
 */
@property (nonatomic,assign)BOOL member_isShowLogin;

/**
 优惠券
 */
@property (nonatomic,strong)NSString *member_couponsValueString;

/**
 成长值
 */
@property (nonatomic,strong)NSString *member_growUpValueString;

/**
 购物卡
 */
@property (nonatomic,strong)NSString *member_shoppingCardString;

/**
 我的积分
 */
@property (nonatomic,strong)NSString *member_myPointValueString;
@end

NS_ASSUME_NONNULL_END
