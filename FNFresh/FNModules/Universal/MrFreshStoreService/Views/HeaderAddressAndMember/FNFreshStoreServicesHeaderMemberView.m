//
//  FNFreshStoreServicesHeaderMemberView.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/29.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServicesHeaderMemberView.h"
#import "FNFreshImageLabelStyleView.h"
#import "UILabel+factory.h"
#import "UIFont+FontType.h"
#import "FNFreshPriceLabel.h"

@interface FNFreshStoreServicesHeaderMemberView()

/// logo
@property (nonatomic,strong)UIImageView *logoImageView;

/// 标题
@property (nonatomic,strong)UILabel *titleLabel;

/// 去登录
@property (nonatomic,strong)FNFreshImageLabelStyleView *loginView;

/// 我的会员开
@property (nonatomic,strong)FNFreshImageLabelStyleView *myMemberView;

@property (nonatomic,strong)UIStackView *stackView;

/// 优惠券
@property (nonatomic,strong)FNFreshStoreServicesHeaderMemberUnitView *couponsView;

/// 成长值
@property (nonatomic,strong)FNFreshStoreServicesHeaderMemberUnitView *growUpView;

/// 购物卡
@property (nonatomic,strong)FNFreshStoreServicesHeaderMemberUnitView *shoppingCardView;


@end

@implementation FNFreshStoreServicesHeaderMemberView

- (void)setDataModel:(FNFreshStoreServicesHeaderAddressCellDataModel *)dataModel{
    _dataModel = dataModel;
    self.member_titleString = dataModel.member_titleString;
    self.isShowLogin = dataModel.member_isShowLogin;
    self.member_couponsValueString = dataModel.member_couponsValueString;
    self.member_myPointValueString = dataModel.member_myPointValueString;
    self.member_shoppingCardString = dataModel.member_shoppingCardString;
    self.member_growUpValueString = dataModel.member_growUpValueString;
    self.member_myPointValueString = dataModel.member_myPointValueString;
}

- (void)setMember_titleString:(NSString *)member_titleString{
    _member_titleString = member_titleString;
    self.titleLabel.text = member_titleString;
}

- (void)setIsShowLogin:(BOOL)isShowLogin{
    _isShowLogin = isShowLogin;
    self.loginView.hidden = !isShowLogin;
    self.myMemberView.hidden = isShowLogin;
    self.couponsView.isNormal = !self.isShowLogin;
    self.growUpView.isNormal = !self.isShowLogin;
    self.shoppingCardView.isNormal = !self.isShowLogin;
}

- (void)setMember_couponsValueString:(NSString *)member_couponsValueString{
    _member_couponsValueString = member_couponsValueString;
    self.couponsView.topTitleString = member_couponsValueString;
}

- (void)setMember_growUpValueString:(NSString *)member_growUpValueString{
    _member_growUpValueString = member_growUpValueString;
    self.growUpView.topTitleString = member_growUpValueString;
    self.growUpView.topOtherLabel.hidden = NO;
    self.growUpView.topContentLabel.hidden = YES;
    self.growUpView.topOtherLabel.attributedText = [self setText:member_growUpValueString frontFont:22 behindFont:14 textColor:[UIColor fn_colorWithHex:@"#333333"]];
}

- (void)setMember_shoppingCardString:(NSString *)member_shoppingCardString{
    _member_shoppingCardString = member_shoppingCardString;
    self.shoppingCardView.topTitleString = member_shoppingCardString;
}

- (void)initData{
    [super initData];
}

- (void)addChildView{
    [super addChildView];
    
    [self addSubview:self.logoImageView];
    [self.logoImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self).mas_offset(13);
        make.leading.mas_equalTo(self);
        make.size.mas_equalTo(CGSizeMake(59, 22));
    }];
    
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(self.logoImageView);
        make.leading.mas_equalTo(self.logoImageView.mas_trailing).mas_offset(6);
    }];
    
    [self addSubview:self.loginView];
    [self.loginView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.titleLabel.mas_trailing).mas_offset(8);
        make.centerY.mas_equalTo(self.titleLabel);
        make.trailing.mas_lessThanOrEqualTo(self.mas_trailing).mas_offset(-10);
    }];
    
    [self addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.mas_leading).mas_offset(14);
        make.top.mas_equalTo(self.logoImageView.mas_bottom).mas_offset(13.5);
        make.height.mas_equalTo(40.5).priorityHigh();
        make.bottom.mas_equalTo(self).mas_offset(-13);
    }];
    
    [self.stackView addArrangedSubview:self.shoppingCardView];
    [self.shoppingCardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_offset(40.5);
    }];
    
    [self.stackView addArrangedSubview:self.couponsView];
    [self.couponsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_offset(40.5);
    }];
    [self.stackView addArrangedSubview:self.growUpView];
    [self.growUpView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_offset(40.5);
    }];
    
    [self addSubview:self.myMemberView];
    [self.myMemberView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.stackView.mas_trailing);
        make.trailing.mas_equalTo(self.mas_trailing).mas_offset(0);
        make.bottom.mas_equalTo(-12);
        make.width.mas_equalTo(96);
    }];
}

- (void)setBindEvent{
    [super setBindEvent];
    
    __weak FNFreshStoreServicesHeaderMemberView *weakSelf = self;
    self.loginView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventAtOnceLoginClick object:nil userInfo:nil];
    };
    
    self.myMemberView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventMemberShipCardClick object:nil userInfo:nil];
    };
    
    self.couponsView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventCouponsClick object:nil userInfo:nil];
    };
    
    self.growUpView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventGrowUpClick object:nil userInfo:nil];
    };
    
    self.shoppingCardView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventShoppingCardClick object:nil userInfo:nil];
    };
}

- (NSMutableAttributedString *)setText:(NSString *)Text
                            frontFont:(CGFloat)frontFont
                           behindFont:(CGFloat)behindFont
                            textColor:(UIColor *)textColor{
    //分隔字符串
    NSString *lastStr;
    NSString *firstStr;
    
    if (!Text) return [NSMutableAttributedString new];
    
    if ([Text containsString:@"."]) {
        NSRange range = [Text rangeOfString:@"."];
        lastStr = [Text substringFromIndex:range.location];
        firstStr = [Text substringToIndex:range.location];
    }else{
        if ([Text containsString:@"万"]) {
            NSRange range = [Text rangeOfString:@"万"];
            lastStr = [Text substringFromIndex:range.location];
            firstStr = [Text substringToIndex:range.location];
        }else{
            firstStr = Text;
        }
    }

    NSMutableAttributedString *AttributedStr = [[NSMutableAttributedString alloc] initWithString:Text];
    //小数点前面的字体大小
    [AttributedStr addAttribute:NSFontAttributeName
                          value: [UIFont fnHarmonyFontOfSize:frontFont weight:UIFontWeightBold]
                          range:NSMakeRange(0, firstStr.length)];
    //小数点后面的字体大小
    [AttributedStr addAttribute:NSFontAttributeName
                          value:[UIFont fnHarmonyFontOfSize:behindFont weight:UIFontWeightBold]
                          range:NSMakeRange(firstStr.length, lastStr.length)];
    //字符串的颜色
    [AttributedStr addAttribute:NSForegroundColorAttributeName
                          value:textColor
                          range:NSMakeRange(0, Text.length)];
    return AttributedStr;
}

#pragma mark -lazy

- (UIImageView *)logoImageView {
    if (!_logoImageView){
        _logoImageView = [UIImageView new];
        _logoImageView.contentMode = UIViewContentModeScaleAspectFill;
        [_logoImageView setImage:[UIImage imageNamed:@"store_service_icon_logo"]];
    }
    return _logoImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel){
        _titleLabel = [UILabel labelWithTextColorString:@"#666666" fontSize:11 fontWeight:UIFontWeightMedium];
        [_titleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        _titleLabel.numberOfLines = 1;
    }
    return _titleLabel;
}

- (FNFreshImageLabelStyleView *)loginView{
    if (!_loginView){
        _loginView = [FNFreshImageLabelStyleView new];
        _loginView.style = FNFreshImageLabelStyleOnlyTitle;
        _loginView.edge = UIEdgeInsetsMake(3, 6, 3, 6);
        _loginView.titleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
        [_loginView setTitleColor:[UIColor fn_colorWithHex:@"#E60012"] forState:UIControlStateNormal];
        [_loginView setTitle:@"立即登录" forState:UIControlStateNormal];
        _loginView.layer.borderColor = [UIColor fn_colorWithHex:@"#E60012"].CGColor;
        _loginView.layer.borderWidth = 0.5;
        [_loginView setCornerRadius:11];
    }
    return _loginView;
}

- (FNFreshImageLabelStyleView *)myMemberView{
    if (!_myMemberView){
        _myMemberView = [FNFreshImageLabelStyleView new];
        _myMemberView.style = FNFreshImageLabelStyleTopImageBottomTitle;
        _myMemberView.spacing = 2;
        _myMemberView.titleLabel.font = [UIFont systemFontOfSize:12 weight:UIFontWeightMedium];
        [_myMemberView setTitleColor:[UIColor fn_colorWithHex:@"#FF5A5D"] forState:UIControlStateNormal];
        [_myMemberView setTitle:@"出示会员卡" forState:UIControlStateNormal];
        [_myMemberView setImage:[UIImage imageNamed:@"store_service_viper_code"] forState:UIControlStateNormal];
    }
    return _myMemberView;
}

- (UIStackView *)stackView{
    if (!_stackView){
        _stackView = [UIStackView new];
        _stackView.alignment = UIStackViewAlignmentFill;
        _stackView.distribution = UIStackViewDistributionFillEqually;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
        _stackView.spacing = 0;
    }
    return _stackView;
}


- (FNFreshStoreServicesHeaderMemberUnitView *)couponsView{
    if (!_couponsView){
        _couponsView = [FNFreshStoreServicesHeaderMemberUnitView unitViewForTopTitle:@"*" bottomTitleString:@"优惠卡券" titleType:1 isNormal:YES];
    }
    return _couponsView;
}

- (FNFreshStoreServicesHeaderMemberUnitView *)growUpView{
    if (!_growUpView){
        _growUpView = [FNFreshStoreServicesHeaderMemberUnitView unitViewForTopTitle:@"*" bottomTitleString:@"成长值" titleType:1 isNormal:YES];
    }
    return _growUpView;
}


- (FNFreshStoreServicesHeaderMemberUnitView *)shoppingCardView{
    if (!_shoppingCardView){
        _shoppingCardView = [FNFreshStoreServicesHeaderMemberUnitView unitViewForTopTitle:@"*" bottomTitleString:@"购物卡" titleType:1 isNormal:YES];
    }
    return _shoppingCardView;
}

@end

@interface FNFreshStoreServicesHeaderMemberUnitView()

@property (nonatomic,strong)UIStackView *stackView;

@property (nonatomic,strong)UILabel *bottomContentLabel;

@end

@implementation FNFreshStoreServicesHeaderMemberUnitView


- (void)setTopTitleString:(NSString *)topTitleString{
    _topTitleString = topTitleString;
    self.topContentLabel.text = topTitleString;
    self.topOtherLabel.text = topTitleString;
}

- (void)setBottomTitleString:(NSString *)bottomTitleString{
    _bottomTitleString = bottomTitleString;
    self.bottomContentLabel.text = bottomTitleString;
}

- (void)setTitleType:(NSInteger)titleType {
    _titleType = titleType;
}

- (void)setIsNormal:(BOOL)isNormal{
    _isNormal = isNormal;
    self.topContentLabel.hidden = !isNormal;
    self.topOtherLabel.hidden = isNormal;
}

- (void)setTopAttributeString:(NSMutableAttributedString *)topAttributeString{
    _topAttributeString = topAttributeString;
    self.topOtherLabel.attributedText = topAttributeString;
}

+ (FNFreshStoreServicesHeaderMemberUnitView *)unitViewForTopTitle:(NSString *)topTitleString bottomTitleString:(NSString *)bottomTitleString titleType:(NSInteger)titleType isNormal:(BOOL)isNormal{
    FNFreshStoreServicesHeaderMemberUnitView *unitView = [FNFreshStoreServicesHeaderMemberUnitView new];
    unitView.titleType = titleType;
    unitView.topTitleString = topTitleString;
    unitView.bottomTitleString = bottomTitleString;
    unitView.isNormal = isNormal;
    return unitView;
}

- (void)addChildView{
    [super addChildView];
    
    [self addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.mas_equalTo(self);
    }];
    [self.stackView addArrangedSubview:self.topOtherLabel];
    [self.stackView addArrangedSubview:self.topContentLabel];
    
    [self addSubview:self.bottomContentLabel];
    [self.bottomContentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.mas_equalTo(self);
        make.top.mas_equalTo(self.stackView.mas_bottom);
    }];
}

- (FNFreshPriceLabel *)topContentLabel {
    if (!_topContentLabel){
        _topContentLabel = [[FNFreshPriceLabel alloc] init];
        _topContentLabel.textColor = [UIColor fn_colorWithHex:@"#333333"];
        
        [_topContentLabel setConfig:[FNFreshPriceLabelPartConfig configWith:[UIFont fnHarmonyFontOfSize:22 weight:UIFontWeightBold] textColor:[UIColor fn_colorWithHex:@"#333333"]] AtPartOfPrice:FNFreshPriceLabelPartInteger];
        
        [_topContentLabel setConfig:[FNFreshPriceLabelPartConfig configWith:[UIFont fnHarmonyFontOfSize:14 weight:UIFontWeightBold] textColor:[UIColor fn_colorWithHex:@"#333333"]] AtPartOfPrice:FNFreshPriceLabelPartDecimal];
        
        [_topContentLabel setConfig:[FNFreshPriceLabelPartConfig configWith:[UIFont fnHarmonyFontOfSize:14 weight:UIFontWeightBold] textColor:[UIColor fn_colorWithHex:@"#333333"]] AtPartOfPrice:FNFreshPriceLabelPartSign];
        
        [_topContentLabel setConfig:[FNFreshPriceLabelPartConfig configWith:[UIFont fnHarmonyFontOfSize:14 weight:UIFontWeightMedium] textColor:[UIColor fn_colorWithHex:@"#333333"]] AtPartOfPrice:FNFreshPriceLabelPartUnit];
        
        _topContentLabel.isNeedAddMoneySymbolAutomatically = NO;
        _topContentLabel.text = @"388.88万";
        _topContentLabel.userInteractionEnabled = NO;
        _topContentLabel.hidden = YES;
    }
    return _topContentLabel;
}

- (UIStackView *)stackView{
    if (!_stackView){
        _stackView = [UIStackView new];
        _stackView.alignment = UIStackViewAlignmentFill;
        _stackView.distribution = UIStackViewDistributionEqualSpacing;
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0;
        _stackView.userInteractionEnabled = NO;
    }
    return _stackView;
}

- (UILabel *)topOtherLabel {
    if (!_topOtherLabel){
        _topOtherLabel = [UILabel new];
        _topOtherLabel.textColor = [UIColor fn_colorWithHex:@"#333333"];
        _topOtherLabel.font = [UIFont fnHarmonyFontOfSize:17 weight:UIFontWeightBold];
        _topOtherLabel.textAlignment = NSTextAlignmentLeft;
        _topOtherLabel.numberOfLines = 0;
        _topOtherLabel.userInteractionEnabled = NO;
    }
    return _topOtherLabel;
}

- (UILabel *)bottomContentLabel{
    if (!_bottomContentLabel) {
        _bottomContentLabel = [UILabel new];
        _bottomContentLabel.textColor = [UIColor fn_colorWithHex:@"#333333"];
        _bottomContentLabel.font =  [UIFont systemFontOfSize:12 weight:UIFontWeightRegular];
        _bottomContentLabel.textAlignment = NSTextAlignmentLeft;
        _bottomContentLabel.numberOfLines = 0;
        _bottomContentLabel.userInteractionEnabled = NO;
    }
    return _bottomContentLabel;
}

@end
