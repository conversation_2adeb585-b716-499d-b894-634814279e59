//
//  FNFreshStoreServicesHeaderMemberView.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/29.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBaseView.h"
#import "FNFreshStoreServicesHeaderAddressCellDataModel.h"
#import "FNFreshStoreServiceEventNameString.h"
#import "FNFreshPriceLabel.h"
NS_ASSUME_NONNULL_BEGIN

@interface FNFreshStoreServicesHeaderMemberView : FNFreshBaseView

@property (nonatomic,strong)FNFreshStoreServicesHeaderAddressCellDataModel *dataModel;

/**
 本年已享折扣金额9.99w元
 */
@property (nonatomic,strong)NSString *member_titleString;
/**
 是否登录
 */
@property (nonatomic,assign)BOOL isShowLogin;

/**
 优惠券
 */
@property (nonatomic,strong)NSString *member_couponsValueString;

/**
 成长值
 */
@property (nonatomic,strong)NSString *member_growUpValueString;

/**
 购物卡
 */
@property (nonatomic,strong)NSString *member_shoppingCardString;

/**
 我的积分
 */
@property (nonatomic,strong)NSString *member_myPointValueString;

@end

@interface FNFreshStoreServicesHeaderMemberUnitView : FNFreshBaseControl

@property (nonatomic,strong)NSString *topTitleString;

@property (nonatomic,strong)NSString *bottomTitleString;

/**
  titleType: 1 小的小的样式
  titleType: 2 大的样式
 */
@property (nonatomic,assign)NSInteger titleType;

///设置大小字体
@property (nonatomic,assign)BOOL isNormal;

@property (nonatomic,strong)NSMutableAttributedString *topAttributeString;

@property (nonatomic,strong)FNFreshPriceLabel *topContentLabel;

@property (nonatomic,strong)UILabel *topOtherLabel;

+ (FNFreshStoreServicesHeaderMemberUnitView *)unitViewForTopTitle:(NSString *)topTitleString bottomTitleString:(NSString *)bottomTitleString titleType:(NSInteger)titleType isNormal:(BOOL)isNormal;

@end

NS_ASSUME_NONNULL_END
