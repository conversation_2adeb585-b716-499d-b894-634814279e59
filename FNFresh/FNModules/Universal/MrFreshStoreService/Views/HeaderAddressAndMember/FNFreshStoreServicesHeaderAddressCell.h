//
//  FNFreshStoreServicesHeaderAddressCell.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/29.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshBaseView.h"
#import "FNFreshImageLabelStyleView.h"
#import "FNFreshStoreServicesHeaderAddressCellDataModel.h"
#import "FNFreshStoreServiceEventNameString.h"
NS_ASSUME_NONNULL_BEGIN


@interface FNFreshStoreServicesHeaderAddressCell : FNFreshBaseMarginCollectionViewCell

@property (nonatomic,strong)FNFreshStoreServicesHeaderAddressCellDataModel *dataModel;

/// 是否显示 phone
@property (nonatomic,assign)BOOL isShowPhone;

/// 是否显示地址模块
@property (nonatomic,assign)BOOL isShowAddress;

@end

@interface FNFreshStoreServicesHeaderAddressView : FNFreshBaseView

@property (nonatomic,strong)FNFreshStoreServicesHeaderAddressCellDataModel *dataModel;

/// title 的点击事件
@property (nonatomic,copy)ClickControlBlock clickControlBlock;

/**
 地址
 */
@property (nonatomic,strong)NSString *titleString;
/**
 详细地址
 */
@property (nonatomic,strong)NSString *subTitleString;

@end



NS_ASSUME_NONNULL_END
