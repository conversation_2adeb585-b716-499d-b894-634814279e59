//
//  FNFreshMiniHomeTopView.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/6/4.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshMiniHomeTopView.h"
#import "FNFreshMiniStoreHomeResponseModel.h"

@interface FNFreshMiniHomeTopView ()

@property (weak, nonatomic) IBOutlet UILabel *storeNameLab;
@property (weak, nonatomic) IBOutlet UILabel *storeAddressLab;
@property (weak, nonatomic) IBOutlet UIButton *bindBtn;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bindBtnWidthConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topConstraint;
@property (strong, nonatomic) FNFreshMiniStoreHomeResponseModel *dataModel;
@property (copy, nonatomic) void(^handler)(BOOL isBind, NSString *showCode, NSString *barCode);
@property (copy, nonatomic) void(^changeStoreHome)(void);

@end

@implementation FNFreshMiniHomeTopView

- (void)awakeFromNib {
    [super awakeFromNib];
    UIImage *image = [UIImage fnFresh_imageNamed:@"mini_bg_button"];
    CGFloat width = image.size.width;
    UIEdgeInsets edgeInsets = UIEdgeInsetsMake(0, width *0.3, 0, width *0.3);
    [image resizableImageWithCapInsets:edgeInsets resizingMode:UIImageResizingModeStretch];
    [self.bindBtn setBackgroundImage:image forState:UIControlStateNormal];
}

+ (instancetype)instance {
    
     return [[NSBundle mainBundle] loadNibNamed:NSStringFromClass([self class]) owner:nil options:nil].firstObject;
}

- (void)setupWithDataModel:(FNFreshMiniStoreHomeResponseModel *)dataModel
                   handler:(void(^)(BOOL isBind, NSString *showCode, NSString *barCode))handler
        andChangeStoreHome:(void (^)(void))changeHandler {
    if ([self.dataModel isEqual:dataModel]) {
        return;
    }
    self.dataModel = dataModel;
    self.handler = handler;
    self.changeStoreHome = changeHandler;
    self.storeNameLab.text = dataModel.storeName;
    self.storeAddressLab.text = dataModel.storeAddress;
    [self.bindBtn setTitle:dataModel.isBind?@"使用会员卡":@"免费领卡" forState:UIControlStateNormal];
    self.bindBtnWidthConstraint.constant = dataModel.isBind?200:180;
    self.topConstraint.constant = [UIApplication sharedApplication].statusBarFrame.size.height + 44;
}
- (IBAction)switchStoreHomeList:(id)sender {
    !self.changeStoreHome? : self.changeStoreHome();
}

- (IBAction)topBtnClick:(id)sender {
    !self.handler?:self.handler(self.dataModel.isBind, self.dataModel.showCardNo, self.dataModel.barCode);
    [FNFreshAgent eventWithTrackDataPrameters:@{ @"page_col":@"138004",
                                                 @"page_id":@"141",
                                                 @"track_type":@"2",
                                                 @"col_position":self.dataModel.isBind?@"2":@"1",
    }];
}


@end
