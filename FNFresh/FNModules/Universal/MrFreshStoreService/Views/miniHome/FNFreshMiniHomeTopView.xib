<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="15705" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="15706"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="pg8-5x-eyv" customClass="FNFreshMiniHomeTopView">
            <rect key="frame" x="0.0" y="0.0" width="375" height="708"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mini_bg_top" translatesAutoresizingMaskIntoConstraints="NO" id="clg-qD-nCd">
                    <rect key="frame" x="0.0" y="64" width="375" height="644"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="644" id="gVx-Re-QWc"/>
                    </constraints>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mini_logo" translatesAutoresizingMaskIntoConstraints="NO" id="Ezu-fN-oiE">
                    <rect key="frame" x="62.5" y="72" width="250" height="76"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="250" id="85T-zq-uob"/>
                        <constraint firstAttribute="height" constant="76" id="DqF-9E-pUH"/>
                    </constraints>
                </imageView>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mini_img_top" translatesAutoresizingMaskIntoConstraints="NO" id="TGQ-di-dmF">
                    <rect key="frame" x="74" y="197" width="227" height="227"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="227" id="82q-9S-u6B"/>
                        <constraint firstAttribute="width" constant="227" id="O4Y-jG-Gwx"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OmY-JU-F3V">
                    <rect key="frame" x="171.5" y="443" width="0.0" height="39"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="39" id="OZS-Jz-gez"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="28"/>
                    <color key="textColor" red="1" green="0.88235294117647056" blue="0.3411764705882353" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fPZ-GI-c5b">
                    <rect key="frame" x="175.5" y="448.5" width="28" height="28"/>
                    <state key="normal" image="icon_switchover_mini"/>
                    <connections>
                        <action selector="switchStoreHomeList:" destination="pg8-5x-eyv" eventType="touchUpInside" id="Abg-2h-qYS"/>
                    </connections>
                </button>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4tp-Kq-EX3">
                    <rect key="frame" x="199.5" y="487" width="0.0" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="g5K-s5-sfp"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="15"/>
                    <color key="textColor" red="1" green="0.88235294119999996" blue="0.3411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mini_icon_dz" translatesAutoresizingMaskIntoConstraints="NO" id="51g-RP-Ax7">
                    <rect key="frame" x="173.5" y="487" width="18" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="Nd1-8Z-STB"/>
                        <constraint firstAttribute="width" constant="18" id="eok-Ii-Ad3"/>
                    </constraints>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qXH-mm-hYt">
                    <rect key="frame" x="97.5" y="517" width="180" height="70"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="180" id="VFO-qG-5j3"/>
                        <constraint firstAttribute="height" constant="70" id="XGN-jX-4ea"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="24"/>
                    <state key="normal" title="免费领卡" backgroundImage="mini_bg_button"/>
                    <connections>
                        <action selector="topBtnClick:" destination="pg8-5x-eyv" eventType="touchUpInside" id="vYj-Sa-6Rl"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" red="0.14509803921568626" green="0.53725490196078429" blue="0.27450980392156865" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="clg-qD-nCd" firstAttribute="top" secondItem="pg8-5x-eyv" secondAttribute="top" constant="64" id="3KC-q2-Ob5"/>
                <constraint firstItem="fPZ-GI-c5b" firstAttribute="leading" secondItem="OmY-JU-F3V" secondAttribute="trailing" constant="4" id="4wA-L6-6iu"/>
                <constraint firstItem="OmY-JU-F3V" firstAttribute="centerX" secondItem="pg8-5x-eyv" secondAttribute="centerX" constant="-16" id="5UR-oS-ApK"/>
                <constraint firstItem="OmY-JU-F3V" firstAttribute="top" secondItem="TGQ-di-dmF" secondAttribute="bottom" constant="19" id="5eP-dt-OYu"/>
                <constraint firstItem="4tp-Kq-EX3" firstAttribute="top" secondItem="OmY-JU-F3V" secondAttribute="bottom" constant="5" id="Iwf-AO-Q6v"/>
                <constraint firstItem="Ezu-fN-oiE" firstAttribute="top" secondItem="clg-qD-nCd" secondAttribute="top" constant="8" id="JTc-tM-0Zq"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="4tp-Kq-EX3" secondAttribute="trailing" constant="20" id="Trl-WL-j7I"/>
                <constraint firstItem="qXH-mm-hYt" firstAttribute="top" secondItem="51g-RP-Ax7" secondAttribute="bottom" constant="9" id="Zbe-Dv-Hk4"/>
                <constraint firstItem="4tp-Kq-EX3" firstAttribute="centerX" secondItem="pg8-5x-eyv" secondAttribute="centerX" constant="12" id="aUv-zs-xEP"/>
                <constraint firstItem="clg-qD-nCd" firstAttribute="leading" secondItem="pg8-5x-eyv" secondAttribute="leading" id="b5d-wf-VN3"/>
                <constraint firstItem="TGQ-di-dmF" firstAttribute="centerX" secondItem="clg-qD-nCd" secondAttribute="centerX" id="ckV-as-fGZ"/>
                <constraint firstItem="fPZ-GI-c5b" firstAttribute="centerY" secondItem="OmY-JU-F3V" secondAttribute="centerY" id="fpn-P5-tYE"/>
                <constraint firstItem="Ezu-fN-oiE" firstAttribute="centerX" secondItem="clg-qD-nCd" secondAttribute="centerX" id="gVW-eH-wWW"/>
                <constraint firstItem="qXH-mm-hYt" firstAttribute="centerX" secondItem="pg8-5x-eyv" secondAttribute="centerX" id="gej-Vz-oqr"/>
                <constraint firstItem="4tp-Kq-EX3" firstAttribute="leading" secondItem="51g-RP-Ax7" secondAttribute="trailing" constant="8" id="kGB-iy-yOO"/>
                <constraint firstItem="TGQ-di-dmF" firstAttribute="top" secondItem="clg-qD-nCd" secondAttribute="top" constant="133" id="pJV-25-2VE"/>
                <constraint firstItem="51g-RP-Ax7" firstAttribute="centerY" secondItem="4tp-Kq-EX3" secondAttribute="centerY" id="wUe-ZJ-OjY"/>
                <constraint firstAttribute="trailing" secondItem="clg-qD-nCd" secondAttribute="trailing" id="x7a-Pc-zal"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="bindBtn" destination="qXH-mm-hYt" id="gB9-qe-xkZ"/>
                <outlet property="bindBtnWidthConstraint" destination="VFO-qG-5j3" id="KXS-GR-cyW"/>
                <outlet property="storeAddressLab" destination="4tp-Kq-EX3" id="o4i-tD-kXf"/>
                <outlet property="storeNameLab" destination="OmY-JU-F3V" id="8ex-oK-VNt"/>
                <outlet property="topConstraint" destination="3KC-q2-Ob5" id="ENp-Wj-HxP"/>
            </connections>
            <point key="canvasLocation" x="-170.40000000000001" y="-222.1889055472264"/>
        </view>
    </objects>
    <resources>
        <image name="icon_switchover_mini" width="28" height="28"/>
        <image name="mini_bg_button" width="180" height="70"/>
        <image name="mini_bg_top" width="376" height="643.5"/>
        <image name="mini_icon_dz" width="17.5" height="21"/>
        <image name="mini_img_top" width="226.5" height="226.5"/>
        <image name="mini_logo" width="250" height="75.5"/>
    </resources>
</document>
