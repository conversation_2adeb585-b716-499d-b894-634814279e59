//
//  FNFreshMiniTableViewCell.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/6/16.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshMiniTableViewCell.h"

@implementation FNFreshMiniTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)configContentWithImgStr:(NSString *)imgStr {
    if ([imgStr hasPrefix:@"http"]) {
        [self.imgView sd_setImageWithURL:[NSURL URLWithString:imgStr] placeholderImage:nil];
    } else {
        [self.imgView setImage:[UIImage imageWithContentsOfFile:imgStr]];
    }
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    // Configure the view for the selected state
}

@end
