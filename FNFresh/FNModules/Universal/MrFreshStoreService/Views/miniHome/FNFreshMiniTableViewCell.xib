<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" rowHeight="115" id="KGk-i7-Jjw" customClass="FNFreshMiniTableViewCell">
            <rect key="frame" x="0.0" y="0.0" width="314" height="115"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="314" height="115"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hmM-zM-2eF">
                        <rect key="frame" x="0.0" y="0.0" width="314" height="115"/>
                    </imageView>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="hmM-zM-2eF" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="5Uz-Hy-6a3"/>
                    <constraint firstItem="hmM-zM-2eF" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="YLp-Ww-hZn"/>
                    <constraint firstAttribute="bottom" secondItem="hmM-zM-2eF" secondAttribute="bottom" id="r7X-VF-VQU"/>
                    <constraint firstAttribute="trailing" secondItem="hmM-zM-2eF" secondAttribute="trailing" id="wUe-Gt-Zum"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="imgView" destination="hmM-zM-2eF" id="I5r-f6-fiP"/>
            </connections>
            <point key="canvasLocation" x="-63.768115942028992" y="67.96875"/>
        </tableViewCell>
    </objects>
</document>
