<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshShoppingStreetCollectionCell">
            <rect key="frame" x="0.0" y="0.0" width="351" height="199"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="351" height="199"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="uxx-xn-qp6">
                        <rect key="frame" x="0.0" y="0.0" width="351" height="49"/>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mYI-nz-soS">
                        <rect key="frame" x="10" y="24.5" width="0.0" height="0.0"/>
                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="fitMediumFont">
                                <real key="value" value="14"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="h4S-54-CqI">
                        <rect key="frame" x="18" y="14" width="0.0" height="21"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="21" id="2fE-C8-y19"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.59999999999999998" green="0.59999999999999998" blue="0.59999999999999998" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cN2-aT-vwZ">
                        <rect key="frame" x="0.0" y="0.0" width="351" height="49"/>
                        <connections>
                            <action selector="topClick:" destination="gTV-IL-0wX" eventType="touchUpInside" id="20J-2o-rHB"/>
                        </connections>
                    </button>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="0G4-9d-hyG">
                        <rect key="frame" x="0.0" y="49" width="351" height="142"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="f9D-eg-VAV" customClass="FNFreshShoppingStreetCollectionViewLayout">
                            <size key="itemSize" width="50" height="50"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                        </collectionViewFlowLayout>
                        <connections>
                            <outlet property="dataSource" destination="gTV-IL-0wX" id="6pw-kK-1Qy"/>
                            <outlet property="delegate" destination="gTV-IL-0wX" id="8YD-uG-PwU"/>
                        </connections>
                    </collectionView>
                </subviews>
            </view>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="uxx-xn-qp6" firstAttribute="leading" secondItem="cN2-aT-vwZ" secondAttribute="leading" id="6fD-w2-jhX"/>
                <constraint firstItem="uxx-xn-qp6" firstAttribute="bottom" secondItem="cN2-aT-vwZ" secondAttribute="bottom" id="Gz9-Ve-GWB"/>
                <constraint firstItem="h4S-54-CqI" firstAttribute="centerY" secondItem="mYI-nz-soS" secondAttribute="centerY" id="Heb-4c-nt0"/>
                <constraint firstItem="h4S-54-CqI" firstAttribute="leading" secondItem="mYI-nz-soS" secondAttribute="trailing" constant="8" id="J4E-Vt-z8G"/>
                <constraint firstItem="0G4-9d-hyG" firstAttribute="top" secondItem="cN2-aT-vwZ" secondAttribute="bottom" id="Nux-RL-TpI"/>
                <constraint firstItem="0G4-9d-hyG" firstAttribute="top" secondItem="cN2-aT-vwZ" secondAttribute="bottom" id="OhL-Xf-vd3"/>
                <constraint firstItem="mYI-nz-soS" firstAttribute="centerY" secondItem="uxx-xn-qp6" secondAttribute="centerY" id="P9A-KJ-j2R"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="h4S-54-CqI" secondAttribute="trailing" constant="10" id="We4-v4-9xz"/>
                <constraint firstAttribute="trailing" secondItem="0G4-9d-hyG" secondAttribute="trailing" id="aH0-uU-F7Q"/>
                <constraint firstItem="uxx-xn-qp6" firstAttribute="top" secondItem="cN2-aT-vwZ" secondAttribute="top" id="b78-6b-Apl"/>
                <constraint firstAttribute="trailing" secondItem="cN2-aT-vwZ" secondAttribute="trailing" id="d8d-wq-EzV"/>
                <constraint firstItem="0G4-9d-hyG" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="49" id="hKn-4y-6Dv"/>
                <constraint firstItem="uxx-xn-qp6" firstAttribute="trailing" secondItem="cN2-aT-vwZ" secondAttribute="trailing" id="iQl-mP-y5j"/>
                <constraint firstAttribute="trailing" secondItem="cN2-aT-vwZ" secondAttribute="trailing" id="jSb-iV-FZz"/>
                <constraint firstItem="cN2-aT-vwZ" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="jkI-sJ-OBN"/>
                <constraint firstItem="cN2-aT-vwZ" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="kbP-aT-1Wj"/>
                <constraint firstItem="mYI-nz-soS" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="pEh-Kd-wSw"/>
                <constraint firstItem="0G4-9d-hyG" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="rQ2-l8-6YP"/>
                <constraint firstItem="cN2-aT-vwZ" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="s2p-fV-dJW"/>
                <constraint firstItem="cN2-aT-vwZ" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="zMD-IE-cWe"/>
                <constraint firstAttribute="bottom" secondItem="0G4-9d-hyG" secondAttribute="bottom" constant="8" id="zSV-GC-4UL"/>
            </constraints>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                    <integer key="value" value="10"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="10"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="collectionView" destination="0G4-9d-hyG" id="vEm-7c-whK"/>
                <outlet property="collectionViewTopConstraint" destination="hKn-4y-6Dv" id="XDe-0k-eOw"/>
                <outlet property="headerImageView" destination="uxx-xn-qp6" id="Ucs-V6-OSB"/>
                <outlet property="moduleNameLab" destination="mYI-nz-soS" id="61e-Ih-Ldf"/>
                <outlet property="subTitleLab" destination="h4S-54-CqI" id="V8v-YK-OXg"/>
            </connections>
            <point key="canvasLocation" x="132" y="100"/>
        </collectionViewCell>
    </objects>
</document>
