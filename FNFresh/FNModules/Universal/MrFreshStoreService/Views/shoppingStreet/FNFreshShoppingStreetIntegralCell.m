//
//  FNFreshShoppingStreetIntegralCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2022/12/20.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshShoppingStreetIntegralCell.h"
#import "FNFreshStoreServiceImageCell.h"

@interface FNFreshShoppingStreetIntegralCell()<UICollectionViewDelegate,UICollectionViewDataSource>
@property (weak, nonatomic) IBOutlet UIImageView *topImageView;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UIView *bgView;
@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *titleLabelHeightConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewBottomConstraint;

@property (assign, nonatomic) CGFloat contentViewHeight;
@property (copy,nonatomic) NSArray *dataArray;
//滚动的时候只埋点一次
@property (assign,nonatomic) BOOL isTrackData;

@end

@implementation FNFreshShoppingStreetIntegralCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.titleLabel.font = [UIFont fnSystemRatioFontOfSize:14 weight:UIFontWeightMedium];
    self.titleLabel.textColor = [UIColor fn_333333];
    [self initlializeCollectionView];
}

- (void)initlializeCollectionView {
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
    
    NSString *cellName = NSStringFromClass([FNFreshStoreServiceImageCell class]);
    [self.collectionView registerNib:[UINib nibWithNibName:cellName
                                                        bundle:[FNFreshBundleHandler fnFreshBundle]]
              forCellWithReuseIdentifier:cellName];
    
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    FNFreshStoreServiceModel *itemModel = sectionModel.storeServiceModel;
    self.collectionView.backgroundColor = [UIColor whiteColor];
    self.isTrackData = false;
    [self.topImageView fn_setImageWithURL:[NSURL URLWithString:itemModel.bannerUrl]
                              placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];
    
    if (itemModel.color.length > 0) {
        self.bgView.backgroundColor = [UIColor hexString:itemModel.color];
    } else {
        self.bgView.backgroundColor = [UIColor whiteColor];
    }
    
    self.contentViewHeight = sectionModel.contentViewHeight;
    self.collectionView.backgroundColor = [UIColor whiteColor];
    
    if (sectionModel.hasShoppingStreetData && itemModel.subTitle2.length > 0) {
        self.titleLabel.text = itemModel.subTitle2;
        self.titleLabelHeightConstraint.constant = 28;
        self.collectionViewBottomConstraint.constant = 15;
    } else {
        self.titleLabel.text = @"";
        self.titleLabelHeightConstraint.constant = 0;
        self.collectionViewBottomConstraint.constant = 8;
    }
    
    self.dataArray = itemModel.picList2;
    [self.collectionView reloadData];
}

#pragma mark UICollectionViewDelegate & UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceBannerItemModel *itemModel = [self.dataArray safeObjectAtIndex:indexPath.item];
    FNFreshStoreServiceImageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceImageCell class]) forIndexPath:indexPath];
    [cell setDataWithImgUrl:itemModel.img imgCornerRadius:5];
    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    //内容总宽度 SCREEN_WIDTH - 24 - 16
    NSInteger itemsCount = self.dataArray.count;
    CGFloat itemHeight = collectionView.frame.size.height;
   
    if (itemsCount < 4) {
        if (indexPath.row == 0) {
            return CGSizeMake((floor((SCREEN_WIDTH - 40 - 6)/2)), itemHeight);
        } else {
            return CGSizeMake((floor((SCREEN_WIDTH - 40 - 6)/2)), (itemHeight-6)/2.0);
        }
    } else {
        // 150/183
        if (indexPath.row == 0) {
            return CGSizeMake((floor(150.0 / 183 * itemHeight)), itemHeight);
        } else {
            // 98/ 165
            return CGSizeMake((floor(165.0 / 98 * (itemHeight-6)/2.0)), (itemHeight-6)/2.0);
        }
    }
    return CGSizeMake(0, 0);
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (self.handleClickItem) {
        FNFreshStoreServiceBannerItemModel *itemModel = [self.dataArray safeObjectAtIndex:indexPath.item];
        self.handleClickItem(itemModel.url);
    }
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return 6.0f;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 6.0f;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (self.isTrackData) {
        return;
    }
    self.isTrackData = true;
    if (self.isNewStore) {
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"176021",
            @"page_id" :@"160",
            @"track_type":@"2",
        }];
    } else {
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"176018",
            @"page_id" :@"211",
            @"track_type":@"2",
        }];
    }
}

@end
