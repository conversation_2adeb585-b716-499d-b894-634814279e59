<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceImageCell">
            <rect key="frame" x="0.0" y="0.0" width="226" height="142"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="226" height="142"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hBm-yM-peU">
                        <rect key="frame" x="0.0" y="0.0" width="226" height="142"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="0.0"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="0.0"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="hBm-yM-peU" secondAttribute="trailing" id="7z6-Cv-jSR"/>
                <constraint firstAttribute="bottom" secondItem="hBm-yM-peU" secondAttribute="bottom" id="FtE-7j-lPb"/>
                <constraint firstItem="hBm-yM-peU" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="W0C-4I-Qdh"/>
                <constraint firstItem="hBm-yM-peU" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="udF-D2-AU4"/>
            </constraints>
            <size key="customSize" width="226" height="142"/>
            <connections>
                <outlet property="imgView" destination="hBm-yM-peU" id="hTc-CT-r7J"/>
            </connections>
            <point key="canvasLocation" x="265.21739130434787" y="47.544642857142854"/>
        </collectionViewCell>
    </objects>
</document>
