//
//  FNFreshShoppingStreetChildCell.m
//  FNFresh
//
//  Created by wangbo on 2020/8/11.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshShoppingStreetChildCell.h"
#import "UICollectionReusableView+RectCorner.h"

@interface FNFreshShoppingStreetChildCell ()

@property (weak, nonatomic) IBOutlet UIImageView *storeBgImgView;
@property (weak, nonatomic) IBOutlet UIImageView *logoImgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *logoWidthConstraint;

@property (strong, nonatomic) FNFreshStoreServiceBannerItemModel *dataModel;

@end

@implementation FNFreshShoppingStreetChildCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setupWithDataModel:(FNFreshStoreServiceBannerItemModel *)dataModel isBigOne:(BOOL)isBigOne {
    self.dataModel = dataModel;
    if (isBigOne) {
        [self setupRectCorner:UIRectCornerTopLeft|UIRectCornerBottomLeft];
        self.logoWidthConstraint.constant = floorf(0.51 * self.bounds.size.width);
    } else {
        self.layer.mask = nil;
        self.logoWidthConstraint.constant = floorf(0.65 * self.bounds.size.width);
    }
    self.logoImgView.layer.cornerRadius = self.logoWidthConstraint.constant / 2;
    [self.storeBgImgView fn_setImageWithURL:[NSURL URLWithString:self.dataModel.bgImg] placeholder:[UIImage imageNamed:@"img_business_empty"]];
    [self.logoImgView fn_setImageWithURL:[NSURL URLWithString:self.dataModel.img] placeholder:nil];
}

@end
