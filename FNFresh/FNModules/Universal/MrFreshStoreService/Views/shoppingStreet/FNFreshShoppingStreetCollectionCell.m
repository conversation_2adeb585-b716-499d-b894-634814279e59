//
//  FNFreshShoppingStreetCollectionCell.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/8/11.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshShoppingStreetCollectionCell.h"
#import "FNFreshShoppingStreetCollectionViewLayout.h"
#import "FNFreshShoppingStreetChildCell.h"
#import "FNFreshShoppingStreetMoreChildCell.h"

static NSString *const FNFreshShoppingStreetChildCellIdentifier = @"FNFreshShoppingStreetChildCell";
static NSString *const FNFreshShoppingStreetMoreChildCellIdentifier = @"FNFreshShoppingStreetMoreChildCell";

@interface FNFreshShoppingStreetCollectionCell ()<UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>
@property (weak, nonatomic) IBOutlet UIImageView *headerImageView;
@property (weak, nonatomic) IBOutlet UILabel *moduleNameLab;
@property (weak, nonatomic) IBOutlet UILabel *subTitleLab;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewTopConstraint;

@property (copy, nonatomic) void(^clickHandler)(NSString *url);
@property (strong, nonatomic) FNFreshStoreServiceModel *dataModel;

@end

@implementation FNFreshShoppingStreetCollectionCell

- (void)awakeFromNib {
    [super awakeFromNib];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshShoppingStreetChildCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshShoppingStreetChildCellIdentifier];
    [self.collectionView registerNib:[UINib nibWithNibName:FNFreshShoppingStreetMoreChildCellIdentifier bundle:nil] forCellWithReuseIdentifier:FNFreshShoppingStreetMoreChildCellIdentifier];
}

- (void)setDataWith:(FNFreshStoreServiceModel *)model clickHandler:(void (^)(NSString *))clickHandler {
    self.clickHandler = clickHandler;
    self.dataModel = model;
    
    if (model.subTitle.length > 0 && model.picList2.count > 0) {
        self.collectionViewTopConstraint.constant = 38;
    self.moduleNameLab.text = model.subTitle;
        [self.headerImageView fn_setImageWithURL:[NSURL URLWithString:@""] placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];
    } else {
        self.collectionViewTopConstraint.constant = 49;
        self.moduleNameLab.text = @"";
        [self.headerImageView fn_setImageWithURL:[NSURL URLWithString:model.bannerUrl] placeholder:[UIImage fnFresh_imageNamed:@"icon_placeholder"]];
    }
    self.contentView.backgroundColor = [UIColor hexString:model.color];
    [self.collectionView reloadData];
}

- (void)didMoveToSuperview {
    [super didMoveToSuperview];
    self.collectionView.collectionViewLayout = (FNFreshShoppingStreetCollectionViewLayout *)self.collectionView.collectionViewLayout;
    FNFreshShoppingStreetCollectionViewLayout *layout = (FNFreshShoppingStreetCollectionViewLayout *)self.collectionView.collectionViewLayout;
    CGSize tailItemSize = CGSizeMake(floor(72 * Ratio), floor(138 * Ratio));
    CGSize collectionSize = CGSizeMake(SCREEN_WIDTH - 24, tailItemSize.height);
    CGSize sectionSize = CGSizeMake(collectionSize.width - 20, collectionSize.height);
    CGFloat midItemWidth = (collectionSize.height - 2)/2;
    CGFloat frontItemWidth = sectionSize.width - (3*2 + 2*midItemWidth + tailItemSize.width);
    layout.moreItemSize = tailItemSize;
    layout.firstItemWidth = frontItemWidth;
    layout.middleItemWidth = midItemWidth;
}

- (IBAction)topClick:(id)sender {
    [self handleEnterToStreetListVC];
}

- (void)handleEnterToStreetListVC {
    !self.clickHandler? : self.clickHandler(self.dataModel.hotLinkUrl);
}

#pragma mark UICollectionViewDataSource,UICollectionViewDelegateFlowLayout
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    NSInteger count = self.dataModel.picList.count;
    if (!(count % 2)) {
        count -= 1;
    }
    if (count > 11) {
        count = 11;
    }
    return count + 1;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    UICollectionViewCell *collectionCell = nil;
    NSInteger count = [self.collectionView numberOfItemsInSection:0];
    if (indexPath.row == count - 1) { // 更多
        FNFreshShoppingStreetMoreChildCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshShoppingStreetMoreChildCellIdentifier forIndexPath:indexPath];
        [cell setRectCorner];
        collectionCell = cell;
    } else {
        FNFreshStoreServiceBannerItemModel *dataModel = [self.dataModel.picList safeObjectAtIndex:indexPath.item];
        FNFreshShoppingStreetChildCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:FNFreshShoppingStreetChildCellIdentifier forIndexPath:indexPath];
        [cell setupWithDataModel:dataModel isBigOne:indexPath.item == 0];
        collectionCell = cell;
    }
    
    return collectionCell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    [self handleEnterToStreetListVC];
}

@end
