//
//  FNFreshShoppingStreetCollectionViewLayout.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/8/27.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshShoppingStreetCollectionViewLayout.h"

static const CGFloat spacing = 2;

@interface FNFreshShoppingStreetCollectionViewLayout ()

@property (strong, nonatomic) NSMutableArray <UICollectionViewLayoutAttributes *>*itemAttributes;

@end

@implementation FNFreshShoppingStreetCollectionViewLayout

- (void)prepareLayout {
    [super prepareLayout];
    [self configureLayoutAttributes];
}

- (CGSize)collectionViewContentSize {
    NSInteger count = [self.collectionView numberOfItemsInSection:0];
    CGSize contentSize = CGSizeMake(self.firstItemWidth + (count - 1) / 2 * (self.middleItemWidth + spacing) + spacing + self.moreItemSize.width + 20, self.moreItemSize.height);
    return contentSize;
}

- (void)invalidateLayout {
    [super invalidateLayout];
    [self.itemAttributes removeAllObjects];
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    return self.itemAttributes[indexPath.row];
}

- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    
    NSMutableArray *array = [NSMutableArray array];
    for (UICollectionViewLayoutAttributes *attributes in self.itemAttributes) {
        
        if (CGRectIntersectsRect(rect, attributes.frame)) {
            
            [array addObject:attributes];
        }
    }
    return array;
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds {
    
    return NO;
}

#pragma mark - private methods
- (void)configureLayoutAttributes {
    NSInteger numberOfItem = [self.collectionView numberOfItemsInSection:0];
    for (NSInteger i = 0; i < numberOfItem; i ++) {
        CGRect frame = CGRectZero;
        CGFloat leftSpace = 10;
        NSUInteger x = (i - 1) / 2;
        NSUInteger y = (i + 1) % 2;
        CGFloat originX = leftSpace + spacing + self.firstItemWidth + x * (self.middleItemWidth + spacing);
        CGFloat originY = y * (self.middleItemWidth + spacing);
        if (i == 0) { // 第一个item
            frame.origin = CGPointMake(leftSpace, 0);
            frame.size = CGSizeMake(self.firstItemWidth, self.moreItemSize.height);
        } else if (i == numberOfItem - 1) { // 最后一个item
            frame = (CGRect){{originX,originY},self.moreItemSize};
        } else { // 中间item
            frame = CGRectMake(originX, originY, self.middleItemWidth, self.middleItemWidth);
        }
        UICollectionViewLayoutAttributes *attributes = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:[NSIndexPath indexPathForRow:i inSection:0]];
        attributes.frame = frame;
        if (self.itemAttributes.count > i) {
            
            [self.itemAttributes replaceObjectAtIndex:i withObject:attributes];
        } else {
            
            [self.itemAttributes addObject:attributes];
        }
    }
}

#pragma mark - getters
- (NSMutableArray<UICollectionViewLayoutAttributes *> *)itemAttributes {
    
    if (!_itemAttributes) {
        
        _itemAttributes = [NSMutableArray array];
    }
    return _itemAttributes;
}

@end
