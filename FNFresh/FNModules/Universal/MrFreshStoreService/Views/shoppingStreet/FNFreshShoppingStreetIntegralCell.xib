<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshShoppingStreetIntegralCell">
            <rect key="frame" x="0.0" y="0.0" width="351" height="216"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="351" height="216"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="yWB-XZ-iOP">
                        <rect key="frame" x="0.0" y="0.0" width="351" height="42"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="42" id="RYC-18-b0Y"/>
                            <constraint firstAttribute="width" secondItem="yWB-XZ-iOP" secondAttribute="height" multiplier="351:42" id="XQs-mz-Pei"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CIR-IP-k9W">
                        <rect key="frame" x="0.0" y="42" width="351" height="174"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="k5j-eQ-GHW">
                        <rect key="frame" x="8" y="70" width="335" height="138"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="wNX-hX-GcF">
                            <size key="itemSize" width="128" height="128"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                        </collectionViewFlowLayout>
                    </collectionView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fc1-VL-cwS">
                        <rect key="frame" x="10" y="42" width="331" height="28"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="28" id="x6h-zj-qee"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="CIR-IP-k9W" secondAttribute="bottom" id="3Yg-O3-Uv0"/>
                <constraint firstItem="yWB-XZ-iOP" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="3kV-f1-Bf1"/>
                <constraint firstItem="yWB-XZ-iOP" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="7UC-ro-3Gs"/>
                <constraint firstAttribute="trailing" secondItem="CIR-IP-k9W" secondAttribute="trailing" id="DSi-0p-Nxh"/>
                <constraint firstAttribute="bottom" secondItem="k5j-eQ-GHW" secondAttribute="bottom" constant="8" id="Jve-bQ-vim"/>
                <constraint firstItem="k5j-eQ-GHW" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="8" id="YuV-CC-hJg"/>
                <constraint firstItem="k5j-eQ-GHW" firstAttribute="top" secondItem="Fc1-VL-cwS" secondAttribute="bottom" id="ek5-qj-nar"/>
                <constraint firstItem="Fc1-VL-cwS" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="euQ-K4-9OQ"/>
                <constraint firstAttribute="trailing" secondItem="Fc1-VL-cwS" secondAttribute="trailing" constant="10" id="hvf-19-dFj"/>
                <constraint firstAttribute="trailing" secondItem="yWB-XZ-iOP" secondAttribute="trailing" id="l5t-gg-xgm"/>
                <constraint firstAttribute="trailing" secondItem="k5j-eQ-GHW" secondAttribute="trailing" constant="8" id="n9h-hJ-87g"/>
                <constraint firstItem="Fc1-VL-cwS" firstAttribute="top" secondItem="yWB-XZ-iOP" secondAttribute="bottom" id="pfa-nl-ynP"/>
                <constraint firstItem="CIR-IP-k9W" firstAttribute="top" secondItem="yWB-XZ-iOP" secondAttribute="bottom" id="qah-Mo-2ze"/>
                <constraint firstItem="CIR-IP-k9W" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="xWZ-Bt-5dK"/>
            </constraints>
            <size key="customSize" width="351" height="216"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="10"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="bgView" destination="CIR-IP-k9W" id="kOs-vf-jyr"/>
                <outlet property="collectionView" destination="k5j-eQ-GHW" id="1Xn-8x-LhX"/>
                <outlet property="collectionViewBottomConstraint" destination="Jve-bQ-vim" id="K4g-Oi-w2h"/>
                <outlet property="titleLabel" destination="Fc1-VL-cwS" id="Gev-Id-Stl"/>
                <outlet property="titleLabelHeightConstraint" destination="x6h-zj-qee" id="vdk-pk-1qb"/>
                <outlet property="topImageView" destination="yWB-XZ-iOP" id="66E-k5-dTK"/>
            </connections>
            <point key="canvasLocation" x="142.75362318840581" y="117.1875"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
