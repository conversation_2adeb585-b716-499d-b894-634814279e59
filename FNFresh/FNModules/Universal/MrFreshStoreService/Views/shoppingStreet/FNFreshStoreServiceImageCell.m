//
//  FNFreshStoreServiceImageCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2022/12/20.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceImageCell.h"

@interface FNFreshStoreServiceImageCell()
@property (weak, nonatomic) IBOutlet UIImageView *imgView;

@end

@implementation FNFreshStoreServiceImageCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.imgView.placeholderColor = [UIColor fn_colorWithColorKey:kFNImgPlaceholderKey];
}

- (void)setDataWithImgUrl:(NSString *)imgUrl imgCornerRadius:(CGFloat)cornerRadius {
    self.imgView.layer.cornerRadius = cornerRadius;
    self.imgView.clipsToBounds = true;
    [self.imgView fn_setImageWithURL:[NSURL URLWithString:imgUrl] placeholder:[UIImage fnFresh_imageNamed:@"icon_placeholder_white"]];
}

@end
