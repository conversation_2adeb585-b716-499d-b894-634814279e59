//
//  FNFreshStoreServiceGoodsModuleCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2024/7/25.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceGoodsModuleCell.h"
#import "FNFreshStoreServiceHeaderView.h"
#import "FNFreshStoreServiceEventNameString.h"
#import "FNFreshStoreServiceGoodsItemCell.h"
#import "FNFreshStoreServiceGoodsModuleHeaderView.h"
#import "FNFreshStoreServiceGoodsMoreView.h"
#import "UIColor+Gradient.h"
#import "UIView+Corners.h"
#import "FNFreshStoreServiceEventNameString.h"
#import "FNFreshGradationView.h"
#import "FNFreshStoreServiceGoodsEmptyView.h"

@interface FNFreshStoreServiceGoodsModuleCell() <UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>
@property (weak, nonatomic) IBOutlet FNFreshStoreServiceHeaderView *headerView;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet FNFreshStoreServiceGoodsModuleHeaderView *tabView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tabViewHeight;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewLeft;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewRight;
@property (nonatomic,strong) FNFreshGradationView *listBgView;

@property (nonatomic, copy) NSArray<FNFreshStoreServiceGoodsModuleModel *> *tabList;
@property (nonatomic,strong) NSString *moreBtnJumpUrl;
@property (nonatomic,strong) NSNumber *sectionType;

@property (nonatomic,assign) CGFloat goodSpace;
@property (nonatomic,assign) CGFloat itemWidth;
@property (nonatomic,assign) CGFloat itemHeight;
@property (nonatomic,assign) CGFloat contentViewHeight;

@end

@implementation FNFreshStoreServiceGoodsModuleCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    self.collectionView.delegate = self;
    self.collectionView.showsHorizontalScrollIndicator = NO;
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.backgroundColor = [UIColor clearColor];
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 12, 0, 0);
    [self.collectionView registerNib:[UINib nibWithNibName:NSStringFromClass([FNFreshStoreServiceGoodsItemCell class]) bundle:[FNFreshBundleHandler fnFreshBundle]] forCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceGoodsItemCell class])];
    
    [self.collectionView registerNib:[UINib nibWithNibName:NSStringFromClass([FNFreshStoreServiceGoodsMoreView class]) bundle:nil] forSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:NSStringFromClass([FNFreshStoreServiceGoodsMoreView class])];
    

    
    [self.collectionView registerClass:FNFreshStoreServiceGoodsEmptyView.class forSupplementaryViewOfKind:UICollectionElementKindSectionFooter withReuseIdentifier:NSStringFromClass(FNFreshStoreServiceGoodsEmptyView.class)];

    
    WS(weakSelf);
    self.headerView.moreViewClick = ^(FNFreshImageLabelStyleView * _Nonnull button) {
        [weakSelf handleJump:weakSelf.moreBtnJumpUrl style:@"headerViewMore"];
    };
    
    self.tabView.backgroundColor = [UIColor clearColor];
    self.tabView.tabItemClick = ^(NSInteger index) {
        [weakSelf handleSelectTab:index];
    };
    self.tabView.tabItemMoreBtnClick = ^(NSInteger index) {
        [weakSelf handleClickMore: index];
    };
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    self.headerView.titleString = sectionModel.storeServiceModel.moduleTitle;
    self.headerView.subTitleString = sectionModel.storeServiceModel.moduleSubTitle;
    self.headerView.isMoreHidden = (sectionModel.storeServiceModel.hotLinkUrl.length > 0 &&
                                    sectionModel.storeServiceModel.tabList.count <=1) ? NO : YES;
    self.moreBtnJumpUrl = sectionModel.storeServiceModel.hotLinkUrl;
    self.sectionType = [NSNumber numberWithInteger:sectionModel.sectionType];
    self.contentViewHeight = sectionModel.contentViewHeight;
    
    self.tabList = sectionModel.storeServiceModel.tabList;
    self.tabViewHeight.constant = self.tabList.count > 1 ? 45 : 0;
    self.collectionViewLeft.constant = self.tabList.count > 1 ? 12 : 0;
    self.collectionViewRight.constant = self.tabList.count > 1 ? 12 : 0;
    self.collectionView.backgroundColor = [UIColor clearColor];
    
    if (self.tabList.count > 1) {
        self.tabView.tabList = self.tabList;
        self.collectionView.backgroundView = self.listBgView;
    } else {
        self.collectionView.backgroundView = nil;
    }

    if (self.tabList.count > 0) {
        [self updateCollectionLayout];
    }
    
    // 确保布局更新
    [self.collectionView.collectionViewLayout invalidateLayout];
    [self.collectionView reloadData];
}

- (void)prepareForReuse {
    [super prepareForReuse];
    [self.collectionView scrollToItemAtIndexPath:[NSIndexPath indexPathForItem:0 inSection:0] atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally animated:NO];
    
    // 重置内容偏移
    for (FNFreshStoreServiceGoodsModuleModel *moduleModel in self.tabList) {
        moduleModel.contentOffset = CGPointZero;
    }
    
    // 确保布局更新
    [self.collectionView.collectionViewLayout invalidateLayout];
}


- (void)layoutSubviews {
    [super layoutSubviews];
    if (self.tabList.count > 1) {
        [self.collectionView cutCornerRadius:10 bounds:self.collectionView.frame rectCorner:UIRectCornerBottomLeft | UIRectCornerBottomRight];
    }
}

- (void)updateCollectionLayout {
    NSArray *arr = [self goodsArray];
    if (arr.count <= 0 ) {
        return;
    }
    if (self.tabList.count > 1) {
        self.itemHeight = self.contentViewHeight - 45 - 4 - 10;
        if (arr.count > 3) {
            self.collectionView.contentInset = UIEdgeInsetsMake(4, 9, 10, 0);
            self.goodSpace = 4;
            self.itemWidth = 105;
        } else {
            self.collectionView.contentInset = UIEdgeInsetsMake(4, 9, 10, 9);
            self.goodSpace = 9;
            self.itemWidth = (SCREEN_WIDTH - 24 - 18 - self.goodSpace*2)/3.0;
        }
    } else {
        self.itemHeight = self.contentViewHeight;
        if (arr.count > 3) {
            self.collectionView.contentInset = UIEdgeInsetsMake(0, 12, 0, 0);
            self.goodSpace = 4;
            self.itemWidth = 105;
        } else {
            self.collectionView.contentInset = UIEdgeInsetsMake(0, 12, 0, 12);
            self.goodSpace = 6;
            self.itemWidth = (SCREEN_WIDTH - 24 - self.goodSpace*2)/3.0;
        }
    }
}

- (void)handleSelectTab:(NSInteger)index {
    FNFreshStoreServiceGoodsModuleModel *moduleModel = [self.tabList safeObjectAtIndex: index];
    if (moduleModel.isSelected == true) {
        return;
    }
    
    for (int i=0; i<self.tabList.count; i++) {
        FNFreshStoreServiceGoodsModuleModel *moduleModel = [self.tabList safeObjectAtIndex: i];
        if (i == index) {
            moduleModel.isSelected = true;
        } else {
            moduleModel.isSelected = false;
        }
    }
    
    [self.tabView updateSelected];
    [self updateCollectionLayout];
    [self.collectionView scrollToItemAtIndexPath:[NSIndexPath indexPathForItem:0 inSection:0] atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally animated:YES];
    
    [self.collectionView reloadData];
}

- (void)handleClickMore:(NSInteger)index {
    FNFreshStoreServiceGoodsModuleModel *moduleModel = [self.tabList safeObjectAtIndex:index];
    [self handleJump:moduleModel.hotLinkUrl style:@"tabViewMore"];
}

- (void)handleTapGoodsMore {
    [self handleJump:[self getSelectedTab].hotLinkUrl style:@"goodsTailMore"];
}

- (FNFreshStoreServiceGoodsModuleModel *)getSelectedTab {
    for (int i=0; i<self.tabList.count; i++) {
        FNFreshStoreServiceGoodsModuleModel *moduleModel = [self.tabList safeObjectAtIndex: i];
        if (moduleModel.isSelected == true) {
            return moduleModel;
        }
    }
    if (self.tabList.count > 0) {
        return self.tabList.firstObject;
    }
    return nil;
}

- (NSArray *)goodsArray {
    FNFreshStoreServiceGoodsModuleModel *model = [self getSelectedTab];
    if (model != nil) {
        return model.goodsList;
    }
    return [NSArray array];
}

- (void)handleJump:(NSString *)linkUrl style:(NSString *)style {
    if (linkUrl.length > 0) {
        [self touchActionName:FNFreshStoreServiceEventOpenUrl object:nil userInfo:@{
            FNFreshUserInfoConstKey.content:linkUrl,
            FNFreshUserInfoConstKey.type:self.sectionType,
            FNFreshUserInfoConstKey.style: style ?: @""}];
    }
}

#pragma mark - UICollectionViewDataSource & UICollectionViewDelegate

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self goodsArray].count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshProductListMerchandiseModel *item = [[self goodsArray] safeObjectAtIndex:indexPath.item];
    FNFreshStoreServiceGoodsItemCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceGoodsItemCell class]) forIndexPath:indexPath];
    [cell updateGoodsItemWith:item sectionType:self.sectionType];
    return cell;
}

- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    UICollectionReusableView *reusableView = nil;
//    if ([self getSelectedTab].isShowMoreView) {
    if ([kind isEqualToString:UICollectionElementKindSectionFooter]) {
        if ([self getSelectedTab].isShowMoreView) {
            FNFreshStoreServiceGoodsMoreView *moreView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:NSStringFromClass([FNFreshStoreServiceGoodsMoreView class]) forIndexPath:indexPath];
            moreView.backgroundColor = [UIColor clearColor];
            moreView.userInteractionEnabled = true;
            UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapGoodsMore)];
            [moreView addGestureRecognizer:tap];
            reusableView = moreView;
        } else {
            FNFreshStoreServiceGoodsEmptyView *emptyView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:NSStringFromClass([FNFreshStoreServiceGoodsEmptyView class]) forIndexPath:indexPath];
            reusableView = emptyView;
        }
    }
//    }
    return reusableView;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForFooterInSection:(NSInteger)section {
    if ([self getSelectedTab].isShowMoreView) {
        return CGSizeMake(90, self.itemHeight);
    }
    return CGSizeMake(0.1, 0);
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshProductListMerchandiseModel *item = [[self goodsArray] safeObjectAtIndex:indexPath.item];
    [self.nextResponder touchActionName:FNFreshStoreServiceEventJumpToProductDetail object:nil userInfo:@{
        FNFreshUserInfoConstKey.content: item,
        FNFreshUserInfoConstKey.type: self.sectionType,
    }];
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return CGSizeMake(self.itemWidth, self.itemHeight);
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return self.goodSpace;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return  0;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self getSelectedTab].contentOffset = scrollView.contentOffset;
}

- (FNFreshGradationView *)listBgView {
    if (!_listBgView) {
        _listBgView = [[FNFreshGradationView alloc] initWithFrame:CGRectZero];
        _listBgView.colors = @[(__bridge id)[UIColor hexString:@"#FFFFFF"].CGColor, (__bridge id)[UIColor hexString:@"##FFF4F4"].CGColor];
        _listBgView.startPoint = CGPointMake(0.5, 0);
        _listBgView.endPoint = CGPointMake(0.5, 1);
        _listBgView.locations = @[@(0), @(1.0f)];
    }
    return _listBgView;
}

@end


