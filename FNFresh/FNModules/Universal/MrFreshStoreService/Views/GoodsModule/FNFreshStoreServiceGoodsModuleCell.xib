<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceGoodsModuleCell">
            <rect key="frame" x="0.0" y="0.0" width="221" height="172"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="221" height="172"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KTm-Lr-XdV" customClass="FNFreshStoreServiceHeaderView">
                        <rect key="frame" x="12" y="0.0" width="197" height="25"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="25" id="I9Y-E8-vNt"/>
                        </constraints>
                    </view>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="QTR-iV-X0O">
                        <rect key="frame" x="12" y="75" width="197" height="97"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="1Yf-Ib-uDN">
                            <size key="itemSize" width="128" height="128"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                        </collectionViewFlowLayout>
                    </collectionView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="x61-1B-srR" customClass="FNFreshStoreServiceGoodsModuleHeaderView">
                        <rect key="frame" x="12" y="30" width="197" height="45"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="45" id="yvD-Y4-ofj"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstItem="KTm-Lr-XdV" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="7Gx-sm-fCe"/>
                <constraint firstItem="QTR-iV-X0O" firstAttribute="top" secondItem="x61-1B-srR" secondAttribute="bottom" id="9Wa-tz-Zcb"/>
                <constraint firstAttribute="trailing" secondItem="x61-1B-srR" secondAttribute="trailing" constant="12" id="Ip7-RR-kqZ"/>
                <constraint firstAttribute="trailing" secondItem="QTR-iV-X0O" secondAttribute="trailing" constant="12" id="LOZ-Ee-Mp9"/>
                <constraint firstItem="x61-1B-srR" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="12" id="VFy-U9-2TE"/>
                <constraint firstAttribute="bottom" secondItem="QTR-iV-X0O" secondAttribute="bottom" id="deL-oa-i8u"/>
                <constraint firstItem="QTR-iV-X0O" firstAttribute="top" secondItem="x61-1B-srR" secondAttribute="bottom" id="j2y-Ow-jAe"/>
                <constraint firstAttribute="trailing" secondItem="KTm-Lr-XdV" secondAttribute="trailing" constant="12" id="nRA-Pb-Mqk"/>
                <constraint firstItem="QTR-iV-X0O" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="12" id="qK7-kN-ZcV"/>
                <constraint firstItem="KTm-Lr-XdV" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="12" id="vvN-9f-ZMy"/>
                <constraint firstItem="x61-1B-srR" firstAttribute="top" secondItem="KTm-Lr-XdV" secondAttribute="bottom" constant="5" id="zyA-ay-CH7"/>
            </constraints>
            <size key="customSize" width="221" height="172"/>
            <connections>
                <outlet property="collectionView" destination="QTR-iV-X0O" id="JdM-55-2I2"/>
                <outlet property="collectionViewLeft" destination="LOZ-Ee-Mp9" id="E1o-rM-UnZ"/>
                <outlet property="collectionViewRight" destination="qK7-kN-ZcV" id="ret-t3-MkK"/>
                <outlet property="headerView" destination="KTm-Lr-XdV" id="WqC-5P-Wqa"/>
                <outlet property="tabView" destination="x61-1B-srR" id="2zV-Bq-aXU"/>
                <outlet property="tabViewHeight" destination="yvD-Y4-ofj" id="McF-YQ-zs4"/>
            </connections>
            <point key="canvasLocation" x="304.58015267175574" y="28.87323943661972"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
