<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceGoodsItemCell">
            <rect key="frame" x="0.0" y="0.0" width="185" height="278"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="185" height="278"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3Og-11-l6o">
                        <rect key="frame" x="0.0" y="0.0" width="185" height="278"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_placeholder" translatesAutoresizingMaskIntoConstraints="NO" id="oVo-6u-GtK">
                                <rect key="frame" x="0.0" y="0.0" width="185" height="185"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="oVo-6u-GtK" secondAttribute="height" multiplier="1:1" id="Zgg-Vp-80P"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="商品标题" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SdY-gW-GYn" customClass="FNTagLabel">
                                <rect key="frame" x="6" y="190" width="174" height="15.666666666666657"/>
                                <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Ku6-ef-7hq">
                                <rect key="frame" x="156" y="242" width="24" height="24"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="24" id="0Tu-gb-kI1"/>
                                    <constraint firstAttribute="width" constant="24" id="OrI-5d-Aqx"/>
                                </constraints>
                                <state key="normal" image="icon_addshopingcat_small"/>
                                <connections>
                                    <action selector="addShopcartAction:" destination="gTV-IL-0wX" eventType="touchUpInside" id="bfP-ea-phi"/>
                                </connections>
                            </button>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="KV0-26-Ejv">
                                <rect key="frame" x="146" y="0.0" width="39" height="16"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="16" id="7Pa-fn-lfe"/>
                                    <constraint firstAttribute="width" constant="39" id="YG5-Y9-qX4"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fzd-dI-csG" customClass="FNFreshPriceLabel">
                                <rect key="frame" x="6" y="241.66666666666666" width="137.33333333333334" height="14.333333333333343"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Oac-FV-7AZ" customClass="FNFreshPriceLabel">
                                <rect key="frame" x="6" y="256" width="140" height="16"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="16" id="Owa-V8-X9U"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="12"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="标签" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HxB-8t-eZy" customClass="FNTagLabel">
                                <rect key="frame" x="0.0" y="171.66666666666666" width="22.666666666666668" height="13.333333333333343"/>
                                <fontDescription key="fontDescription" type="system" pointSize="11"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="252" verticalHuggingPriority="252" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="751" image="img_offline" translatesAutoresizingMaskIntoConstraints="NO" id="awu-bA-jSs">
                                <rect key="frame" x="145.33333333333334" y="248" width="39.666666666666657" height="30"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="HxB-8t-eZy" firstAttribute="bottom" secondItem="oVo-6u-GtK" secondAttribute="bottom" id="07w-2m-xj3"/>
                            <constraint firstItem="Oac-FV-7AZ" firstAttribute="top" secondItem="Fzd-dI-csG" secondAttribute="bottom" id="0D0-2H-aNX"/>
                            <constraint firstItem="HxB-8t-eZy" firstAttribute="leading" secondItem="oVo-6u-GtK" secondAttribute="leading" id="2HO-Ke-cap"/>
                            <constraint firstItem="Ku6-ef-7hq" firstAttribute="bottom" secondItem="Oac-FV-7AZ" secondAttribute="bottom" constant="-6" id="5D8-1t-ja0"/>
                            <constraint firstAttribute="trailing" secondItem="oVo-6u-GtK" secondAttribute="trailing" id="D4x-Cd-oup"/>
                            <constraint firstItem="Oac-FV-7AZ" firstAttribute="leading" secondItem="Fzd-dI-csG" secondAttribute="leading" id="GLy-SS-gch"/>
                            <constraint firstAttribute="trailing" secondItem="Ku6-ef-7hq" secondAttribute="trailing" constant="5" id="KoW-Gg-wQM"/>
                            <constraint firstItem="SdY-gW-GYn" firstAttribute="top" secondItem="oVo-6u-GtK" secondAttribute="bottom" constant="5" id="RER-YK-U9R"/>
                            <constraint firstItem="KV0-26-Ejv" firstAttribute="top" secondItem="oVo-6u-GtK" secondAttribute="top" id="WCy-G1-eDh"/>
                            <constraint firstItem="oVo-6u-GtK" firstAttribute="leading" secondItem="3Og-11-l6o" secondAttribute="leading" id="ZnO-Ha-er6"/>
                            <constraint firstAttribute="trailing" secondItem="awu-bA-jSs" secondAttribute="trailing" id="f4q-AL-gEm"/>
                            <constraint firstItem="Ku6-ef-7hq" firstAttribute="leading" secondItem="Oac-FV-7AZ" secondAttribute="trailing" constant="10" id="hRy-eh-Z1j"/>
                            <constraint firstItem="Fzd-dI-csG" firstAttribute="leading" secondItem="SdY-gW-GYn" secondAttribute="leading" id="hhO-TW-jgH"/>
                            <constraint firstAttribute="bottom" secondItem="Ku6-ef-7hq" secondAttribute="bottom" constant="12" id="kNc-wM-nqu"/>
                            <constraint firstItem="oVo-6u-GtK" firstAttribute="top" secondItem="3Og-11-l6o" secondAttribute="top" id="kxo-Xx-Uhz"/>
                            <constraint firstItem="awu-bA-jSs" firstAttribute="leading" secondItem="Fzd-dI-csG" secondAttribute="trailing" constant="2" id="mW1-km-SSw"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="HxB-8t-eZy" secondAttribute="trailing" id="re3-Ae-4EK"/>
                            <constraint firstItem="SdY-gW-GYn" firstAttribute="leading" secondItem="3Og-11-l6o" secondAttribute="leading" constant="6" id="rsE-hB-V2y"/>
                            <constraint firstAttribute="bottom" secondItem="awu-bA-jSs" secondAttribute="bottom" id="svi-1W-dOR"/>
                            <constraint firstItem="KV0-26-Ejv" firstAttribute="trailing" secondItem="oVo-6u-GtK" secondAttribute="trailing" id="y5c-Pr-pei"/>
                            <constraint firstAttribute="trailing" secondItem="SdY-gW-GYn" secondAttribute="trailing" constant="5" id="yVV-Rz-woV"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="3Og-11-l6o" secondAttribute="bottom" id="5LI-LR-Jl1"/>
                <constraint firstItem="3Og-11-l6o" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="KR3-dn-gQ2"/>
                <constraint firstItem="3Og-11-l6o" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Lby-VA-aQ2"/>
                <constraint firstAttribute="trailing" secondItem="3Og-11-l6o" secondAttribute="trailing" id="oT4-o8-VYq"/>
            </constraints>
            <size key="customSize" width="185" height="278"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="6"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="addShopCartButton" destination="Ku6-ef-7hq" id="aei-0E-syO"/>
                <outlet property="coldTagImageView" destination="KV0-26-Ejv" id="83N-hT-8RF"/>
                <outlet property="goodsImageView" destination="oVo-6u-GtK" id="3eB-YF-c8y"/>
                <outlet property="linePriceLabel" destination="Oac-FV-7AZ" id="f8O-JW-quv"/>
                <outlet property="offlineImageView" destination="awu-bA-jSs" id="6P4-jr-RZF"/>
                <outlet property="priceLabel" destination="Fzd-dI-csG" id="LXf-wm-ogA"/>
                <outlet property="tagLabel" destination="HxB-8t-eZy" id="cgu-NA-DmQ"/>
                <outlet property="titleLabel" destination="SdY-gW-GYn" id="yke-DK-nnw"/>
            </connections>
            <point key="canvasLocation" x="84.732824427480907" y="100"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="icon_addshopingcat_small" width="24" height="24"/>
        <image name="icon_placeholder" width="44" height="42.666667938232422"/>
        <image name="img_offline" width="39.666667938232422" height="30"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
