//
//  FNFreshStoreServiceGoodsModuleHeaderView.h
//  FNFresh
//
//  Created by ye<PERSON> on 2024/7/29.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshStoreServiceModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface FNFreshStoreServiceGoodsModuleHeaderView : UIView

@property (copy, nonatomic) NSArray<FNFreshStoreServiceGoodsModuleModel *> *tabList;
@property (nonatomic, copy) void(^tabItemClick)(NSInteger index);
@property (nonatomic, copy) void(^tabItemMoreBtnClick)(NSInteger index);

- (void)updateSelected;

@end

@interface FNFreshStoreServiceGoodsModuleHeaderItemView : UIView

@property (nonatomic,strong) UILabel *titleLabel;

@property (nonatomic,strong) UILabel *subTitleLabel;

@property (nonatomic,strong) UIImageView *moreImgView;

@property (nonatomic,assign) BOOL isShowMoreImageView;

//是否选中
@property (nonatomic,assign) BOOL isSelected;
@property (nonatomic, copy) void(^moreBtnClick)(void);


@end
NS_ASSUME_NONNULL_END
