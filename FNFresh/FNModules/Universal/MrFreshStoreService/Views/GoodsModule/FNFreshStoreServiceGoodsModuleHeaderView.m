//
//  FNFreshStoreServiceGoodsModuleHeaderView.m
//  FNFresh
//
//  Created by <PERSON><PERSON> on 2024/7/29.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceGoodsModuleHeaderView.h"
#import "UIView+Corners.h"

@class FNFreshStoreServiceGoodsModuleHeaderItemView;

@interface FNFreshStoreServiceGoodsModuleHeaderView()

@property (nonatomic,strong) UIScrollView *bgScrollView;
@property (nonatomic,strong) NSMutableArray *tabItemViews;

@end


@implementation FNFreshStoreServiceGoodsModuleHeaderView
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setup];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self setup];
    }
    return self;
}

- (void)setup {
    self.tabItemViews = [NSMutableArray array];
    [self addSubview:self.bgScrollView];
    [self.bgScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self);
    }];
}


-(void)setTabList:(NSArray<FNFreshStoreServiceGoodsModuleModel *> *)tabList {
    _tabList = tabList;
    
    [self.tabItemViews removeAllObjects];
    [self.bgScrollView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    CGFloat tabItemWidth = 107;
    if (tabList.count < 4) {
        tabItemWidth = (SCREEN_WIDTH - 24)*1.0/tabList.count;
    }
    
    CGFloat itemLeft = 0.0;
    for (int i=0; i<tabList.count; i++) {
        FNFreshStoreServiceGoodsModuleModel *itemModel = [tabList safeObjectAtIndex:i];
        FNFreshStoreServiceGoodsModuleHeaderItemView *tabItem = [[FNFreshStoreServiceGoodsModuleHeaderItemView alloc] init];
        tabItem.titleLabel.text = itemModel.moduleTitle;
        tabItem.subTitleLabel.text = itemModel.moduleSubTitle;
        tabItem.isShowMoreImageView = itemModel.hotLinkUrl.length > 0;
        tabItem.isSelected = itemModel.isSelected;
        tabItem.tag = 1000 + i;
         
        tabItem.moreImgView.userInteractionEnabled = YES;
        tabItem.moreImgView.tag = 1000 + i;
        UITapGestureRecognizer *tapMore = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleClickMore:)];
        [tabItem.moreImgView addGestureRecognizer:tapMore];
        
        
        tabItem.userInteractionEnabled = true;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapTabItem:)];
        [tabItem addGestureRecognizer:tap];
        
        if (i != 0) {
            if (tabList.count > 2) {
                itemLeft = i*tabItemWidth - 0.5;
            } else {
                itemLeft = i*tabItemWidth;
            }
        } else {
            itemLeft = 0;
        }
        [self.bgScrollView addSubview:tabItem];
        [tabItem mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(itemLeft);
            make.width.mas_equalTo(tabItemWidth);
            make.top.bottom.mas_equalTo(self);
        }];
        [self.tabItemViews addObject:tabItem];
    }
    [self.bgScrollView setContentOffset:CGPointMake(0, 0)];
    self.bgScrollView.contentSize = CGSizeMake(tabList.count*tabItemWidth, 0);
}

- (void)handleTapTabItem:(UITapGestureRecognizer *)tap {
    NSInteger index = tap.view.tag - 1000;
    
    for (int i=0; i<self.tabList.count; i++) {
        FNFreshStoreServiceGoodsModuleHeaderItemView *item = [self.tabItemViews safeObjectAtIndex:i];
        if (i == index) {
            item.isSelected = true;
        } else {
            item.isSelected = false;
        }
    }
    
    if (self.tabItemClick) {
        self.tabItemClick(index);
    }
}

- (void)updateSelected {
    for (int i=0; i<self.tabList.count; i++) {
        FNFreshStoreServiceGoodsModuleHeaderItemView *item = [self.tabItemViews safeObjectAtIndex:i];
        FNFreshStoreServiceGoodsModuleModel *itemModel = [self.tabList safeObjectAtIndex:i];
        item.isSelected = itemModel.isSelected;
    }
}


- (void)handleClickMore:(UITapGestureRecognizer *)tap {
    NSInteger index = tap.view.tag - 1000;
    if (self.tabItemMoreBtnClick) {
        self.tabItemMoreBtnClick(index);
    }
}
    
- (UIScrollView *)bgScrollView {
    if (!_bgScrollView) {
        _bgScrollView = [[UIScrollView alloc] init];
        _bgScrollView.showsVerticalScrollIndicator = NO;
        _bgScrollView.showsHorizontalScrollIndicator = NO;
    }
    return _bgScrollView;
}

@end


@interface FNFreshStoreServiceGoodsModuleHeaderItemView()

@property (nonatomic,strong) UIView *bgView;

@end


@implementation FNFreshStoreServiceGoodsModuleHeaderItemView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setup];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self setup];
    }
    return self;
}

- (void)setup {
    [self addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
    }];
    
    [self.bgView addSubview:self.moreImgView];
    [self.moreImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@14);
        make.width.equalTo(@0);
        make.centerY.equalTo(self.bgView);
        make.right.equalTo(@0);
    }];
    
    
    [self.bgView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.left.mas_equalTo(2);
        make.right.equalTo(self.moreImgView.mas_left).offset(-2);
    }];
    
    [self.bgView addSubview:self.subTitleLabel];
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.titleLabel.mas_bottom).offset(-1);
        make.bottom.mas_equalTo(0);
        make.left.right.equalTo(self.titleLabel);
    }];
    
    self.clipsToBounds = true;
    
    self.backgroundColor = [UIColor hex:@"#FFF4F4"];
    self.layer.borderColor = [UIColor hex:@"#FFD8D8"].CGColor;
    self.layer.borderWidth = 1.0;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    [self cutCornerRadius:10 bounds:self.bounds rectCorner:UIRectCornerTopLeft|UIRectCornerTopRight];
}

- (void)setIsSelected:(BOOL)isSelected {
    _isSelected = isSelected;
    CGFloat w = 0;
    if (isSelected) {
        self.titleLabel.textAlignment = NSTextAlignmentRight;
        self.subTitleLabel.textAlignment = NSTextAlignmentRight;
        self.backgroundColor = [UIColor whiteColor];
        self.layer.borderWidth = 0.0;
        if (self.isShowMoreImageView) {
            w = 14;
        }
        [self.moreImgView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(w);

        }];
    } else {
        self.titleLabel.textAlignment = NSTextAlignmentCenter;
        self.subTitleLabel.textAlignment = NSTextAlignmentCenter;
        self.backgroundColor = [UIColor hex:@"#FFF4F4"];
        self.layer.borderColor = [UIColor hex:@"#FFD8D8"].CGColor;
        self.layer.borderWidth = 0.5;
        [self.moreImgView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@0);
        }];
    }
}

- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [[UIView alloc] init];
    }
    return _bgView;
}

- (UILabel *)titleLabel{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.textColor = [UIColor hex:@"#333333"];
        _titleLabel.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
        _titleLabel.numberOfLines = 1;
        _titleLabel.textAlignment = NSTextAlignmentCenter;
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel{
    if (!_subTitleLabel){
        _subTitleLabel = [[UILabel alloc] init];
        _subTitleLabel.textColor = [UIColor hex:@"#333333"];
        _subTitleLabel.font = [UIFont systemFontOfSize:10 weight:UIFontWeightRegular];
        _subTitleLabel.textAlignment = NSTextAlignmentCenter;
        _subTitleLabel.numberOfLines = 1;
    }
    return _subTitleLabel;
}

- (UIImageView *)moreImgView {
    if (!_moreImgView) {
        _moreImgView = [[UIImageView alloc] init];
        _moreImgView.image = [UIImage imageNamed:@"icon_tab_more"];
    }
    return _moreImgView;
}
@end
