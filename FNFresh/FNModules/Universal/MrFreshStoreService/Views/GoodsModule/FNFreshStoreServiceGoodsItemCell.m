//
//  FNFreshStoreServiceGoodsItemCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2024/7/26.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceGoodsItemCell.h"
#import "FNTagLabel.h"
#import "FNFreshPriceLabel.h"
#import "FNTag.h"
#import "FNBubbleView.h"
#import "FNFreshStoreServiceEventNameString.h"
#import "UIFont+FontType.h"

@interface FNFreshStoreServiceGoodsItemCell()
@property (weak, nonatomic) IBOutlet UIImageView *goodsImageView;
@property (weak, nonatomic) IBOutlet FNTagLabel *titleLabel;
@property (weak, nonatomic) IBOutlet UIImageView *coldTagImageView;

@property (weak, nonatomic) IBOutlet FNFreshPriceLabel *priceLabel;
@property (weak, nonatomic) IBOutlet FNFreshPriceLabel *linePriceLabel;
@property (weak, nonatomic) IBOutlet FNTagLabel *tagLabel;
@property (weak, nonatomic) IBOutlet UIImageView *offlineImageView;
@property (weak, nonatomic) IBOutlet UIButton *addShopCartButton;

@property (nonatomic,strong) NSNumber *sectionType;

@property (nonatomic, strong) FNBubbleView *badgeView;

@property (nonatomic, strong) FNFreshProductListMerchandiseModel *goodsItem;

@end

@implementation FNFreshStoreServiceGoodsItemCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
    
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont fnHarmonyFontOfSize:11 weight:UIFontWeightBold]] AtPartOfPrice:FNFreshPriceLabelPartSign];
    
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont fnHarmonyFontOfSize:20 weight:UIFontWeightBold]] AtPartOfPrice:FNFreshPriceLabelPartInteger];
    
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont fnHarmonyFontOfSize:11 weight:UIFontWeightBold]] AtPartOfPrice:FNFreshPriceLabelPartDecimal];
    
    [self.priceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont systemFontOfSize:11 weight:UIFontWeightRegular]] AtPartOfPrice:FNFreshPriceLabelPartUnit];
        
    [self.linePriceLabel setConfig:[FNFreshPriceLabelPartConfig configWithFont:[UIFont systemFontOfSize:11]] AtPartOfPrice:FNFreshPriceLabelPartLinePrice];

    self.offlineImageView.hidden = YES;
}

- (void)updateGoodsItemWith:(FNFreshProductListMerchandiseModel*)goodsModel sectionType:(NSNumber *)sectionType;
{
    self.goodsItem = goodsModel;
    self.sectionType = sectionType;
    
    [self.goodsImageView fn_setImageWithURL:[NSURL URLWithString:goodsModel.productPictureURL] placeholder:[UIImage fnFresh_imageNamed:@"icon_placeholder"]];
    
    [self.titleLabel refreshLabelWithText:goodsModel.productName tags:goodsModel.tags];
    
    [self.priceLabel setPrice:goodsModel.productRealPrice unit:goodsModel.saleUnit];
    [self.linePriceLabel setPrice:nil unit:nil linePrice:goodsModel.linePrice];
    
    //131 冷藏标
    if (goodsModel.coldTagUrl != nil && goodsModel.coldTagUrl.length > 0) {
        self.coldTagImageView.hidden = false;
        [self.coldTagImageView fn_setImageWithURL:[NSURL URLWithString:goodsModel.coldTagUrl] placeholder:nil];
    } else {
        self.coldTagImageView.hidden = true;
    }

    if (goodsModel.items.count > 0) {
        [self.tagLabel updateText:@"" tags:[NSArray arrayWithObject:[goodsModel.items firstObject]]];
    } else {
        [self.tagLabel updateText:@"" tags:[NSArray array]];
    }
  
    if (goodsModel.isOfflineGoods) {
        self.offlineImageView.hidden = NO;
        self.addShopCartButton.hidden = YES;
    } else {
        self.offlineImageView.hidden = YES;
        self.addShopCartButton.hidden = NO;
    }
}

- (void)updatePurchasedNumBadge:(NSString *)count {
    if(count.integerValue != 0) {
        self.badgeView.hidden = NO;
        self.badgeView.textString = [NSString stringWithFormat:@"x%@",count];
    } else {
        self.badgeView.hidden = YES;
    }
}

- (IBAction)addShopcartAction:(id)sender {
//    if (self.addShopCart) {
//        self.addShopCart(self.goodsItem, self.goodsImageView);
//    }
    [self.nextResponder touchActionName:FNFreshStoreServiceEventAddToShopCart object:self userInfo:@{
        FNFreshUserInfoConstKey.content: self.goodsItem,
        FNFreshUserInfoConstKey.type: self.sectionType,
        @"productImgView":self.goodsImageView,
    }];
}

///加购数量
- (FNBubbleView *)badgeView {
    if (!_badgeView) {
        _badgeView = [FNBubbleView new];
        [self.addShopCartButton addSubview:_badgeView];
        _badgeView.userInteractionEnabled = YES;
        _badgeView.hidden = YES;
        [_badgeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(self.addShopCartButton.mas_centerY).offset(-5);
            make.leading.mas_equalTo(self.addShopCartButton.mas_centerX);
        }];
       }
    return _badgeView;
}

@end
