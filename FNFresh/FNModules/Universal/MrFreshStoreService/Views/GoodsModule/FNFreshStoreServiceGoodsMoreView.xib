<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionReusableView opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="U6b-Vx-4bR">
            <rect key="frame" x="0.0" y="0.0" width="144" height="295"/>
            <autoresizingMask key="autoresizingMask"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="too-ab-J5I">
                    <rect key="frame" x="47.333333333333329" y="123.99999999999999" width="49.333333333333329" height="47.333333333333329"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_more01" translatesAutoresizingMaskIntoConstraints="NO" id="Fm0-bs-j9N">
                            <rect key="frame" x="11.333333333333329" y="0.0" width="27" height="27"/>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="查看更多" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jAB-pu-u4u">
                            <rect key="frame" x="0.0" y="33" width="49.333333333333336" height="14.333333333333336"/>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <color key="textColor" red="0.40000003579999999" green="0.40000003579999999" blue="0.40000003579999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="jAB-pu-u4u" firstAttribute="leading" secondItem="too-ab-J5I" secondAttribute="leading" id="LF3-uI-yvC"/>
                        <constraint firstItem="jAB-pu-u4u" firstAttribute="top" secondItem="Fm0-bs-j9N" secondAttribute="bottom" constant="6" id="dLd-HJ-DJ0"/>
                        <constraint firstItem="Fm0-bs-j9N" firstAttribute="centerX" secondItem="too-ab-J5I" secondAttribute="centerX" id="jDB-TQ-Q8d"/>
                        <constraint firstItem="Fm0-bs-j9N" firstAttribute="top" secondItem="too-ab-J5I" secondAttribute="top" id="wWB-ln-cmx"/>
                        <constraint firstAttribute="bottom" secondItem="jAB-pu-u4u" secondAttribute="bottom" id="yhs-hi-F3k"/>
                        <constraint firstAttribute="trailing" secondItem="jAB-pu-u4u" secondAttribute="trailing" id="zFd-aW-uoi"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="9Gg-ye-v9C"/>
            <constraints>
                <constraint firstItem="too-ab-J5I" firstAttribute="centerY" secondItem="U6b-Vx-4bR" secondAttribute="centerY" id="HUQ-et-fVj"/>
                <constraint firstItem="too-ab-J5I" firstAttribute="centerX" secondItem="U6b-Vx-4bR" secondAttribute="centerX" id="Qnd-zv-4nV"/>
            </constraints>
            <point key="canvasLocation" x="-314.50381679389312" y="124.29577464788733"/>
        </collectionReusableView>
    </objects>
    <resources>
        <image name="icon_more01" width="27" height="27"/>
    </resources>
</document>
