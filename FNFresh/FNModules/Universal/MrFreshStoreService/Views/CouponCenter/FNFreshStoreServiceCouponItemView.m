//
//  FNFreshStoreServiceCouponItemView.m
//  FNFresh
//
//  Created by ye<PERSON> on 2025/7/31.
//  Copyright © 2025 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceCouponItemView.h"
#import "NSAttributedString+FNFreshDiscountRule.h"

@interface  FNFreshStoreServiceCouponItemView ()

@property (nonatomic, strong) FNFreshStoreServiceCouponMarkView *leftTagView;
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;

@property (nonatomic, strong) UILabel *nameLabel;

@end


@implementation FNFreshStoreServiceCouponItemView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUpUI];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self setUpUI];
    }
    return self;
}

- (void)setUpUI {
    [self addSubview:self.bgImageView];
    [self.bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self);
    }];
    
    [self addSubview:self.leftTagView];
    [self.leftTagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.mas_offset(0);
        make.height.mas_equalTo(12);
        make.width.mas_greaterThanOrEqualTo(35);
    }];
    
//    self.stackView.layer.borderColor = [UIColor greenColor].CGColor;
//    self.stackView.layer.borderWidth = 1.0;
    [self.stackView addArrangedSubview:self.titleLabel];
    [self.stackView addArrangedSubview:self.subTitleLabel];
    [self addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.mas_equalTo(12);
        make.height.mas_equalTo(35);
    }];
    
    [self addSubview:self.nameLabel];
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.bottom.mas_equalTo(-8);
//        make.top.equalTo(self.stackView.mas_bottom).offset(4);
    }];
}

- (void)updateWithData:(FNFreshStoreServiceConponModel * _Nonnull)model {
    /**
    左上角标：线上券若是APP专享打“APP专享”，互通券打“互通券”、线下券打“门店券”
    金额：满减、单品定价、单品立减、抵用、自提、运费展示【券金额】；满折展示【券折扣度】；赠品/礼品展示【赠品券/礼品券】，数字最多取小数位后两位，后位去0显示
    门槛：满减、满折、单品立减、运费展示【满XX元可用，无门槛展示“无门槛”，数字最多取小数位后两位，后位去0显示】；单品定价展示【单品价】；抵用券、自提券展示【无门槛】；赠品/礼品不展示门槛。
    券名称：展示券名称
    */
    if (model.couponUseTagType > 0  && model.couponUseTag.length > 0) {
        self.leftTagView.hidden = false;
        self.leftTagView.type = model.couponUseTagType;
        self.leftTagView.title = model.couponUseTag;
    } else {
        self.leftTagView.hidden = true;
    }
    
    kFNDiscountUIType type = kFNDiscountUITypeNumber;
    // 券类型（1:满减券 2:礼品券 3:单品立减券 4：单品定价券 5：满折券 6：赠品券 7：抵用券 8：运费券 9：自提券）
    if (model.couponType == 2 ||
        model.couponType == 6) {
        type = kFNDiscountUITypeString;
    } else if (model.couponType == 5) {
        type = kFNDiscountUITypeDiscount;
    }
    
    UIFont *textFont = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
    UIColor *textColor = [UIColor fn_colorWithHex:@"#FF2828"];
    
    self.titleLabel.attributedText = [NSAttributedString attrStrWithDiscountType:type value:model.couponValue integerFont:[UIFont fnHarmonyFontOfSize:22 weight:UIFontWeightBold] decimalFont:[UIFont fnHarmonyFontOfSize:13 weight:UIFontWeightBold] symbolFont:[UIFont fnHarmonyFontOfSize:14 weight:UIFontWeightBold] textFont:textFont color:textColor];
    
    self.subTitleLabel.text = model.couponThreshold;
    self.nameLabel.text = model.couponName;

}

- (UIImageView *)bgImageView {
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] init];
    }
    return _bgImageView;
}

- (FNFreshStoreServiceCouponMarkView *)leftTagView {
    if (!_leftTagView) {
        _leftTagView = [FNFreshStoreServiceCouponMarkView new];
        _leftTagView.hidden = YES;
        _leftTagView.label.font = [UIFont systemFontOfSize:9];
        _leftTagView.contentInset = UIEdgeInsetsMake(0, 4, 0, 4);
    }
    return _leftTagView;
}

- (UIStackView *)stackView {
    if (!_stackView) {
        _stackView = [[UIStackView alloc] init];
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.distribution = UIStackViewDistributionFill;
        _stackView.alignment = UIStackViewAlignmentCenter;
        _stackView.spacing = 0;
    }
    return _stackView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:15 weight:UIFontWeightMedium];
        _titleLabel.textAlignment = NSTextAlignmentCenter;
        [_titleLabel setTextColor:[UIColor hex:@"#FF2828"]];
        _titleLabel.text = @"礼品券";
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        _subTitleLabel = [[UILabel alloc] init];
        _subTitleLabel.font = [UIFont systemFontOfSize:9];
        _subTitleLabel.textAlignment = NSTextAlignmentCenter;
        [_subTitleLabel setTextColor:[UIColor hex:@"#888888"]];
        _subTitleLabel.text = @"满199可用";
    }
    return _subTitleLabel;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] init];
        _nameLabel.font = [UIFont systemFontOfSize:11 weight:UIFontWeightMedium];
        _nameLabel.textAlignment = NSTextAlignmentCenter;
        [_nameLabel setTextColor:[UIColor hex:@"#333333"]];
        _nameLabel.text = @"标题主文字";
    }
    return _nameLabel;
}
@end


@implementation FNFreshStoreServiceCouponMarkView

- (void)setType:(NSInteger)type {
//    1：APP专享:2：门店券:3：互通券 4: 运费券 0：不展示
    _type = type;
    NSString *text;
    UIColor *bgColor;
    switch (type) {
        case 1: {
            text = @"APP专享";
            bgColor = [UIColor fn_colorWithHex:@"#CD2222"];
        }
            break;
        case 2: {
            text = @"门店券";
            bgColor = [UIColor fn_colorWithHex:@"#FB9A34"];
        }
            break;
        case 3: {
            text = @"互通券";
            bgColor = [UIColor fn_colorWithHex:@"#6579E2"];
        }
            break;
        case 4: {
            text = @"运费券";
            bgColor = [UIColor fn_colorWithHex:@"#CD2222"];
        }
            break;
        default:
            text = @"";
            bgColor = [UIColor clearColor];
    }
    self.backgroundColor = bgColor;
    if (_title && _title.length > 0) {
        text = _title;
    }
    self.label.text = text;
}

- (void)setTitle:(NSString *)title {
    _title = title;
    self.label.text = title;
}

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupConfig];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self setupConfig];
    }
    return self;
}

- (void)setupConfig {
    self.label.font = [UIFont systemFontOfSize:10];
    self.label.textColor = UIColor.whiteColor;
    self.contentInset = UIEdgeInsetsMake(3, 6, 3, 6);
    self.drawCorners = UIRectCornerTopLeft | UIRectCornerBottomRight;
    self.drawRadius = 5;
}

@end

