//
//  FNFreshStoreServiceCouponCenterCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON> on 2025/7/31.
//  Copyright © 2025 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceCouponCenterCell.h"
#import "FNFreshStoreServiceCouponView.h"

@interface FNFreshStoreServiceCouponCenterCell()

@property (weak, nonatomic) IBOutlet UILabel *titleLabel;
@property (weak, nonatomic) IBOutlet UILabel *subTitleLabel;
@property (weak, nonatomic) IBOutlet FNFreshStoreServiceCouponView *couponView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *couponViewHeightConstraint;

@end

@implementation FNFreshStoreServiceCouponCenterCell

- (void)awakeFromNib {
    [super awakeFromNib];
}
// 分版优惠券/分版图片 115 不分版优惠券/不分版图片 78  都需要加上底部12
- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    self.titleLabel.text = sectionModel.storeServiceModel.moduleTitle;
    self.subTitleLabel.text = sectionModel.storeServiceModel.moduleSubTitle;
    
    self.couponViewHeightConstraint.constant = sectionModel.contentViewHeight;
    
    if (sectionModel.storeServiceModel.moduleList.count == 0) {
        self.couponView.hidden = true;
        return;
    }
    
    self.couponView.hidden = false;
    self.couponView.viewHeight = sectionModel.contentViewHeight;
    
    //  领券中心 -- 1：分版展示，2：不分版展示
    if ([sectionModel.storeServiceModel.styleType isEqual:@"1"]) {
        
        self.couponView.type = FNFreshStoreServiceCouponViewTypeGroup;
        
        //1：图片展示，2：券展示
        if (sectionModel.storeServiceModel.moduleList.count > 0) {
            self.couponView.leftView.hidden = false;
            FNFreshStoreServiceCouponCenterModuleModel *firstItem = sectionModel.storeServiceModel.moduleList.firstObject;
            
            if ([firstItem.styleType isEqual:@"1"]) {
                [self.couponView.leftView updateImageTypeWithImageUrl:firstItem.pic.img];
            } else {
                [self.couponView.leftView updateCouponTypeWithTitle:firstItem.moduleTitle
                                                           subTitle:firstItem.moduleSubTitle
                                                          imageName:@"bg_coupon_left"
                                                         couponList:firstItem.couponList];
            }
        } else {
            self.couponView.leftView.hidden = true;
        }
        
        if (sectionModel.storeServiceModel.moduleList.count > 1) {
            self.couponView.rightView.hidden = false;
            
            FNFreshStoreServiceCouponCenterModuleModel *secondItem = [sectionModel.storeServiceModel.moduleList safeObjectAtIndex:1];
            
            if ([secondItem.styleType isEqual:@"1"]) {
                [self.couponView.rightView updateImageTypeWithImageUrl:secondItem.pic.img];
                
            } else {
                [self.couponView.rightView updateCouponTypeWithTitle:secondItem.moduleTitle
                                                            subTitle:secondItem.moduleSubTitle
                                                           imageName:@"bg_coupon_right"
                                                          couponList:secondItem.couponList];
            }
        } else {
            self.couponView.rightView.hidden = true;
        }

    } else if ([sectionModel.storeServiceModel.styleType isEqual:@"2"]) {
        
        FNFreshStoreServiceCouponCenterModuleModel *item = sectionModel.storeServiceModel.moduleList.firstObject;
        //1：图片展示，2：券展示
        if ([item.styleType isEqual:@"1"]) {
            //不分版图片
            self.couponView.type = FNFreshStoreServiceCouponViewTypeSingImage;
            [self.couponView.leftView updateImageTypeWithImageUrl:item.pic.img];
        } else {
            //不分版优惠券
            self.couponView.type = FNFreshStoreServiceCouponViewTypeCoupon;
            NSArray *leftCouponList = [NSArray array];
            NSArray *rightCouponList = [NSArray array];
            
            if (item.couponList.count > 0) {
                NSInteger totalCount = item.couponList.count;
                NSInteger leftCount = MIN(2, totalCount);
                NSInteger rightCount = MAX(0, totalCount - 2);
                
                leftCouponList = [item.couponList subarrayWithRange:NSMakeRange(0, leftCount)];
                if (rightCount > 0) {
                    rightCouponList = [item.couponList subarrayWithRange:NSMakeRange(2, rightCount)];
                }
            }
            
            [self.couponView.leftView updateCouponTypeWithTitle:nil
                                                       subTitle:nil
                                                       imageName:nil
                                                      couponList:leftCouponList];
            
            [self.couponView.rightView updateCouponTypeWithTitle:nil
                                                       subTitle:nil
                                                       imageName:nil
                                                      couponList:rightCouponList];
        }
    } else {
        self.couponView.hidden = true;
    }
}

@end
