//
//  FNFreshStoreServiceCouponView.h
//  FNFresh
//
//  Created by ye<PERSON> on 2025/7/31.
//  Copyright © 2025 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshStoreServiceModel.h"

typedef enum {
    //分组展示 优惠券+优惠券 优惠券+图片 图片+ 优惠券
    FNFreshStoreServiceCouponViewTypeGroup,
    //单行优惠券
    FNFreshStoreServiceCouponViewTypeCoupon,
    //图片，一张
    FNFreshStoreServiceCouponViewTypeSingImage,
} FNFreshStoreServiceCouponViewType;


@class FNFreshStoreServiceCouponSubView;

@interface FNFreshStoreServiceCouponView : UIView

@property (nonatomic, strong) FNFreshStoreServiceCouponSubView *leftView;
@property (nonatomic, strong) FNFreshStoreServiceCouponSubView *rightView;
@property (assign, nonatomic) FNFreshStoreServiceCouponViewType type;
@property (assign, nonatomic) CGFloat viewHeight;

@end

@interface FNFreshStoreServiceCouponSubView : UIView

- (void)updateImageTypeWithImageUrl:(NSString *)imageUrl;

- (void)updateCouponTypeWithTitle:(NSString *)title
                         subTitle:(NSString *)subTitle
                        imageName:(NSString *)imageName
                       couponList:(NSArray<FNFreshStoreServiceConponModel *> *)couponList;

@end

