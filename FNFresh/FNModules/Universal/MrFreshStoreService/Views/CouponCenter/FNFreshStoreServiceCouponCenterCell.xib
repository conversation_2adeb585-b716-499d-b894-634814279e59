<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceCouponCenterCell">
            <rect key="frame" x="0.0" y="0.0" width="334" height="130"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="334" height="130"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ycB-Wp-dkN">
                        <rect key="frame" x="0.0" y="0.0" width="334" height="130"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nJk-yt-NCN">
                                <rect key="frame" x="0.0" y="0.0" width="334" height="40"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yzB-qV-QHv">
                                        <rect key="frame" x="10" y="10.333333333333334" width="40.666666666666664" height="19.333333333333329"/>
                                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                        <color key="textColor" red="0.20000001789999999" green="0.20000001789999999" blue="0.20000001789999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="749" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cPa-Ie-2mC">
                                        <rect key="frame" x="56.666666666666657" y="13" width="31" height="14.333333333333336"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                        <color key="textColor" red="0.60000002379999995" green="0.60000002379999995" blue="0.60000002379999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="更多" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vYs-tg-JMe">
                                        <rect key="frame" x="286" y="12.666666666666668" width="24" height="14.666666666666668"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                        <color key="textColor" red="0.60000002379999995" green="0.60000002379999995" blue="0.60000002379999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="fitRegularFont">
                                                <real key="value" value="12"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_index_more_01" translatesAutoresizingMaskIntoConstraints="NO" id="jHY-Rb-uz9">
                                        <rect key="frame" x="312" y="14" width="12" height="12"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="12" id="uJ0-jf-PiC"/>
                                            <constraint firstAttribute="height" constant="12" id="urd-B4-6Pk"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="jHY-Rb-uz9" firstAttribute="leading" secondItem="vYs-tg-JMe" secondAttribute="trailing" constant="2" id="1NY-zw-SpA"/>
                                    <constraint firstItem="vYs-tg-JMe" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="cPa-Ie-2mC" secondAttribute="trailing" constant="2" id="36S-oe-4d6"/>
                                    <constraint firstAttribute="trailing" secondItem="jHY-Rb-uz9" secondAttribute="trailing" constant="10" id="4yE-ku-KuM"/>
                                    <constraint firstItem="yzB-qV-QHv" firstAttribute="leading" secondItem="nJk-yt-NCN" secondAttribute="leading" constant="10" id="6R9-4i-bXT"/>
                                    <constraint firstAttribute="height" constant="40" id="7oU-l0-Fyw"/>
                                    <constraint firstItem="vYs-tg-JMe" firstAttribute="centerY" secondItem="nJk-yt-NCN" secondAttribute="centerY" id="9jF-1Z-Rm5"/>
                                    <constraint firstItem="cPa-Ie-2mC" firstAttribute="centerY" secondItem="nJk-yt-NCN" secondAttribute="centerY" id="HEc-bd-926"/>
                                    <constraint firstItem="jHY-Rb-uz9" firstAttribute="centerY" secondItem="nJk-yt-NCN" secondAttribute="centerY" id="qJa-yw-7qs"/>
                                    <constraint firstItem="yzB-qV-QHv" firstAttribute="centerY" secondItem="nJk-yt-NCN" secondAttribute="centerY" id="qzx-ae-Ife"/>
                                    <constraint firstItem="cPa-Ie-2mC" firstAttribute="leading" secondItem="yzB-qV-QHv" secondAttribute="trailing" constant="6" id="tRY-EL-WFD"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="R7g-Nj-vgj" customClass="FNFreshStoreServiceCouponView">
                                <rect key="frame" x="10" y="40" width="314" height="78"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="78" id="pA3-qP-m1O"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="nJk-yt-NCN" firstAttribute="leading" secondItem="ycB-Wp-dkN" secondAttribute="leading" id="0da-tO-uwP"/>
                            <constraint firstItem="R7g-Nj-vgj" firstAttribute="top" secondItem="nJk-yt-NCN" secondAttribute="bottom" id="E1h-Zg-wvz"/>
                            <constraint firstItem="R7g-Nj-vgj" firstAttribute="leading" secondItem="ycB-Wp-dkN" secondAttribute="leading" constant="10" id="MKt-5y-QO1"/>
                            <constraint firstAttribute="trailing" secondItem="nJk-yt-NCN" secondAttribute="trailing" id="NZe-bo-Yni"/>
                            <constraint firstAttribute="bottom" secondItem="R7g-Nj-vgj" secondAttribute="bottom" constant="12" id="YGF-NJ-AoB"/>
                            <constraint firstAttribute="trailing" secondItem="R7g-Nj-vgj" secondAttribute="trailing" constant="10" id="huj-6A-Np8"/>
                            <constraint firstItem="nJk-yt-NCN" firstAttribute="top" secondItem="ycB-Wp-dkN" secondAttribute="top" id="r7v-Rk-7Up"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="ycB-Wp-dkN" secondAttribute="bottom" id="DdF-Qy-Fkw"/>
                <constraint firstItem="ycB-Wp-dkN" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Kbz-4b-x6c"/>
                <constraint firstItem="ycB-Wp-dkN" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="RFB-fn-Pcy"/>
                <constraint firstAttribute="trailing" secondItem="ycB-Wp-dkN" secondAttribute="trailing" id="dRe-6U-6da"/>
            </constraints>
            <size key="customSize" width="334" height="135"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                    <real key="value" value="10"/>
                </userDefinedRuntimeAttribute>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="couponView" destination="R7g-Nj-vgj" id="Kaf-dV-SFG"/>
                <outlet property="couponViewHeightConstraint" destination="pA3-qP-m1O" id="Wsw-DT-38m"/>
                <outlet property="subTitleLabel" destination="cPa-Ie-2mC" id="Ebq-xp-Bjd"/>
                <outlet property="titleLabel" destination="yzB-qV-QHv" id="RFF-n9-lm0"/>
            </connections>
            <point key="canvasLocation" x="195.41984732824426" y="65.140845070422543"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="icon_index_more_01" width="7" height="12"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
