//
//  FNFreshStoreServiceCouponView.m
//  FNFresh
//
//  Created by ye<PERSON> on 2025/7/31.
//  Copyright © 2025 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceCouponView.h"
#import "FNFreshStoreServiceCouponItemView.h"
#import "UIImage+DrawLogo.h"

@interface FNFreshStoreServiceCouponView()

@property (assign, nonatomic) CGFloat imageViewWidth;

@end

@implementation FNFreshStoreServiceCouponView


- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUpUI];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self setUpUI];
    }
    return self;
}

- (void)setUpUI {
    [self addSubview:self.leftView];
    [self.leftView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.mas_equalTo(0);
    }];
    [self addSubview:self.rightView];
    [self.rightView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.leftView.mas_right);
        make.right.top.bottom.mas_equalTo(0);
        make.width.equalTo(self.leftView.mas_width);
    }];
}

- (void)setType:(FNFreshStoreServiceCouponViewType)type {
    self.leftView.hidden = false;
    self.rightView.hidden = false;
    
    if (type == FNFreshStoreServiceCouponViewTypeGroup)  {
        [self.leftView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.top.bottom.mas_equalTo(0);
        }];
        [self.rightView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.leftView.mas_right).offset(6);
            make.right.top.bottom.mas_equalTo(0);
            make.width.equalTo(self.leftView.mas_width);
        }];
    } else if (type == FNFreshStoreServiceCouponViewTypeCoupon) {
        [self.leftView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.top.bottom.mas_equalTo(0);
        }];
        [self.rightView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.leftView.mas_right).offset(4);
            make.right.top.bottom.mas_equalTo(0);
            make.width.equalTo(self.leftView.mas_width);
        }];
    } else if (type == FNFreshStoreServiceCouponViewTypeSingImage) {
        self.rightView.hidden = true;
        [self.leftView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.left.right.mas_equalTo(0);
        }];
        [self.rightView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(0);
        }];
    }
}

- (FNFreshStoreServiceCouponSubView *)leftView {
    if (!_leftView) {
        _leftView = [[FNFreshStoreServiceCouponSubView alloc] init];
    }
    return _leftView;
}

- (FNFreshStoreServiceCouponSubView *)rightView {
    if (!_rightView) {
        _rightView = [[FNFreshStoreServiceCouponSubView alloc] init];
    }
    return _rightView;
}

@end


@interface FNFreshStoreServiceCouponSubView()

@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;

@property (nonatomic, strong) FNFreshStoreServiceCouponItemView *couponItemLeft;
@property (nonatomic, strong) FNFreshStoreServiceCouponItemView *couponItemRight;

@end

@implementation FNFreshStoreServiceCouponSubView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUpUI];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self setUpUI];
    }
    return self;
}

- (void)setUpUI {
    [self addSubview:self.bgImageView];
    [self.bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.mas_equalTo(0);
    }];
    
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(8);
        make.top.mas_equalTo(0);
        make.height.mas_equalTo(30);
    }];
    
    [self addSubview:self.subTitleLabel];
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).offset(4);
        make.right.lessThanOrEqualTo(self.mas_right).offset(-8);
        make.centerY.equalTo(self.titleLabel.mas_centerY);
    }];
    
    [self.subTitleLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:(UILayoutConstraintAxisHorizontal)];
    
    [self addSubview:self.couponItemLeft];
    [self.couponItemLeft mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.subTitleLabel.mas_bottom).offset(4);
        make.bottom.mas_equalTo(-8);
        make.left.mas_equalTo(8);
    }];
    
    [self addSubview:self.couponItemRight];
    [self.couponItemRight mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.couponItemLeft.mas_top);
        make.bottom.equalTo(self.couponItemLeft.mas_bottom);
        make.right.mas_equalTo(-8);
        make.left.equalTo(self.couponItemLeft.mas_right).offset(6);
        make.width.equalTo(self.couponItemLeft.mas_width);
    }];
}

- (void)updateImageTypeWithImageUrl:(NSString *)imageUrl {
    self.titleLabel.text = @"";
    self.subTitleLabel.text = @"";
    self.couponItemLeft.hidden = true;
    self.couponItemRight.hidden = true;
    
    self.bgImageView.hidden = false;
    [self.bgImageView fn_setImageWithURL:[NSURL URLWithString:imageUrl] placeholder:[UIImage imageNamed:@"icon_placeholder_white"]];
}

- (void)updateCouponTypeWithTitle:(NSString *)title
                         subTitle:(NSString *)subTitle
                        imageName:(NSString *)imageName
                       couponList:(NSArray<FNFreshStoreServiceConponModel *> *)couponList {
    self.titleLabel.text = title;
    self.subTitleLabel.text = subTitle;
    
    NSString *couponBgImageName = @"bg_coupon_white";
    if (imageName.length > 0) {
        //分组的优惠券
        self.bgImageView.hidden = false;
        self.bgImageView.image = [UIImage imageNamed:imageName];
        
        [self.couponItemLeft mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.subTitleLabel.mas_bottom).offset(4);
            make.bottom.mas_equalTo(-8);
            make.left.mas_equalTo(8);
        }];
        [self.couponItemRight mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.couponItemLeft.mas_top);
            make.bottom.equalTo(self.couponItemLeft.mas_bottom);
            make.right.mas_equalTo(-8);
            make.left.equalTo(self.couponItemLeft.mas_right).offset(6);
            make.width.equalTo(self.couponItemLeft.mas_width);
        }];
    } else {
        //不分组的优惠券
        self.bgImageView.hidden = true;
        
        [self.couponItemLeft mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.left.bottom.top.mas_equalTo(0);
        }];
        [self.couponItemRight mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.top.mas_equalTo(0);
            make.left.equalTo(self.couponItemLeft.mas_right).offset(4);
            make.right.mas_equalTo(0);
            make.width.equalTo(self.couponItemLeft.mas_width);
        }];
        couponBgImageName = @"bg_coupon_red";
    }
    
    self.titleLabel.text = title;
    self.subTitleLabel.text = subTitle;
    
    self.couponItemLeft.bgImageView.image = [UIImage imageNamed:couponBgImageName];
    self.couponItemRight.bgImageView.image = [UIImage imageNamed:couponBgImageName];
    
    FNFreshStoreServiceConponModel *leftItem = NULL;
    FNFreshStoreServiceConponModel *rightItem = NULL;
    if (couponList.count >= 2) {
        self.couponItemLeft.hidden = false;
        self.couponItemRight.hidden = false;
        leftItem = couponList.firstObject;
        rightItem = couponList[1];
    } else if (couponList.count >= 1) {
        self.couponItemLeft.hidden = false;
        self.couponItemRight.hidden = true;
        leftItem = couponList.firstObject;
    } else {
        self.couponItemLeft.hidden = true;
        self.couponItemRight.hidden = true;
    }
    if (leftItem != NULL) {
        [self.couponItemLeft updateWithData:leftItem];
    }
    if (rightItem != NULL) {
        [self.couponItemRight updateWithData:rightItem];
    }
}

- (UIImageView *)bgImageView {
    if (!_bgImageView) {
        _bgImageView = [[UIImageView alloc] init];
        _bgImageView.layer.cornerRadius = 6;
        _bgImageView.layer.masksToBounds = true;
        _bgImageView.contentMode = UIViewContentModeScaleAspectFill;
        _bgImageView.placeholderColor = [UIColor hex:@"#F8F8F8"];
    }
    return _bgImageView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [UIFont systemFontOfSize:13 weight:UIFontWeightMedium];
        _titleLabel.textAlignment = NSTextAlignmentLeft;
        [_titleLabel setTextColor:[UIColor whiteColor]];
//        _titleLabel.text = @"会员周周领";
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel {
    if (!_subTitleLabel) {
        _subTitleLabel = [[UILabel alloc] init];
        _subTitleLabel.font = [UIFont systemFontOfSize:11];
        _subTitleLabel.textAlignment = NSTextAlignmentLeft;
        [_subTitleLabel setTextColor:[UIColor whiteColor]];
//        _subTitleLabel.text = @"每周都可以领";
    }
    return _subTitleLabel;
}

- (FNFreshStoreServiceCouponItemView *)couponItemLeft {
    if (!_couponItemLeft) {
        _couponItemLeft = [[FNFreshStoreServiceCouponItemView alloc] init];
    }
    return _couponItemLeft;
}

- (FNFreshStoreServiceCouponItemView *)couponItemRight {
    if (!_couponItemRight) {
        _couponItemRight = [[FNFreshStoreServiceCouponItemView alloc] init];
    }
    return _couponItemRight;
}


@end
