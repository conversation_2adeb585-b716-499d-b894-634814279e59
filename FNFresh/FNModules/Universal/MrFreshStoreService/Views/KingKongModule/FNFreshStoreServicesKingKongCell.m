//
//  FNFreshStoreServicesKingKongCell.m
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/31.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServicesKingKongCell.h"
#import "FNFreshImageLabelStyleView.h"
#import "UILabel+factory.h"
#import "UIView+Corners.h"
#import "FNFreshStoreServiceEventNameString.h"

@interface FNFreshStoreServicesKingKongCell()<UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>

@property (nonatomic,strong)FNFreshGradientBaseView *bgGradientView;
@property (nonatomic,strong)UICollectionViewFlowLayout *layout;
@property (nonatomic,strong)UICollectionView *collectionView;
@property (nonatomic,strong)UIStackView *stackView;
@property (nonatomic,strong)FNFreshImageLabelStyleView *unFlodView;

@property (nonatomic,assign)CGSize itemSize;

@property (nonatomic,assign)CGFloat leftRightMargin;

@property (nonatomic,assign)CGFloat itemMargin;
/// 每一行最多的个数
@property (nonatomic,assign)NSInteger rowNum;


@property (nonatomic,strong) NSArray *dataArray;
@property (nonatomic,assign)FNFreshStoreServiceKingKongCellUnFoldType unFlodStyle;
@property (nonatomic,assign) NSInteger sectionType;

@end

@implementation FNFreshStoreServicesKingKongCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.rowNum = 5;
    self.itemSize = CGSizeMake(50 * Ratio, 71 * Ratio);
    self.leftRightMargin = 12;
    self.itemMargin = (SCREEN_WIDTH - (self.itemSize.width  * self.rowNum) - (2 * self.leftRightMargin)) / (self.rowNum - 1);
    
//    [self.contentView addSubview:self.bgGradientView];
//    [self.bgGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.leading.trailing.top.mas_equalTo(self.contentView);
//        make.height.mas_equalTo(116 * Ratio);
//    }];
    
    [self.contentView addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.mas_equalTo(self.contentView);
        make.height.mas_equalTo(20);
    }];
    
    [self.contentView addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.mas_equalTo(self.contentView);
        make.top.mas_equalTo(self.collectionView.mas_bottom);
    }];

    [self.stackView addArrangedSubview:self.unFlodView];
    
    WS(weakSelf);
    self.unFlodView.clickControlBlock = ^{
        [weakSelf touchActionName:FNFreshStoreServiceEventNameEnumKingKongUnfoldViewClickValue object:weakSelf userInfo:nil];
    };
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    self.sectionType = sectionModel.sectionType;
    self.dataArray = sectionModel.storeServiceModel.picList;
    self.unFlodStyle = sectionModel.unFlodStyle;
    
    [self modifyCollectionViewParamsData];
    [self refreshUnfoldView];
    [self.collectionView reloadData];
    [self layoutIfNeeded];//这句话必须加上,这个如果不加,cell重用就会导致cell 高度出现问题.
    [self.collectionView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(self.collectionView.contentSize.height + self.collectionView.contentInset.top + self.collectionView.contentInset.bottom);
    }];
}

- (void)modifyCollectionViewParamsData {
    if(self.dataArray.count < 5 && self.dataArray.count > 0){
        self.rowNum = self.dataArray.count;
        switch (self.rowNum) {
            case 1: {
                self.leftRightMargin = SCREEN_WIDTH / 2 - self.itemSize.width;
                self.itemMargin = 0;
                break;
            }
            case 2: {
                self.leftRightMargin = 69;
                self.itemMargin = (SCREEN_WIDTH - (self.itemSize.width  * self.rowNum) - (2 * self.leftRightMargin)) / (self.rowNum - 1);
                break;
            }
            case 3: {
                self.leftRightMargin = 37.5;
                self.itemMargin = (SCREEN_WIDTH - (self.itemSize.width  * self.rowNum) - (2 * self.leftRightMargin)) / (self.rowNum - 1);
                break;
            }
            case 4: {
                self.leftRightMargin = 21.5;
                self.itemMargin = (SCREEN_WIDTH - (self.itemSize.width  * self.rowNum) - (2 * self.leftRightMargin)) / (self.rowNum - 1);
                break;
            }
            default:
                break;
        }
    } else if(self.dataArray.count >= 5) {
        self.rowNum = 5;
        self.leftRightMargin = 12;
        self.itemMargin = (SCREEN_WIDTH - (self.itemSize.width  * self.rowNum) - (2 * self.leftRightMargin)) / (self.rowNum - 1);
    }
    self.collectionView.contentInset = UIEdgeInsetsMake(14, self.leftRightMargin, 10, self.leftRightMargin);
}


- (void)refreshUnfoldView {
    switch (self.unFlodStyle) {
        case FNFreshStoreServiceKingKongCellUnFoldType_hidden: {
            self.unFlodView.hidden = YES;
            break;
        }
        case FNFreshStoreServiceKingKongCellUnFoldType_unFlod: {
            [self.unFlodView setImage:[UIImage imageNamed:@"store_service_unfold"] forState:UIControlStateNormal];
            self.unFlodView.hidden = NO;
            break;
        }
        case FNFreshStoreServiceKingKongCellUnFoldType_allShow: {
            [self.unFlodView setImage:[UIImage imageNamed:@"store_service_fold"] forState:UIControlStateNormal];
            self.unFlodView.hidden = NO;
            break;
        }
        default:
            break;
    }
}

#pragma mark -<UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout>
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView{
    return self.dataArray.count > 0 ? 1 : 0;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section{
    switch (self.unFlodStyle) {
        case FNFreshStoreServiceKingKongCellUnFoldType_hidden: {
            return self.dataArray.count;
        }
        case FNFreshStoreServiceKingKongCellUnFoldType_unFlod: {
            return self.rowNum;
            break;
        }
        case FNFreshStoreServiceKingKongCellUnFoldType_allShow: {
            return self.dataArray.count;
            break;
        }
        default:
            return 0;
    }
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceBannerItemModel *itemModel = [self.dataArray safeObjectAtIndex:indexPath.row];
    FNFreshStoreServicesKingKongUnitItemCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshStoreServicesKingKongUnitItemCell identifierDescString] forIndexPath:indexPath];
    cell.dataModel = itemModel;
    cell.backgroundColor = [UIColor clearColor];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    FNFreshStoreServiceBannerItemModel *itemModel = [self.dataArray safeObjectAtIndex:indexPath.row];
    [self touchActionName:FNFreshStoreServiceEventOpenUrl object:self userInfo:@{
        FNFreshUserInfoConstKey.index: [NSNumber numberWithInteger:indexPath.row+1],
        FNFreshUserInfoConstKey.content: itemModel.url,
        FNFreshUserInfoConstKey.type: [NSNumber numberWithInteger:self.sectionType] }
    ];
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath{
    return self.itemSize;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    // 设置 cell 之间的最小间距
    return self.itemMargin;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 10;
}

- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    return UIEdgeInsetsMake(0, 0, 0, 0);
}

#pragma mark -lazy

- (UICollectionViewFlowLayout *)layout {
    if (!_layout) {
        _layout = [UICollectionViewFlowLayout new];
        _layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    }
    return _layout;
}

- (UICollectionView *)collectionView {
    if (!_collectionView) {
        _collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:self.layout];
        _collectionView.delegate = self;
        _collectionView.dataSource = self;
        _collectionView.showsHorizontalScrollIndicator = NO;
        _collectionView.showsVerticalScrollIndicator = NO;
        _collectionView.scrollEnabled = YES;
        _collectionView.bounces = NO;
        _collectionView.alwaysBounceVertical = YES;
        _collectionView.backgroundColor = [UIColor clearColor];
        _collectionView.contentInset = UIEdgeInsetsMake(14, self.leftRightMargin, 10, self.leftRightMargin);
        _collectionView.clipsToBounds = NO;
        [_collectionView registerClass:[FNFreshStoreServicesKingKongUnitItemCell class] forCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServicesKingKongUnitItemCell class])];
    }
    return _collectionView;
}

- (UIStackView *)stackView{
    if (!_stackView){
        _stackView = [UIStackView new];
        _stackView.alignment = UIStackViewAlignmentFill;
        _stackView.distribution = UIStackViewDistributionEqualSpacing;
        _stackView.axis = UILayoutConstraintAxisVertical;
        _stackView.spacing = 0;
    }
    return _stackView;
}

- (FNFreshImageLabelStyleView *)unFlodView {
    if (!_unFlodView){
        _unFlodView = [FNFreshImageLabelStyleView new];
        _unFlodView.style = FNFreshImageLabelStyleOnlyImage;
    }
    return _unFlodView;
}

- (FNFreshGradientBaseView *)bgGradientView{
    if (!_bgGradientView){
        _bgGradientView = [[FNFreshGradientBaseView alloc] initWithFrame:CGRectZero];
        _bgGradientView.colors = @[(__bridge id)[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0].CGColor, (__bridge id)[UIColor colorWithRed:242/255.0 green:242/255.0 blue:242/255.0 alpha:1.0].CGColor];
        _bgGradientView.startPoint = CGPointMake(0.5, 0);
        _bgGradientView.endPoint = CGPointMake(0.5, 1);
        _bgGradientView.locations = @[@(0), @(1.0f)];
    }
    return _bgGradientView;
}

@end


@interface FNFreshStoreServicesKingKongUnitItemCell()

@property (nonatomic,strong)UIImageView *imageV;

@property (nonatomic,strong)UILabel *bottomLabel;

@property (nonatomic,strong)FNFreshImageLabelStyleView *markSignView;

@end

@implementation FNFreshStoreServicesKingKongUnitItemCell

- (void)setDataModel:(FNFreshStoreServiceBannerItemModel *)dataModel {
    [self.imageV fn_setImageWithURL:[NSURL URLWithString:dataModel.img ?: @""] placeholder:[UIImage imageWithColor:[UIColor whiteColor]]];
    self.bottomLabel.text = dataModel.title;
    
    if (dataModel.subTitle.length <= 5){
        [self.markSignView setTitle:dataModel.subTitle forState:UIControlStateNormal];
    } else {
        NSString *str = [dataModel.subTitle substringWithRange:NSMakeRange(0, 5)];
        [self.markSignView setTitle:str forState:UIControlStateNormal];
    }
    self.markSignView.hidden = !(dataModel.subTitle.length > 0);
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    [self.contentView addSubview:self.imageV];
    [self.imageV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.mas_equalTo(self.contentView);
        make.height.mas_equalTo(self.imageV.mas_width);
    }];
    [self.contentView addSubview:self.bottomLabel];
    CGFloat maxWidth = (SCREEN_WIDTH - 24)/ 5;
    [self.bottomLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(self.contentView);
        make.bottom.mas_equalTo(self.contentView);
        make.width.mas_lessThanOrEqualTo(maxWidth);
    }];
    
    [self.contentView addSubview:self.markSignView];
    [self.markSignView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.contentView);
        make.leading.mas_equalTo(self.contentView.mas_centerX).priority(UILayoutPriorityFittingSizeLevel);
        make.trailing.mas_lessThanOrEqualTo(self.contentView.mas_trailing).mas_offset(8).priorityHigh();
    }];
}

- (void)layoutSubviews{
    [super layoutSubviews];
    [self.markSignView cutCornerRadius:7 bounds:self.markSignView.bounds rectCorner:UIRectCornerTopLeft | UIRectCornerTopRight | UIRectCornerBottomRight];
    self.clipsToBounds = NO;
}

- (FNFreshImageLabelStyleView *)markSignView{
    if (!_markSignView){
        _markSignView = [FNFreshImageLabelStyleView new];
        _markSignView.style = FNFreshImageLabelStyleLeftTitleRightImage;
        [_markSignView setTitleColor:[UIColor fn_colorWithHex:@"#FFFFFF"] forState:UIControlStateNormal];
        _markSignView.attributeGradientDataModel = [FNFreshGradientViewDataModel gradientDataModelForStartPoint:CGPointMake(1, 0.5) endPoint:CGPointMake(0, 0.5) location: @[@(0), @(1.0f)] colorStrings:@[@"#FF0057",@"#FF4B00"]];
        _markSignView.titleLabel.font = [UIFont systemFontOfSize:9 weight:UIFontWeightMedium];
        _markSignView.edge = UIEdgeInsetsMake(2.5, 4, 2.5, 4);
        _markSignView.layer.borderColor = [UIColor fn_colorWithHex:@"#FFFFFF"].CGColor;
        _markSignView.layer.borderWidth = 1;
        [_markSignView.titleLabel setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        _markSignView.layer.zPosition = 100;
    }
    return _markSignView;
}

- (UIImageView *)imageV{
    if (!_imageV){
        _imageV = [UIImageView new];
        _imageV.contentMode = UIViewContentModeScaleAspectFill;
        _imageV.clipsToBounds = YES;
        _imageV.backgroundColor = [UIColor clearColor];
    }
    return _imageV;
}

- (UILabel *)bottomLabel{
    if (!_bottomLabel){
        _bottomLabel = [UILabel labelWithTextColorString:@"#333333" fontSize:12 fontWeight:UIFontWeightRegular];
        _bottomLabel.numberOfLines = 1;
    }
    return _bottomLabel;
}

@end
