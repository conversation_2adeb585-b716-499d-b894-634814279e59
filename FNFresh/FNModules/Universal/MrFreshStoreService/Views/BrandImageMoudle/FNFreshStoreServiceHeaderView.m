//
//  FNFreshStoreServiceIHeaderView.m
//  FNFresh
//
//  Created by ye<PERSON> on 2024/7/24.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceHeaderView.h"
#import "UILabel+factory.h"

@interface FNFreshStoreServiceHeaderView()

@property (nonatomic,strong) UILabel *titleLabel;

@property (nonatomic,strong) UILabel *subTitleLabel;

@property (nonatomic,strong) FNFreshImageLabelStyleView *moreView;

@end


@implementation FNFreshStoreServiceHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setup];
    }
    return self;
}

- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
        [self setup];
    }
    return self;
}

- (void)setup {
    [self addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self);
        make.top.bottom.mas_equalTo(self);
    }];
    [self addSubview:self.subTitleLabel];
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(self.titleLabel.mas_trailing).mas_offset(6);
        make.centerY.mas_equalTo(self.titleLabel);
        make.width.mas_equalTo(0).priority(200);
        make.trailing.mas_lessThanOrEqualTo(self).mas_offset(-43);
    }];
    [self addSubview:self.moreView];
    [self.moreView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(self);
        make.centerY.mas_equalTo(self.subTitleLabel.mas_centerY);
    }];
}

- (void)setTitleString:(NSString *)titleString{
    _titleString = titleString;
    self.titleLabel.text = titleString;
}

- (void)setSubTitleString:(NSString *)subTitleString{
    _subTitleString = subTitleString;
    self.subTitleLabel.text = subTitleString;
}

- (void)setIsMoreHidden:(BOOL)isMoreHidden{
    _isMoreHidden = isMoreHidden;
    [self.moreView setHidden:isMoreHidden];
}

- (void)setMoreViewClick:(void (^)(FNFreshImageLabelStyleView * _Nonnull))moreViewClick {
    _moreViewClick = moreViewClick;
    self.moreView.selectButtonClick = moreViewClick;
}

- (UILabel *)titleLabel{
    if (!_titleLabel){
        _titleLabel = [UILabel labelWithTextColorString:@"#333333" fontSize:16 fontWeight:UIFontWeightMedium];
        _titleLabel.numberOfLines = 1;
        [_titleLabel setContentHuggingPriority:200 forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _titleLabel;
}

- (UILabel *)subTitleLabel{
    if (!_subTitleLabel){
        _subTitleLabel = [UILabel labelWithTextColorString:@"#999999" fontSize:12 fontWeight:UIFontWeightRegular];
        _subTitleLabel.textAlignment = NSTextAlignmentLeft;
        _subTitleLabel.numberOfLines = 1;
        [_subTitleLabel setContentHuggingPriority:100 forAxis:UILayoutConstraintAxisHorizontal];
    }
    return _subTitleLabel;
}

- (FNFreshImageLabelStyleView *)moreView{
    if (!_moreView){
        _moreView = [FNFreshImageLabelStyleView new];
        _moreView.style = FNFreshImageLabelStyleLeftTitleRightImage;
        _moreView.spacing = 3;
        [_moreView setTitleColor:[UIColor fn_colorWithHex:@"#333333"] forState:UIControlStateNormal];
        [_moreView setImage:[UIImage imageNamed:@"icon_store_service_right_arrow"] forState:UIControlStateNormal];
        [_moreView.titleLabel setFont:[UIFont systemFontOfSize:12]];
        [_moreView setTitle:@"更多" forState:UIControlStateNormal];
        
    }
    return _moreView;
}

@end
