<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshStoreServiceImagesCell">
            <rect key="frame" x="0.0" y="0.0" width="339" height="306"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="339" height="306"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZY1-SS-gVy" customClass="FNFreshStoreServiceHeaderView">
                        <rect key="frame" x="12" y="0.0" width="315" height="25"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="25" id="Hr7-TN-hNe"/>
                        </constraints>
                    </view>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="WP8-mX-yXG">
                        <rect key="frame" x="0.0" y="30" width="339" height="276"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="T79-1K-9Lb">
                            <size key="itemSize" width="128" height="128"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                        </collectionViewFlowLayout>
                    </collectionView>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="SEy-5g-ep8"/>
            <constraints>
                <constraint firstItem="WP8-mX-yXG" firstAttribute="top" secondItem="ZY1-SS-gVy" secondAttribute="bottom" constant="5" id="541-KB-0m5"/>
                <constraint firstAttribute="trailing" secondItem="ZY1-SS-gVy" secondAttribute="trailing" constant="12" id="FZ2-yj-tTh"/>
                <constraint firstAttribute="bottom" secondItem="WP8-mX-yXG" secondAttribute="bottom" id="GKh-uY-Vf8"/>
                <constraint firstItem="ZY1-SS-gVy" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="12" id="RuO-rB-XAr"/>
                <constraint firstItem="WP8-mX-yXG" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="WG1-N6-uPx"/>
                <constraint firstAttribute="trailing" secondItem="WP8-mX-yXG" secondAttribute="trailing" id="daH-uE-tjd"/>
                <constraint firstItem="ZY1-SS-gVy" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="wwz-2q-vXS"/>
            </constraints>
            <size key="customSize" width="339" height="306"/>
            <connections>
                <outlet property="collectionView" destination="WP8-mX-yXG" id="bKX-rk-ZGq"/>
                <outlet property="headerView" destination="ZY1-SS-gVy" id="a2d-B1-zhN"/>
                <outlet property="headerViewHeight" destination="Hr7-TN-hNe" id="GKF-hH-M9f"/>
            </connections>
            <point key="canvasLocation" x="61.832061068702288" y="62.676056338028175"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
