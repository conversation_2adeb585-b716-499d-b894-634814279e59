//
//  FNFreshStoreServiceSingleImgCell.m
//  FNFresh
//
//  Created by <PERSON> on 2024/1/30.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceSingleImgCell.h"
#import "SDAnimatedImageView+WebCache.h"

@interface FNFreshStoreServiceSingleImgCell()

@property (nonatomic,strong) SDAnimatedImageView *imgView;

@end

@implementation FNFreshStoreServiceSingleImgCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    [self.contentView addSubview:self.imgView];
    [self.imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    [self.imgView setCornerRadius: 10];
}

- (void)updateDataWithModel:(NSString *)imgStr {
    [self.imgView sd_setImageWithURL:[NSURL URLWithString: imgStr]];
}

- (SDAnimatedImageView *) imgView {
    if(!_imgView) {
        _imgView = [SDAnimatedImageView new];
        [_imgView setContentMode:UIViewContentModeScaleToFill];
    }
    return _imgView;
}
@end
