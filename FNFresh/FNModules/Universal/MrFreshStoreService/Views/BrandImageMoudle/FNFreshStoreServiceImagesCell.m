//
//  FNFreshStoreServiceImagesCell.m
//  FNFresh
//
//  Created by ye<PERSON> on 2024/7/24.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceImagesCell.h"
#import "FNFreshStoreServiceHeaderView.h"
#import "SDAnimatedImageView.h"
#import "FNFreshStoreServiceSingleImgCell.h"
#import "FNFresh-Swift.h"
#import "UIResponder+FNHumanInteraction.h"
#import "FNFreshStoreServiceEventNameString.h"
#import "NSArray+Operation.h"

@interface FNFreshStoreServiceImagesCell()<UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout, FNCollectionFLowLayoutDelegate>

@property (weak, nonatomic) IBOutlet FNFreshStoreServiceHeaderView *headerView;
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *headerViewHeight;

@property (nonatomic,strong) UICollectionViewFlowLayout *layout;
@property (nonatomic,strong) FNCollectionFlowlayout *collLayout;

@property (nonatomic,strong) NSArray *dataArray;
@property (nonatomic,strong) NSString *imgType;
@property (nonatomic,assign) CGFloat contentViewHeight;
@property (nonatomic,strong) NSString *moreBtnJumpUrl;
@property (nonatomic,strong) NSNumber *sectionType;
@end

@implementation FNFreshStoreServiceImagesCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.collectionView.delegate = self;
    self.collectionView.showsHorizontalScrollIndicator = NO;
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.backgroundColor = [UIColor clearColor];
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 12, 0, 0);
    [self.collectionView registerClass:[FNFreshStoreServiceSingleImgCell class] forCellWithReuseIdentifier:[FNFreshStoreServiceSingleImgCell description]];

    WS(weakSelf);
    self.headerView.moreViewClick = ^(FNFreshImageLabelStyleView * _Nonnull button) {
        [weakSelf touchActionName:FNFreshStoreServiceEventOpenUrl object:nil userInfo:@{
            FNFreshUserInfoConstKey.content: self.moreBtnJumpUrl}];
    };
}

- (void)setSectionModel:(FNFreshStoreServiceSectionModel *)sectionModel {
    self.headerView.titleString = sectionModel.storeServiceModel.moduleTitle;
    self.headerView.subTitleString = sectionModel.storeServiceModel.moduleSubTitle;
    self.headerView.isMoreHidden = sectionModel.storeServiceModel.hotLinkUrl.length > 0 ? NO : YES;
    self.moreBtnJumpUrl = sectionModel.storeServiceModel.hotLinkUrl;
    self.sectionType = [NSNumber numberWithInteger:sectionModel.sectionType];
    
    //（1：一排横滑，2 ：1****：1****：2+2 ）
    self.imgType = @"1";
    if (sectionModel.storeServiceModel.styleType.length > 0) {
        self.imgType = sectionModel.storeServiceModel.styleType;
    }
        
    if ([self.imgType isEqual:@"3"]) {
        [self.collectionView setCollectionViewLayout:self.collLayout];
    } else {
        [self.collectionView setCollectionViewLayout:self.layout];
    }
   
    if ([self.imgType isEqual:@"1"]) {
        self.collectionView.contentInset = UIEdgeInsetsMake(0, 12, 0, 0);
        self.collectionView.scrollEnabled = true;
    } else {
        self.collectionView.contentInset = UIEdgeInsetsMake(0, 12, 0, 12);
        self.collectionView.scrollEnabled = false;
    }
    
    if ([self.imgType isEqual:@"4"]) {
        _layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    } else {
        _layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    }
    
    self.contentViewHeight = sectionModel.contentViewHeight;
    self.dataArray = sectionModel.storeServiceModel.picList;
    [self.collectionView reloadData];
}

#pragma mark - UICollectionViewDataSource & UICollectionViewDelegate

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataArray.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceBannerItemModel *item = [self.dataArray safeObjectAtIndex:indexPath.item];
    FNFreshStoreServiceSingleImgCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceSingleImgCell class]) forIndexPath:indexPath];
    [cell updateDataWithModel: item.img];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceBannerItemModel *item = [self.dataArray safeObjectAtIndex:indexPath.item];
    [self.nextResponder touchActionName:FNFreshStoreServiceEventOpenUrl object:nil userInfo:@{
        FNFreshUserInfoConstKey.content: item.url,
        FNFreshUserInfoConstKey.type: self.sectionType,
        FNFreshUserInfoConstKey.index: [NSNumber numberWithInteger:indexPath.row+1],
        FNFreshUserInfoConstKey.style: self.imgType
    }];
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    //一排横滑、1+1、1+2、2+2
    CGFloat itemW = 132;
    CGFloat itemH = self.contentViewHeight;
    if (![self.imgType isEqual:@"1"]) {
        itemW = (SCREEN_WIDTH - 24 - 8)/2.0;
    }
    if ([self.imgType isEqual:@"3"]) {
        if (indexPath.item != 0) {
            itemH = (self.contentViewHeight - 8)/2.0;
        }
    }
    return CGSizeMake(itemW, itemH);
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumLineSpacingForSectionAtIndex:(NSInteger)section {
    return 8;
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    if ([self.imgType isEqual:@"3"] || [self.imgType isEqual:@"4"]) {
        return 8;
    }
    return  0;
}

#pragma mark - Lazy Methods

- (FNCollectionFlowlayout *)collLayout {
    if (!_collLayout) {
        _collLayout = [FNCollectionFlowlayout new];
        _collLayout.minimumInteritemSpacing = 0;
        _collLayout.minimumLineSpacing = 8;
        _collLayout.scrollDirection = UICollectionViewScrollDirectionVertical;
        _collLayout.delegate = self;
    }
    return _collLayout;
}

- (UICollectionViewFlowLayout *)layout {
    if (!_layout) {
        _layout = [UICollectionViewFlowLayout new];
        _layout.minimumInteritemSpacing = 0;
        _layout.minimumLineSpacing = 8;
        _layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    }
    return _layout;
}

@end
