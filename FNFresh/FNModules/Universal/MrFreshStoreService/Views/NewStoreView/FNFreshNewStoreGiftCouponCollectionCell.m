//
//  FNFreshNewStoreGiftCouponCollectionCell.m
//  FNFresh
//
//  Created by wangbo on 2020/10/26.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshNewStoreGiftCouponCollectionCell.h"
#import "FNTagLabel.h"
#import "FNTag.h"

@interface FNFreshNewStoreGiftCouponCollectionCell ()

@property (weak, nonatomic) IBOutlet UIImageView *tipBgImgView;
@property (weak, nonatomic) IBOutlet UILabel *tipNameLab;
@property (weak, nonatomic) IBOutlet UILabel *couponValue;
@property (weak, nonatomic) IBOutlet FNTagLabel *couponNameLab;
@property (weak, nonatomic) IBOutlet UILabel *couponTagLab;
@property (weak, nonatomic) IBOutlet UILabel *unitTagLab;
@property (weak, nonatomic) IBOutlet UILabel *couponDescLab;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *couponNameTopConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *couponDescBottomConstraint;

@end

@implementation FNFreshNewStoreGiftCouponCollectionCell

- (void)awakeFromNib {
    [super awakeFromNib];
}

- (void)setCoupon:(FNMrFreshPopupWindowNewGuidanceItemModel *)coupon isNew:(BOOL)isNew {
    if ([coupon.couponTagType isEqualToString:@"1"]) {
        self.tipBgImgView.image = [UIImage imageNamed:@"newstore_tip_01"];
        self.tipNameLab.text = @"新人礼包";
    } else {
        self.tipBgImgView.image = [UIImage imageNamed:@"newstore_tip_02"];
        self.tipNameLab.text = @"开业礼包";
    }
    [self.tipBgImgView setHidden:!isNew];
    [self.tipNameLab setHidden:!isNew];
    // 优惠券价格
    NSString *couponDescString = coupon.couponThreshold;
    NSString *couponStr = [NSString stringWithFormat:@"¥%@",coupon.couponValue];
    if (coupon.couponType == FNMrFreshNewGuidanceVoucherTypeDiscountCoupons) {
        couponStr = [NSString stringWithFormat:@"%@折",coupon.couponValue];
    } else if (coupon.couponType == FNMrFreshNewGuidanceVoucherTypeGift) {
        couponStr = @"赠品券";
        couponDescString = @"";
    } else if (coupon.couponType == FNMrFreshNewGuidanceVoucherTypeGiftCertificate) {
        couponStr = @"礼品券";
        couponDescString = @"";
    } else if (coupon.couponType == FNMrFreshNewGuidanceVoucherTypeDiYong) {
        couponDescString = @"";
    }
     NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:couponStr];
    if (coupon.couponType == FNMrFreshNewGuidanceVoucherTypeDiscountCoupons) {
        [attrStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:12 weight:UIFontWeightSemibold] range:NSMakeRange(attrStr.length - 1, 1)];
        [attrStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:32 weight:UIFontWeightSemibold] range:NSMakeRange(0, attrStr.length - 1)];
        
    } else if (coupon.couponType == FNMrFreshNewGuidanceVoucherTypeGift ||
               coupon.couponType == FNMrFreshNewGuidanceVoucherTypeGiftCertificate) {
        [attrStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:16 weight:UIFontWeightSemibold] range:NSMakeRange(0, attrStr.length)];
    } else {
        [attrStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:18 weight:UIFontWeightMedium] range:NSMakeRange(0, 1)];
        NSRange pointRange = [couponStr rangeOfString:@"."];
        if (pointRange.location != NSNotFound) {
            [attrStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:32 weight:UIFontWeightMedium] range:NSMakeRange(1, pointRange.location - 1)];
            [attrStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:18 weight:UIFontWeightMedium] range:NSMakeRange(pointRange.location, attrStr.length - (pointRange.location))];
        } else {
            [attrStr addAttribute:NSFontAttributeName value:[UIFont systemFontOfSize:32 weight:UIFontWeightMedium] range:NSMakeRange(1, attrStr.length - 1)];
        }
    }
    self.couponValue.attributedText = attrStr;
    
    // 是否展示一小时达半日达单渠道标
    // 仅线上、互通券类型打此标（但线上券/互通券中的单品券、礼品券及核销渠道是凭证的不需打此标）
    NSMutableArray *tagArr = [NSMutableArray array];
    if (coupon.isOnline == 1 || coupon.isOnline == 3) {
        if (coupon.couponType != FNMrFreshNewGuidanceVoucherTypeSingleItem ||
            coupon.couponType != FNMrFreshNewGuidanceVoucherTypeOfflineStore ||
            coupon.couponType != FNMrFreshNewGuidanceVoucherTypeGiftCertificate) {
            if (coupon.couponCircleChannel == 1 || coupon.couponCircleChannel == 2) {
                [tagArr addObject:[self getTag:coupon.couponCircleChannel]];
            }
        }
    }
    [self.couponNameLab refreshLabelWithText:coupon.couponName tags:tagArr];
    
    self.couponDescLab.text = couponDescString;
    self.couponTagLab.text = coupon.couponUseType;
    
    if (couponDescString.length > 0) {
        self.couponNameTopConstraint.constant = 12;
    } else {
        self.couponNameTopConstraint.constant = 24;
    }
    
    // 是否展示互通券标
    self.unitTagLab.hidden = coupon.couponUseTagType != 3;
    
    if (!isNew) {
        self.unitTagLab.hidden = YES;
    }
    
}

- (void)setCoupon:(FNMrFreshPopupWindowNewGuidanceItemModel *)coupon {
    _coupon = coupon;
}

- (FNTag *)getTag:(NSInteger)type {
    FNTag *tag = [[FNTag alloc] init];
    tag.bgcolor = type==1?@"#FF2727":@"#FF5000";
    tag.bgGraduallyColor = type==1?@"#FF5B5B":@"#FC7639";
    tag.form = 1;
    tag.color = @"#FFFFFF";
    tag.name = type==1?@"小时达":@"半日达";
    tag.cornerRadius = ceilf(4.f);
    tag.rightMargin = floorf(4.f);
    tag.fontSize = ceilf(10.f);
    return tag;
}

@end
