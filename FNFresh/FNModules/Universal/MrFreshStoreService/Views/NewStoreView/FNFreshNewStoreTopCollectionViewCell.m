//
//  FNFreshNewStoreTopCollectionViewCell.m
//  FNFresh
//
//  Created by wangbo on 2020/10/26.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshNewStoreTopCollectionViewCell.h"

@interface FNFreshNewStoreTopCollectionViewCell ()
@property (weak, nonatomic) IBOutlet UILabel *storeNameLabel;
@property (weak, nonatomic) IBOutlet UILabel *addressLabel;
@property (weak, nonatomic) IBOutlet UIView *btnView;

@property (weak, nonatomic) IBOutlet UIView *serviceView;
@property (weak, nonatomic) IBOutlet UIView *leftServiceView;
@property (weak, nonatomic) IBOutlet UIImageView *leftServiceIconImageView;
@property (weak, nonatomic) IBOutlet UILabel *leftServiceLabel;

@property (weak, nonatomic) IBOutlet UIView *rightServiceView;
@property (weak, nonatomic) IBOutlet UIImageView *rightServiceIconImageView;
@property (weak, nonatomic) IBOutlet UILabel *rightServiceLabel;


@property (strong, nonatomic) FNFreshNewStoreHomeInfoResponseModel *dataModel;
@property (copy, nonatomic) void(^handler)(void);
@property (copy, nonatomic) void(^serviceItemClick)(FNMrFreshTextModel *);

@end

@implementation FNFreshNewStoreTopCollectionViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    
    self.btnView.userInteractionEnabled = true;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(btnClick:)];
    [self.btnView addGestureRecognizer:tap];
    
    self.leftServiceView.userInteractionEnabled = true;
    UITapGestureRecognizer *leftServiceViewTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(leftServiceViewClick)];
    [self.leftServiceView addGestureRecognizer:leftServiceViewTap];
    
    self.rightServiceView.userInteractionEnabled = true;
    UITapGestureRecognizer *rightServiceViewTap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(rightServiceViewClick)];
    [self.rightServiceView addGestureRecognizer:rightServiceViewTap];
}

- (void)setupWithDataModel:(FNFreshNewStoreHomeInfoResponseModel *)dataModel
                   handler:(void (^)(void))handler
          serviceItemClick:(void (^)(FNMrFreshTextModel *))serviceItemClick {
    
    self.dataModel = dataModel;
    self.handler = handler;
    self.serviceItemClick = serviceItemClick;
    
    self.storeNameLabel.text = dataModel.showName;
    self.addressLabel.text = dataModel.storeAddr;
    
    self.btnView.hidden = [FNFreshUser shareInstance].isLogin;
    if ([FNFreshUser shareInstance].isLogin && dataModel.storeService.count > 0) {
        self.serviceView.hidden = false;
        
        FNMrFreshTextModel *model = [dataModel.storeService safeObjectAtIndex:0];
        self.leftServiceLabel.text = model.title;
        self.leftServiceIconImageView.image = model.type == 60 ? [UIImage imageNamed:@"icon_viper_code"] : [UIImage imageNamed:@"icon_vouchers"];
        
        FNMrFreshTextModel *secModel = [dataModel.storeService safeObjectAtIndex:1];
        self.rightServiceLabel.text = secModel.title;
        self.rightServiceIconImageView.image = secModel.type == 60 ? [UIImage imageNamed:@"icon_viper_code"] : [UIImage imageNamed:@"icon_vouchers"];
    } else {
        self.serviceView.hidden = YES;
    }
}

- (void)btnClick:(id)sender {
    !self.handler?:self.handler();
    
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"143004",
        @"page_id" :@"160",
        @"track_type":@"2",
        @"col_position":self.dataModel.btnStatus
    }];
}

- (void)leftServiceViewClick {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"143007",
        @"page_id" :@"160",
        @"track_type":@"2",
    }];
    [self serviceViewClick:true];
}

- (void)rightServiceViewClick {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"143008",
        @"page_id" :@"160",
        @"track_type":@"2",
    }];
    [self serviceViewClick:false];
}

- (void)serviceViewClick:(BOOL)isLeft {
    FNMrFreshTextModel *model;
    if (isLeft) {
        model = [self.dataModel.storeService safeObjectAtIndex:0];
    } else {
        model = [self.dataModel.storeService safeObjectAtIndex:1];
    }
    !self.serviceItemClick?:self.serviceItemClick(model);
}

@end
