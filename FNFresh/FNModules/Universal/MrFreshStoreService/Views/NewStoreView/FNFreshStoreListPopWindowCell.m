//
//  FNFreshStoreListPopWindowCell.m
//  FNFresh
//
//  Created by wang<PERSON> on 2020/10/29.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshStoreListPopWindowCell.h"

@interface FNFreshStoreListPopWindowCell ()

@property (weak, nonatomic) IBOutlet UIImageView *logoImgView;
@property (weak, nonatomic) IBOutlet UILabel *shopNameLab;
@property (weak, nonatomic) IBOutlet UILabel *distanceLab;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bgViewLeading;

@end

@implementation FNFreshStoreListPopWindowCell

- (void)awakeFromNib {
    [super awakeFromNib];
    self.bgViewLeading.constant = 20*Ratio;
}

- (void)setShopInfo:(FNFreshShopInfoModel *)shopInfo {
    _shopInfo = shopInfo;
    if (shopInfo.storeType == 3) {
        self.logoImgView.image = [UIImage imageNamed:@"logo_mini"];
        self.shopNameLab.text = [NSString stringWithFormat:@"大润发mini(%@)",shopInfo.shopName];
    } else {
        self.logoImgView.image = [UIImage imageNamed:@"mini_logo_rt"];
        self.shopNameLab.text = [NSString stringWithFormat:@"大润发(%@)",shopInfo.shopName];
    }
    self.distanceLab.text = shopInfo.distance;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end
