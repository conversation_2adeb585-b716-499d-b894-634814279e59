<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="18122" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="18093"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshNewStoreGiftCouponCollectionCell">
            <rect key="frame" x="0.0" y="0.0" width="292" height="81"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="292" height="81"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg_coupon_newstore" translatesAutoresizingMaskIntoConstraints="NO" id="t1p-hV-fuG">
                        <rect key="frame" x="0.0" y="0.0" width="292" height="81"/>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="newstore_tip_01" translatesAutoresizingMaskIntoConstraints="NO" id="U3G-P6-N68">
                        <rect key="frame" x="0.0" y="0.0" width="60" height="18"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="60" id="N9j-8H-327"/>
                            <constraint firstAttribute="width" secondItem="U3G-P6-N68" secondAttribute="height" multiplier="10:3" id="bPq-c4-emR"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="新人礼包" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fzh-Y4-DMi">
                        <rect key="frame" x="0.0" y="0.0" width="60" height="18"/>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="clip" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="F3C-Sq-lmM">
                        <rect key="frame" x="0.0" y="23" width="83" height="35"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="35" id="4Lz-wF-T04"/>
                            <constraint firstAttribute="width" constant="83" id="Aof-Ih-Bpv"/>
                        </constraints>
                        <attributedString key="attributedText">
                            <fragment content="¥">
                                <attributes>
                                    <color key="NSColor" red="0.95686274509803915" green="0.2627450980392157" blue="0.2627450980392157" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <font key="NSFont" size="18" name="PingFangSC-Semibold"/>
                                    <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                </attributes>
                            </fragment>
                            <fragment content=" ">
                                <attributes>
                                    <color key="NSColor" red="0.95686274509803915" green="0.2627450980392157" blue="0.2627450980392157" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <font key="NSFont" size="12" name="PingFangSC-Semibold"/>
                                    <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                </attributes>
                            </fragment>
                            <fragment content="8">
                                <attributes>
                                    <color key="NSColor" red="0.95686274509803915" green="0.2627450980392157" blue="0.2627450980392157" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <font key="NSFont" size="32" name="PingFangSC-Semibold"/>
                                    <paragraphStyle key="NSParagraphStyle" alignment="center" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                </attributes>
                            </fragment>
                        </attributedString>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="券的名称文案编辑文案编辑辑" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bu8-sF-co0" customClass="FNTagLabel">
                        <rect key="frame" x="95" y="12" width="185" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="K07-QK-JjW"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="14"/>
                        <color key="textColor" red="0.20000000000000001" green="0.20000000000000001" blue="0.20000000000000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="[线上专享] " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6ii-mS-h95">
                        <rect key="frame" x="95" y="32.5" width="67" height="16"/>
                        <fontDescription key="fontDescription" type="system" weight="medium" pointSize="13"/>
                        <color key="textColor" red="0.94509803921568625" green="0.25098039215686274" blue="0.25098039215686274" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="互通券" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="r3P-9I-IYe">
                        <rect key="frame" x="166" y="33.5" width="36" height="14"/>
                        <color key="backgroundColor" red="0.396078431372549" green="0.47450980392156861" blue="0.88627450980392153" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="36" id="Y39-Hd-PVp"/>
                            <constraint firstAttribute="height" constant="14" id="iSS-MQ-pQg"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="4"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="使用条件文案编辑" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Rc-GQ-iS8">
                        <rect key="frame" x="95" y="56" width="185" height="13"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="13" id="5A4-Fc-wh2"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="13"/>
                        <color key="textColor" red="0.40000000000000002" green="0.40000000000000002" blue="0.40000000000000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
            </view>
            <constraints>
                <constraint firstItem="bu8-sF-co0" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="12" id="1i1-vF-jbK"/>
                <constraint firstAttribute="trailing" secondItem="8Rc-GQ-iS8" secondAttribute="trailing" constant="12" id="2xB-fq-Zdu"/>
                <constraint firstItem="F3C-Sq-lmM" firstAttribute="centerY" secondItem="gTV-IL-0wX" secondAttribute="centerY" id="8ae-G0-3Ol"/>
                <constraint firstItem="t1p-hV-fuG" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="A3B-fr-aRy"/>
                <constraint firstItem="fzh-Y4-DMi" firstAttribute="trailing" secondItem="U3G-P6-N68" secondAttribute="trailing" id="AWl-JM-1T1"/>
                <constraint firstItem="U3G-P6-N68" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="CE3-fO-JgD"/>
                <constraint firstItem="U3G-P6-N68" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="GwK-pv-fW1"/>
                <constraint firstItem="fzh-Y4-DMi" firstAttribute="leading" secondItem="U3G-P6-N68" secondAttribute="leading" id="Jza-9q-bIK"/>
                <constraint firstItem="6ii-mS-h95" firstAttribute="leading" secondItem="F3C-Sq-lmM" secondAttribute="trailing" constant="12" id="LOX-3y-sV3"/>
                <constraint firstItem="F3C-Sq-lmM" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="NYU-F6-179"/>
                <constraint firstItem="fzh-Y4-DMi" firstAttribute="top" secondItem="U3G-P6-N68" secondAttribute="top" id="QLG-sH-2aD"/>
                <constraint firstItem="t1p-hV-fuG" firstAttribute="centerY" secondItem="gTV-IL-0wX" secondAttribute="centerY" id="QWq-o9-N2P"/>
                <constraint firstItem="t1p-hV-fuG" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="Whw-Es-ZNJ"/>
                <constraint firstItem="6ii-mS-h95" firstAttribute="top" secondItem="bu8-sF-co0" secondAttribute="bottom" constant="5.5" id="XDY-tf-NMA"/>
                <constraint firstItem="r3P-9I-IYe" firstAttribute="leading" secondItem="6ii-mS-h95" secondAttribute="trailing" constant="4" id="fE3-pR-Hra"/>
                <constraint firstItem="fzh-Y4-DMi" firstAttribute="bottom" secondItem="U3G-P6-N68" secondAttribute="bottom" id="fTF-5N-ZvG"/>
                <constraint firstItem="bu8-sF-co0" firstAttribute="leading" secondItem="F3C-Sq-lmM" secondAttribute="trailing" constant="12" id="fa2-zh-qLQ"/>
                <constraint firstAttribute="trailing" secondItem="bu8-sF-co0" secondAttribute="trailing" constant="12" id="og1-u6-TOT"/>
                <constraint firstItem="8Rc-GQ-iS8" firstAttribute="leading" secondItem="F3C-Sq-lmM" secondAttribute="trailing" constant="12" id="sH4-nM-xhk"/>
                <constraint firstAttribute="bottom" secondItem="8Rc-GQ-iS8" secondAttribute="bottom" constant="12" id="vAd-BN-r1R"/>
                <constraint firstItem="t1p-hV-fuG" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="xVx-sm-r4d"/>
                <constraint firstItem="r3P-9I-IYe" firstAttribute="centerY" secondItem="6ii-mS-h95" secondAttribute="centerY" id="xwq-M2-RPm"/>
            </constraints>
            <size key="customSize" width="231" height="65"/>
            <connections>
                <outlet property="couponDescBottomConstraint" destination="vAd-BN-r1R" id="fBf-Nj-BNu"/>
                <outlet property="couponDescLab" destination="8Rc-GQ-iS8" id="coy-Ac-een"/>
                <outlet property="couponNameLab" destination="bu8-sF-co0" id="9Ug-z2-Sft"/>
                <outlet property="couponNameTopConstraint" destination="1i1-vF-jbK" id="7gY-VJ-bdX"/>
                <outlet property="couponTagLab" destination="6ii-mS-h95" id="Tym-yi-cgS"/>
                <outlet property="couponValue" destination="F3C-Sq-lmM" id="ROl-XC-GGg"/>
                <outlet property="tipBgImgView" destination="U3G-P6-N68" id="FKl-Xn-3hc"/>
                <outlet property="tipNameLab" destination="fzh-Y4-DMi" id="Weo-Kh-OHz"/>
                <outlet property="unitTagLab" destination="r3P-9I-IYe" id="BiL-mQ-aQ6"/>
            </connections>
            <point key="canvasLocation" x="266.66666666666669" y="76.004464285714278"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="bg_coupon_newstore" width="292" height="80.5"/>
        <image name="newstore_tip_01" width="60" height="18"/>
    </resources>
</document>
