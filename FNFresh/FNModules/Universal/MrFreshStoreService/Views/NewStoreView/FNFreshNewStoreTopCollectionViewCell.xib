<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="FNFreshNewStoreTopCollectionViewCell">
            <rect key="frame" x="0.0" y="0.0" width="399" height="526"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="399" height="526"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="new_store_bg" translatesAutoresizingMaskIntoConstraints="NO" id="dRS-KR-vMU">
                        <rect key="frame" x="0.0" y="44" width="399" height="482"/>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="new_store_logo" translatesAutoresizingMaskIntoConstraints="NO" id="5vr-tU-aoF">
                        <rect key="frame" x="141.5" y="64" width="116" height="44"/>
                    </imageView>
                    <view contentMode="scaleToFill" placeholderIntrinsicWidth="96" placeholderIntrinsicHeight="30" translatesAutoresizingMaskIntoConstraints="NO" id="gM4-ao-WJz">
                        <rect key="frame" x="94" y="130" width="211" height="30"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="闸北店" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="B44-nW-tkO">
                                <rect key="frame" x="0.0" y="0.0" width="85.5" height="30"/>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="28"/>
                                <color key="textColor" red="1" green="0.90588235289999997" blue="0.54117647059999996" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="即将开业" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="go7-56-lqc">
                                <rect key="frame" x="97.5" y="0.0" width="113.5" height="30"/>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="28"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="go7-56-lqc" secondAttribute="trailing" id="Ebn-qK-UAy"/>
                            <constraint firstItem="B44-nW-tkO" firstAttribute="centerY" secondItem="gM4-ao-WJz" secondAttribute="centerY" id="Hgu-44-Sgl"/>
                            <constraint firstItem="go7-56-lqc" firstAttribute="height" secondItem="gM4-ao-WJz" secondAttribute="height" id="LBv-ed-tjs"/>
                            <constraint firstItem="B44-nW-tkO" firstAttribute="leading" secondItem="gM4-ao-WJz" secondAttribute="leading" id="VkA-Wd-CKl"/>
                            <constraint firstItem="B44-nW-tkO" firstAttribute="height" secondItem="gM4-ao-WJz" secondAttribute="height" id="ZbM-9f-lxB"/>
                            <constraint firstItem="go7-56-lqc" firstAttribute="centerY" secondItem="B44-nW-tkO" secondAttribute="centerY" id="aI2-KH-w5h"/>
                            <constraint firstAttribute="height" constant="30" id="e00-Ln-Rdh"/>
                            <constraint firstItem="go7-56-lqc" firstAttribute="leading" secondItem="B44-nW-tkO" secondAttribute="trailing" constant="12" id="oQJ-mi-Ynr"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="浙江省杭州市姑嫂树路与后湖路交叉口江旺路7号 写不下就转行" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NHy-P0-XKH">
                        <rect key="frame" x="20" y="168" width="369" height="36"/>
                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                        <color key="textColor" red="1" green="0.90588235289999997" blue="0.54117647059999996" alpha="1" colorSpace="calibratedRGB"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <view contentMode="scaleToFill" horizontalCompressionResistancePriority="250" placeholderIntrinsicWidth="220" placeholderIntrinsicHeight="52" translatesAutoresizingMaskIntoConstraints="NO" id="KPQ-Av-9j6">
                        <rect key="frame" x="111.5" y="226" width="176.5" height="51"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="button_newstore_01" translatesAutoresizingMaskIntoConstraints="NO" id="pYe-Su-0jU">
                                <rect key="frame" x="0.0" y="0.0" width="176.5" height="51"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="立即加入会员" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iyW-1K-sLA">
                                <rect key="frame" x="27" y="12" width="122.5" height="21"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="21" id="FYO-zL-q5J"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="20"/>
                                <color key="textColor" red="0.46666666670000001" green="0.1647058824" blue="0.1058823529" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="pYe-Su-0jU" firstAttribute="centerX" secondItem="KPQ-Av-9j6" secondAttribute="centerX" id="7uS-1B-ALi"/>
                            <constraint firstItem="pYe-Su-0jU" firstAttribute="centerY" secondItem="KPQ-Av-9j6" secondAttribute="centerY" id="85h-75-8WR"/>
                            <constraint firstAttribute="height" constant="51" id="IAG-78-Vzv"/>
                            <constraint firstItem="pYe-Su-0jU" firstAttribute="top" secondItem="KPQ-Av-9j6" secondAttribute="top" id="dV0-z9-Djc"/>
                            <constraint firstItem="iyW-1K-sLA" firstAttribute="leading" secondItem="KPQ-Av-9j6" secondAttribute="leading" constant="27" id="hQX-Lb-Ays"/>
                            <constraint firstItem="iyW-1K-sLA" firstAttribute="top" secondItem="KPQ-Av-9j6" secondAttribute="top" constant="12" id="kEk-A9-FIr"/>
                            <constraint firstItem="iyW-1K-sLA" firstAttribute="centerX" secondItem="KPQ-Av-9j6" secondAttribute="centerX" id="nGv-3U-Mqn"/>
                            <constraint firstItem="pYe-Su-0jU" firstAttribute="leading" secondItem="KPQ-Av-9j6" secondAttribute="leading" id="w8s-Rw-fSo"/>
                        </constraints>
                    </view>
                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dLN-vv-4lU">
                        <rect key="frame" x="76.5" y="219" width="246" height="40"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HYf-LR-dY9">
                                <rect key="frame" x="0.0" y="0.0" width="117" height="40"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zaq-ud-1k3">
                                        <rect key="frame" x="19.5" y="0.0" width="78" height="40"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_vouchers" translatesAutoresizingMaskIntoConstraints="NO" id="B9k-Tv-82V">
                                                <rect key="frame" x="0.0" y="7.5" width="25" height="25"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="25" id="QLj-LN-KpZ"/>
                                                    <constraint firstAttribute="width" constant="25" id="o4d-JN-tAp"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="优惠券" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="h1B-0E-lha">
                                                <rect key="frame" x="29" y="10.5" width="49" height="19.5"/>
                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                <color key="textColor" red="0.20000001789999999" green="0.20000001789999999" blue="0.20000001789999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="h1B-0E-lha" firstAttribute="centerY" secondItem="zaq-ud-1k3" secondAttribute="centerY" id="9jI-VF-Dle"/>
                                            <constraint firstItem="h1B-0E-lha" firstAttribute="leading" secondItem="B9k-Tv-82V" secondAttribute="trailing" constant="4" id="XRh-8L-MkQ"/>
                                            <constraint firstAttribute="trailing" secondItem="h1B-0E-lha" secondAttribute="trailing" id="oBp-s6-5pK"/>
                                            <constraint firstItem="B9k-Tv-82V" firstAttribute="centerY" secondItem="zaq-ud-1k3" secondAttribute="centerY" id="sf6-eT-xJP"/>
                                            <constraint firstItem="B9k-Tv-82V" firstAttribute="leading" secondItem="zaq-ud-1k3" secondAttribute="leading" id="tLw-Qq-Cyg"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.98823523520000001" green="0.92941176889999999" blue="0.92156863209999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="117" id="0sp-aT-uIX"/>
                                    <constraint firstItem="zaq-ud-1k3" firstAttribute="top" secondItem="HYf-LR-dY9" secondAttribute="top" id="Jda-Gc-ePJ"/>
                                    <constraint firstItem="zaq-ud-1k3" firstAttribute="centerX" secondItem="HYf-LR-dY9" secondAttribute="centerX" id="O5b-eR-rXG"/>
                                    <constraint firstAttribute="height" constant="40" id="PLc-44-m5e"/>
                                    <constraint firstAttribute="bottom" secondItem="zaq-ud-1k3" secondAttribute="bottom" id="mzy-r1-Xhp"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="20"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="A2L-f9-szv">
                                <rect key="frame" x="129" y="0.0" width="117" height="40"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vJy-Ou-THu">
                                        <rect key="frame" x="19.5" y="0.0" width="78" height="40"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_viper_code" translatesAutoresizingMaskIntoConstraints="NO" id="ktR-ar-exs">
                                                <rect key="frame" x="0.0" y="7.5" width="25" height="25"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="25" id="p5A-ci-PoI"/>
                                                    <constraint firstAttribute="height" constant="25" id="zRJ-xv-Znl"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="会员卡" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Op-s5-URs">
                                                <rect key="frame" x="29" y="10.5" width="49" height="19.5"/>
                                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                                <color key="textColor" red="0.20000001789999999" green="0.20000001789999999" blue="0.20000001789999999" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="8Op-s5-URs" secondAttribute="trailing" id="5ke-jS-nf5"/>
                                            <constraint firstItem="8Op-s5-URs" firstAttribute="centerY" secondItem="vJy-Ou-THu" secondAttribute="centerY" id="GeH-XX-r9e"/>
                                            <constraint firstItem="ktR-ar-exs" firstAttribute="leading" secondItem="vJy-Ou-THu" secondAttribute="leading" id="V0w-gz-tU1"/>
                                            <constraint firstItem="8Op-s5-URs" firstAttribute="leading" secondItem="ktR-ar-exs" secondAttribute="trailing" constant="4" id="mBL-Pf-s8H"/>
                                            <constraint firstItem="ktR-ar-exs" firstAttribute="centerY" secondItem="vJy-Ou-THu" secondAttribute="centerY" id="ycm-if-EcG"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.98823523520000001" green="0.92549026010000002" blue="0.92156863209999995" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                <constraints>
                                    <constraint firstItem="vJy-Ou-THu" firstAttribute="centerX" secondItem="A2L-f9-szv" secondAttribute="centerX" id="7z5-zg-MBv"/>
                                    <constraint firstItem="vJy-Ou-THu" firstAttribute="top" secondItem="A2L-f9-szv" secondAttribute="top" id="Uvo-4l-t9N"/>
                                    <constraint firstAttribute="bottom" secondItem="vJy-Ou-THu" secondAttribute="bottom" id="j1L-Qc-zHb"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="20"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="A2L-f9-szv" secondAttribute="bottom" id="4ko-26-5Vk"/>
                            <constraint firstItem="A2L-f9-szv" firstAttribute="width" secondItem="HYf-LR-dY9" secondAttribute="width" id="EIH-qn-1cX"/>
                            <constraint firstAttribute="bottom" secondItem="HYf-LR-dY9" secondAttribute="bottom" id="FWI-Jw-iTB"/>
                            <constraint firstAttribute="trailing" secondItem="A2L-f9-szv" secondAttribute="trailing" id="Kl3-lJ-t5L"/>
                            <constraint firstItem="A2L-f9-szv" firstAttribute="height" secondItem="HYf-LR-dY9" secondAttribute="height" id="Ncd-IY-NyU"/>
                            <constraint firstItem="A2L-f9-szv" firstAttribute="leading" secondItem="HYf-LR-dY9" secondAttribute="trailing" constant="12" id="OXM-oP-4Lt"/>
                            <constraint firstItem="A2L-f9-szv" firstAttribute="top" secondItem="dLN-vv-4lU" secondAttribute="top" id="sB7-Oy-Ieu"/>
                            <constraint firstItem="HYf-LR-dY9" firstAttribute="leading" secondItem="dLN-vv-4lU" secondAttribute="leading" id="w6E-aL-gZN"/>
                            <constraint firstItem="HYf-LR-dY9" firstAttribute="top" secondItem="dLN-vv-4lU" secondAttribute="top" id="yIo-F4-1YL"/>
                        </constraints>
                    </view>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="44Q-hf-oUC">
                        <rect key="frame" x="0.0" y="510" width="399" height="32"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="32" id="XRv-x1-wzq"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="16"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
            </view>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="dRS-KR-vMU" secondAttribute="bottom" id="0yH-TB-ymx"/>
                <constraint firstAttribute="trailing" secondItem="NHy-P0-XKH" secondAttribute="trailing" constant="10" id="2Kb-ky-Cgr"/>
                <constraint firstItem="NHy-P0-XKH" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="20" id="6Dd-4f-ZbW"/>
                <constraint firstItem="KPQ-Av-9j6" firstAttribute="top" secondItem="NHy-P0-XKH" secondAttribute="bottom" constant="22" id="FNd-ys-ksA"/>
                <constraint firstAttribute="trailing" secondItem="44Q-hf-oUC" secondAttribute="trailing" id="Fwm-fP-Wzc"/>
                <constraint firstAttribute="trailing" secondItem="dRS-KR-vMU" secondAttribute="trailing" id="Imt-KV-pbr"/>
                <constraint firstItem="gM4-ao-WJz" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="JlH-Ia-yJp"/>
                <constraint firstItem="dRS-KR-vMU" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="KxH-bd-pIH"/>
                <constraint firstItem="gM4-ao-WJz" firstAttribute="top" secondItem="5vr-tU-aoF" secondAttribute="bottom" constant="22" id="Nza-qR-r6s"/>
                <constraint firstItem="dLN-vv-4lU" firstAttribute="top" secondItem="NHy-P0-XKH" secondAttribute="bottom" constant="15" id="OFS-tk-8nl"/>
                <constraint firstAttribute="bottom" secondItem="44Q-hf-oUC" secondAttribute="bottom" constant="-16" id="SGR-cX-MKy"/>
                <constraint firstItem="dRS-KR-vMU" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="44" id="UpA-B1-1ho"/>
                <constraint firstItem="44Q-hf-oUC" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Zz5-p8-jUM"/>
                <constraint firstItem="gM4-ao-WJz" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="12" id="drE-KQ-wS3"/>
                <constraint firstItem="5vr-tU-aoF" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="hUZ-Nv-rTA"/>
                <constraint firstItem="5vr-tU-aoF" firstAttribute="top" secondItem="dRS-KR-vMU" secondAttribute="top" constant="20" id="mcT-oT-bMP"/>
                <constraint firstItem="NHy-P0-XKH" firstAttribute="top" secondItem="gM4-ao-WJz" secondAttribute="bottom" constant="8" id="qO4-Z8-ZIu"/>
                <constraint firstItem="KPQ-Av-9j6" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="wBJ-r7-be4"/>
                <constraint firstItem="dLN-vv-4lU" firstAttribute="centerX" secondItem="gTV-IL-0wX" secondAttribute="centerX" id="zBN-oW-8Kl"/>
            </constraints>
            <size key="customSize" width="399" height="526"/>
            <connections>
                <outlet property="addressLabel" destination="NHy-P0-XKH" id="Oi9-F6-Fc3"/>
                <outlet property="btnView" destination="KPQ-Av-9j6" id="1hq-1Z-RIW"/>
                <outlet property="leftServiceIconImageView" destination="B9k-Tv-82V" id="wey-fT-b6x"/>
                <outlet property="leftServiceLabel" destination="h1B-0E-lha" id="FkV-wj-0Tx"/>
                <outlet property="leftServiceView" destination="HYf-LR-dY9" id="vB1-3i-8vT"/>
                <outlet property="rightServiceIconImageView" destination="ktR-ar-exs" id="WGd-qB-rmR"/>
                <outlet property="rightServiceLabel" destination="8Op-s5-URs" id="gvu-pu-tfZ"/>
                <outlet property="rightServiceView" destination="A2L-f9-szv" id="zUx-ze-rqH"/>
                <outlet property="serviceView" destination="dLN-vv-4lU" id="1Ot-1k-3JB"/>
                <outlet property="storeNameLabel" destination="B44-nW-tkO" id="eeO-Zl-kwR"/>
            </connections>
            <point key="canvasLocation" x="65.94202898550725" y="33.482142857142854"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="button_newstore_01" width="49.5" height="52"/>
        <image name="icon_viper_code" width="30" height="30"/>
        <image name="icon_vouchers" width="30.5" height="30"/>
        <image name="new_store_bg" width="375" height="400.5"/>
        <image name="new_store_logo" width="116" height="44"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
