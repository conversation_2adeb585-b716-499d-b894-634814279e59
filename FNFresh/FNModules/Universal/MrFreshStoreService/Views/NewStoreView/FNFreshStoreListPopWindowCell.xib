<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="77" id="KGk-i7-Jjw" customClass="FNFreshStoreListPopWindowCell">
            <rect key="frame" x="0.0" y="0.0" width="325" height="85"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="325" height="85"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5Dq-se-TPh">
                        <rect key="frame" x="20" y="5" width="285" height="75"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mini_logo_rt" translatesAutoresizingMaskIntoConstraints="NO" id="66f-2B-eey">
                                <rect key="frame" x="10" y="10" width="55" height="55"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="55" id="bhf-do-R25"/>
                                    <constraint firstAttribute="width" secondItem="66f-2B-eey" secondAttribute="height" multiplier="1:1" id="gio-4R-5QS"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="大润发（天心店）" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="frU-Y5-998">
                                <rect key="frame" x="77" y="16" width="131" height="16"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="16" id="ZiW-wY-lwb"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="16"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mini_icon_location" translatesAutoresizingMaskIntoConstraints="NO" id="dZH-3V-39l">
                                <rect key="frame" x="77" y="42" width="15" height="18"/>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="3.1km" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lXP-Zs-I8d">
                                <rect key="frame" x="97" y="44" width="38" height="14"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="14" id="b8u-du-RCn"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="frU-Y5-998" firstAttribute="top" secondItem="5Dq-se-TPh" secondAttribute="top" constant="16" id="68l-S7-23P"/>
                            <constraint firstItem="dZH-3V-39l" firstAttribute="centerY" secondItem="lXP-Zs-I8d" secondAttribute="centerY" id="8j2-3l-AmA"/>
                            <constraint firstItem="66f-2B-eey" firstAttribute="centerY" secondItem="5Dq-se-TPh" secondAttribute="centerY" id="9AA-XK-fqw"/>
                            <constraint firstItem="lXP-Zs-I8d" firstAttribute="top" secondItem="frU-Y5-998" secondAttribute="bottom" constant="12" id="G2w-PO-hCl"/>
                            <constraint firstItem="dZH-3V-39l" firstAttribute="leading" secondItem="66f-2B-eey" secondAttribute="trailing" constant="12" id="O4P-cI-qqm"/>
                            <constraint firstItem="lXP-Zs-I8d" firstAttribute="leading" secondItem="dZH-3V-39l" secondAttribute="trailing" constant="5" id="Tnp-DA-jMq"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="lXP-Zs-I8d" secondAttribute="trailing" constant="15" id="UN4-mc-e0R"/>
                            <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="frU-Y5-998" secondAttribute="trailing" constant="10" id="crX-Ys-Ymh"/>
                            <constraint firstAttribute="height" constant="75" id="eEU-Nx-i84"/>
                            <constraint firstItem="66f-2B-eey" firstAttribute="leading" secondItem="5Dq-se-TPh" secondAttribute="leading" constant="10" id="i7U-Zj-7vQ"/>
                            <constraint firstItem="frU-Y5-998" firstAttribute="leading" secondItem="66f-2B-eey" secondAttribute="trailing" constant="12" id="qll-ny-wd2"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="10"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                <real key="value" value="0.5"/>
                            </userDefinedRuntimeAttribute>
                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                <color key="value" red="0.73333333333333328" green="0.73333333333333328" blue="0.73333333333333328" alpha="1" colorSpace="calibratedRGB"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="5Dq-se-TPh" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="Q61-by-5hQ"/>
                    <constraint firstItem="5Dq-se-TPh" firstAttribute="centerX" secondItem="H2p-sc-9uM" secondAttribute="centerX" id="eUx-6q-uLv"/>
                    <constraint firstItem="5Dq-se-TPh" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="wkP-i2-o15"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="bgViewLeading" destination="wkP-i2-o15" id="kG1-Wd-nrh"/>
                <outlet property="distanceLab" destination="lXP-Zs-I8d" id="apP-qz-xgv"/>
                <outlet property="logoImgView" destination="66f-2B-eey" id="sfb-Zm-wIC"/>
                <outlet property="shopNameLab" destination="frU-Y5-998" id="4xF-ch-Zd8"/>
            </connections>
            <point key="canvasLocation" x="31.159420289855074" y="76.004464285714278"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="mini_icon_location" width="14.5" height="18"/>
        <image name="mini_logo_rt" width="55" height="55"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
