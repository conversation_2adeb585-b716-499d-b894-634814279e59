//
//  FNFreshStoreServiceEventNameString.h
//  FNFresh
//
//  Created by <PERSON><PERSON><PERSON> on 2024/1/30.
//  Copyright © 2024 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>

typedef NSString *FNFreshStoreServiceEventNameEnum NS_TYPED_ENUM;
/**
 1.头部view 事件名字
 */
//1.地址点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventAddressModuleClick;
//2.phone 点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventPhoneButtonClick;
//3.说明点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventInfoButtonClick;
//4.立即登录点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventAtOnceLoginClick;
//5.优惠券点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventCouponsClick;
//6.成长值点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventGrowUpClick;
//7.购物卡点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventShoppingCardClick;
//8.我的会员卡点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventMemberShipCardClick;

#pragma mark - 品牌集合、积分商城
//更多按钮 点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventNameEnumMoreClickValue;

#pragma mark - 金刚区
//金刚区  展开 view点击
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventNameEnumKingKongUnfoldViewClickValue;


//打开链接
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventOpenUrl;

//进入商详
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventJumpToProductDetail;

//加入购物车
extern FNFreshStoreServiceEventNameEnum const FNFreshStoreServiceEventAddToShopCart;
