//
//  FNFreshNewStoreViewModel.h
//  FNFresh
//
//  Created by wang<PERSON> on 2020/10/23.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshNewStoreHomeInfoResponseModel.h"
#import "FNFreshStoreServiceSectionModel.h"
#import "FNFreshStoreServiceModel.h"

@interface FNFreshNewStoreViewModel : NSObject

@property (strong, nonatomic, readonly) FNFreshNewStoreHomeInfoResponseModel *responseModel;

@property (assign, nonatomic) BOOL hasShoppingStreetIntegralData;

//顶部cell 高度
- (CGFloat)heightForTopViewCell;
- (NSInteger)numberOfSection;
- (NSInteger)numberOfItemsInSection:(NSInteger)section;
- (CGSize)sizeForItemAtIndexPath:(NSIndexPath *)indexPath;
- (FNFreshStoreServiceSectionModel *)sectionModelAtSection:(NSUInteger)section;

/// 请求门店首页数据
/// @param callBack 回调block
- (void)requestNewStoreHomeIndexWithCallBack:(void(^)(BOOL isSuccess, NSError *error))callBack;

@end
