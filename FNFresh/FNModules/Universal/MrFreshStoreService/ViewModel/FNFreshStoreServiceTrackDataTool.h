//
//  FNFreshStoreServiceTrackDataTool.h
//  FNFresh
//
//  Created by xn on 2022/1/12.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshStoreServiceModel.h"
#import "FNFreshStoreServiceSectionModel.h"
#import "FNFreshBuryEnumerationKeysString.h"

typedef NS_ENUM(NSUInteger,FNFreshStoreServiceTrackType) {
    FNFreshStoreServiceTrack_clickShoppingCard = 100,//点击购物卡
    FNFreshStoreServiceTrack_clickPointsMall,//点击积分商城模块,1、积分换购；2、积分兑券；3、积分兑权益
};

@interface FNFreshStoreServiceTrackDataTool : NSObject

#pragma mark 浏览门店频道页
- (void)agentStoreServiceVCDidAppear;

#pragma mark 门店频道页 点击切店按钮
- (void)agentClickChangeStore;

#pragma mark 门店频道页 点击电话按钮
- (void)agentClickPhone;

#pragma mark 门店频道页 点击门店服务按钮组
- (void)agentClickMemberServiceWithIndex:(NSInteger)index;

#pragma mark 门店频道页 点击会员卡按钮
- (void)agentClickMemberCard;

#pragma mark 门店频道页 点击优惠卡券按钮
- (void)agentClickMyCoupons;

#pragma mark 门店频道页 点击轮播banner
- (void)agentClickStoreBannerWithIndex:(NSInteger)index;

#pragma mark 门店频道页 今日省钱模块曝光
- (void)agentSaveMoneyModuleDidAppear;

#pragma mark 门店频道页 点击今日省钱领券中心
- (void)agentClickCouponModule;

#pragma mark 门店频道页 点击今日省钱当日限定
- (void)agentClickLimitSales;

#pragma mark 门店频道页 点击今日省钱左下banner 164023 & 点击今日省钱右下banner 164024
- (void)agentClickSaveMoneyPicResourcesIsRight:(BOOL)isRight;

#pragma mark 门店频道页 活动报名&促销海报模块曝光
- (void)agentApplyAndPosterModuleDidAppear;

#pragma mark 门店频道页 点击活动报名模块 164028 & 点击促销海报模块 164029
- (void)agentClickApplyOrPosterItem:(BOOL)isApply;

#pragma mark 门店频道页 商店街模块曝光
- (void)agentShoppingStreetModuleDidAppear:(BOOL)isNew;

#pragma mark 门店频道页 点击商店街模块
- (void)agentClickShoppingStreet;

#pragma mark 门店频道页 点点击新版商店街模块
- (void)agentClickShoppingStreetNew:(NSInteger)index;

#pragma mark 门店频道页 点击积分模块图片
- (void)agentClickShoppingStreetIntegralItem;

#pragma mark 门店频道页 排行榜大模块曝光
- (void)agentHotSellingModuleDidAppear;

#pragma mark 门店频道页 点击排行榜大模块进内页
- (void)agentClickHotSellingItemWithIndex:(NSInteger)index;

#pragma mark 门店频道页 横幅banner模块曝光
- (void)agentSingleBannerDidAppear;

#pragma mark 门店频道页 点击横幅banner
- (void)agentClickSingleBanner;

#pragma mark 点击购物卡按钮
- (void)agentClickShoppingCardButton;

#pragma mark 点击成长值按钮
- (void)agentClickGrowUpButton;

#pragma mark 点击规则按钮
- (void)agentClickRuleInfo;

#pragma mark 点击商品陈列模块线上商品进商详 / 点击商品陈列模块线下商品
- (void)agentClickGoodsModuleEnterDeatil:(NSString *_Nullable)goodsId isOffline:(BOOL)isOffline;

#pragma mark 点击商品陈列模块线上商品加购
- (void)agentClickGoodsModuleAddToShopCart:(NSString *_Nullable)goodsId;

#pragma mark 新轮播banner曝光
+ (void)agentCarouserDidAppear:(NSInteger)index;

#pragma mark 点击新轮播banner
+ (void)agentClickCarouserItem:(NSInteger)index;

#pragma mark 点击点击领券中心模块
- (void)agentClickCouponCenter:(NSString *_Nullable)type;

#pragma mark 点击图片集合模块图片资源位
- (void)agentClickImagesCollection:(NSString *_Nonnull)styleType index:(NSInteger)index;

//模块曝光埋点
- (void)handleExposureTrackData:(FNFreshStoreServiceSectionModel *_Nullable)type;
//点击埋点
- (void)handleClickTrackData:(FNFreshStoreHomeSectionType)type index:(NSInteger)index;

//点击商品陈列模块头部更多按钮 和 点击商品陈列模块滑动到尾部更多按钮
- (void)handleClickGoodsModuleMore:(NSString *_Nonnull)style;

/** 埋点 */
+ (void)eventTrackFor:(FNFreshStoreServiceTrackType)type info:(NSDictionary *_Nullable)info;

@end

