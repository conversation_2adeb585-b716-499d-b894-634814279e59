//
//  FNFreshNewStoreViewModel.m
//  FNFresh
//
//  Created by wangbo on 2020/10/23.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshNewStoreViewModel.h"
#import "FNFreshMrFreshService.h"

@interface FNFreshNewStoreViewModel ()

@property (strong, nonatomic) FNFreshNewStoreHomeInfoResponseModel *responseModel;
@property (strong, nonatomic) NSMutableArray<FNFreshStoreServiceSectionModel *> *dataArray;

@end

@implementation FNFreshNewStoreViewModel

- (void)setupDataSource {
    self.hasShoppingStreetIntegralData = false;
    [self.dataArray removeAllObjects];

    FNFreshStoreServiceSectionModel *topModel = [FNFreshStoreServiceSectionModel new];
    topModel.sectionType = FNFreshMrFreshStoreTypeTopView;
    topModel.nStoreResponseModel = self.responseModel;
    [self.dataArray addObject:topModel];
    
    for (FNFreshStoreServiceModel *serviceItem in self.responseModel.content) {
        // 过滤掉已废弃的类型，避免显示空白区域
        if ([self isDeprecatedType:serviceItem.type]) {
            NSLog(@"新店页面过滤掉已废弃的模块类型: %lu, 模块名称: %@", (unsigned long)serviceItem.type, serviceItem.moduleName);
            continue;
        }

        /// 商店街联合模块 特殊处理
        if (serviceItem.type == FNFreshMrFreshStoreTypeShoppingStreetUnite) {
            
           self.hasShoppingStreetIntegralData = false;
           // 积分劵子模块
           if (serviceItem.picList2.count > 0) {
               FNFreshStoreServiceSectionModel *shoppingStreetIntegralModel = [FNFreshStoreServiceSectionModel new];
               shoppingStreetIntegralModel.sectionType = FNFreshMrFreshStoreTypeShoppingStreetIntegral;
               shoppingStreetIntegralModel.storeServiceModel = serviceItem;
               shoppingStreetIntegralModel.hasShoppingStreetData = serviceItem.picList.count > 0;
               self.hasShoppingStreetIntegralData = true;
               [self.dataArray addObject:shoppingStreetIntegralModel];
           }
           if (serviceItem.picList.count > 0) {
               FNFreshStoreServiceSectionModel *shoppingStreetItemModel = [FNFreshStoreServiceSectionModel new];
               shoppingStreetItemModel.storeServiceModel = serviceItem;
               shoppingStreetItemModel.hasShoppingStreetIntegralData = serviceItem.picList2.count > 0;

               //商店街样式类型（1：老式:2：新式）
               if ([serviceItem.styleType isEqual: @"1"]) {
                   shoppingStreetItemModel.sectionType = FNFreshMrFreshStoreTypeShoppingStreet;
               } else {
                   shoppingStreetItemModel.sectionType = FNFreshMrFreshStoreTypeShoppingStreetNew;
               }
               [self.dataArray addObject:shoppingStreetItemModel];
           }
        } else {
            FNFreshStoreServiceSectionModel *itemModel = [FNFreshStoreServiceSectionModel new];
            itemModel.sectionType = serviceItem.type;
            itemModel.storeServiceModel = serviceItem;
            
            [self.dataArray addObject:itemModel];
        }
    }
}

//顶部cell 高度
- (CGFloat)heightForTopViewCell {
    CGFloat topHeight = ceilf(SCREEN_WIDTH * 801 / 750.f);
    return topHeight + 44;
}

- (NSInteger)numberOfSection {
    return self.dataArray.count;
}

- (NSInteger)numberOfItemsInSection:(NSInteger)section {
    return 1;
}

- (FNFreshStoreServiceSectionModel *)sectionModelAtSection:(NSUInteger)section {
    return [self.dataArray safeObjectAtIndex:section];
}

- (CGSize)sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceSectionModel *sectionModel = [self sectionModelAtSection:indexPath.section];

    if (sectionModel.cellHeight > 0) {
        return CGSizeMake(sectionModel.cellWidth, sectionModel.cellHeight);
    }
    CGFloat cellHeight = 0.0;
    CGFloat cellWidth = SCREEN_WIDTH - 24;
    //351\42 SCREEN_WIDTH - 24
    CGFloat headImgHeight = ceilf((SCREEN_WIDTH - 24)*42/351);
    
    switch (sectionModel.sectionType) {
        case FNFreshMrFreshStoreTypeTopView: // 头部视图
            cellWidth = SCREEN_WIDTH;
            cellHeight = [self heightForTopViewCell];
            break;
        case FNFreshMrFreshStoreTypeShoppingStreet:
            if (sectionModel.hasShoppingStreetIntegralData) {
                cellHeight = floor(138*Ratio) + 50;
            } else {
                cellHeight = floor(138*Ratio) + 61;
            }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreetNew: { //新版商店街
            sectionModel.contentViewHeight = floor(92*Ratio);
            if (sectionModel.hasShoppingStreetIntegralData) {
                cellHeight = 45 + 2 * sectionModel.contentViewHeight + 8;
            } else {
                cellHeight = headImgHeight + 2 * sectionModel.contentViewHeight + 8;
            }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreetIntegral: //商店街积分模块
            if (sectionModel.storeServiceModel.picList2.count > 4) {
                sectionModel.contentViewHeight = floor(183*Ratio);
            } else {
                //165 201
                //(SCREEN_WIDTH - 46)/2
                sectionModel.contentViewHeight = 201.0/165 *(SCREEN_WIDTH - 46)/2;
            }
            CGFloat signHeight = sectionModel.hasShoppingStreetData ? 28 + 15 : 0 + 8;
            cellHeight = headImgHeight + sectionModel.contentViewHeight + signHeight;
            break;
        case FNFreshMrFreshStoreTypeSingleBanner://单个banner
            cellHeight = floor(Ratio * 89);
            break;
        default:
            cellHeight = 0.0;
        }
    }
    sectionModel.cellWidth = cellWidth;
    sectionModel.cellHeight = cellHeight;
    return CGSizeMake(cellWidth, cellHeight);
}

#pragma mark request
- (void)requestNewStoreHomeIndexWithCallBack:(void (^)(BOOL, NSError *))callBack {
    WS(weakSelf)
    [FNFreshMrFreshService requestNewStoreHomeInfoWithStoreId:[FNFreshUser shareInstance].lastShopId success:^(FNFreshNewStoreHomeInfoResponseModel *responseObject, BOOL isCache) {
        weakSelf.responseModel = responseObject;
        [weakSelf setupDataSource];
        [weakSelf performSelector:@selector(completeSuccess:) withObject:callBack afterDelay:1];
    } failure:^(id responseObject, NSError *error) {
        callBack(NO, error);
    }];
}

- (void)completeSuccess:(void (^)(BOOL, NSError *))callBack {
    callBack(YES, nil);
}

#pragma mark gette & setter
- (NSMutableArray<FNFreshStoreServiceSectionModel *> *)dataArray {
    if (!_dataArray) {
        _dataArray = [NSMutableArray array];
    }
    return _dataArray;
}

@end
