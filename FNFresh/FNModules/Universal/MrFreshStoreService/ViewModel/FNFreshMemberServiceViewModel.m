//
//  FNFreshMemberServiceViewModel.m
//  FNFresh
//
//  Created by wang<PERSON> on 2019/9/20.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

#import "FNFreshMemberServiceViewModel.h"
#import "FNFreshMrFreshStoreHomeParameterModel.h"
#import "FNFreshMrFreshService.h"
#import "FNFreshMrFreshBottomResponseModel.h"

#import "FNFreshStoreServiceResponseModel.h"
#import "FNFreshStoreServiceModuleTitleCell.h"
#import "FNFreshStoreServicesKingKongCell.h"
#import "FNFreshStoreServiceTrackDataTool.h"
#import "NSString+FNCBCAEScryption.h"
#import "UIDevice+FNScreenSize.h"

/// 获取indexPath
FNFreshOperationEnum const FNFreshOperationEnumStoreServiceGetIndexPath = @"FNFreshOperationEnumStoreServiceGetIndexPath";
/// 打开url
FNFreshOperationEnum const FNFreshOperationEnumStoreServiceOpenUrl = @"FNFreshOperationEnumStoreServiceOpenUrl";
/// 刷新Section
FNFreshOperationEnum const FNFreshOperationEnumStoreServiceRefreshSection = @"FNFreshOperationEnumStoreServiceRefreshSection";

@class FNFreshStoreServiceItemModel, FNFreshStoreServiceShopInfoModel;

@interface FNFreshMemberServiceViewModel ()

@property (strong, nonatomic) FNFreshStoreServiceResponseModel *responseModel;
//@property (strong, nonatomic) NSMutableArray<FNFreshStoreServiceSectionModel *> *dataArray;
//今日省钱模块 是否有今日限定数据
@property (assign, nonatomic) BOOL hasLimitedData;
//今日省钱模块 是否有领券中心数据
@property (assign, nonatomic) BOOL hasCouponCenterData;

@end

@implementation FNFreshMemberServiceViewModel

- (void)setDataSource {
    [self.dataArray removeAllObjects];
//    [self test];
    //1.处理顶部地址会员数据
    [self handleTopAddressHeaderData];
    
    for (FNFreshStoreServiceModel *serviceItem in self.responseModel.content) {
        /// 商店街联合模块 特殊处理
        if (serviceItem.type == FNFreshMrFreshStoreTypeShoppingStreetUnite) {
            
           self.hasShoppingStreetIntegralData = false;
           // 积分劵子模块
           if (serviceItem.picList2.count > 0) {
               FNFreshStoreServiceSectionModel *shoppingStreetIntegralModel = [FNFreshStoreServiceSectionModel new];
               shoppingStreetIntegralModel.sectionType = FNFreshMrFreshStoreTypeShoppingStreetIntegral;
               shoppingStreetIntegralModel.storeServiceModel = serviceItem;
               shoppingStreetIntegralModel.hasShoppingStreetData = serviceItem.picList.count > 0;
               self.hasShoppingStreetIntegralData = true;
               [self.dataArray addObject:shoppingStreetIntegralModel];
           }
           if (serviceItem.picList.count > 0) {
               FNFreshStoreServiceSectionModel *shoppingStreetItemModel = [FNFreshStoreServiceSectionModel new];
               shoppingStreetItemModel.storeServiceModel = serviceItem;
               shoppingStreetItemModel.hasShoppingStreetIntegralData = serviceItem.picList2.count > 0;

               //商店街样式类型（1：老式:2：新式）
               if ([serviceItem.styleType isEqual: @"1"]) {
                   shoppingStreetItemModel.sectionType = FNFreshMrFreshStoreTypeShoppingStreet;
               } else {
                   shoppingStreetItemModel.sectionType = FNFreshMrFreshStoreTypeShoppingStreetNew;
               }
               [self.dataArray addObject:shoppingStreetItemModel];
           }
        } else {
            FNFreshStoreServiceSectionModel *itemModel = [FNFreshStoreServiceSectionModel new];
            itemModel.sectionType = serviceItem.type;
            itemModel.storeServiceModel = serviceItem;
            
            //单独处理banner
            if (serviceItem.type == FNFreshMrFreshStoreTypeSingleBanner){
                if (serviceItem.pic.imgHeight && serviceItem.pic.imgWidth){
                    CGFloat imgHeight = [serviceItem.pic.imgHeight doubleValue];
                    CGFloat imgWidth = [serviceItem.pic.imgWidth doubleValue];
                    CGFloat cellWidth = SCREEN_WIDTH - 24;
                    CGFloat cellHeight = (imgHeight * 1.0) / (imgWidth * 1.0) * cellWidth;
                    itemModel.cellHeight = cellHeight;
                    itemModel.cellWidth = cellWidth;
                }
            }
            
            //今日省钱
            if (serviceItem.type == FNFreshMrFreshStoreTypeSaveMoney) {
                NSMutableArray *saveMoneyitemsArr = [NSMutableArray array];
                self.hasLimitedData = serviceItem.limited && serviceItem.limited.goodsList.count > 0;
                self.hasCouponCenterData = serviceItem.couponCenter && serviceItem.couponCenter.couponList > 0;
                if (self.hasLimitedData) {
                    FNFreshStoreServiceSaveMoneyItemModel *item = [FNFreshStoreServiceSaveMoneyItemModel new];
                    item.itemType = FNFreshStoreServiceSaveMoneyCouponsAndLimit;
                    item.couponCenter = serviceItem.couponCenter;
                    item.limited = serviceItem.limited;
                    [saveMoneyitemsArr addObject:item];
                } else if (self.hasCouponCenterData) {
                    FNFreshStoreServiceSaveMoneyItemModel *item = [FNFreshStoreServiceSaveMoneyItemModel new];
                    item.itemType = FNFreshStoreServiceSaveMoneyHorizontalCoupon;
                    item.couponCenter = serviceItem.couponCenter;
                    [saveMoneyitemsArr addObject:item];
                }
                
                if (serviceItem.picList.count > 0) {
                    FNFreshStoreServiceSaveMoneyItemModel *item = [FNFreshStoreServiceSaveMoneyItemModel new];
                    item.itemType = FNFreshStoreServiceSaveMoneyPicActivity;
                    item.picList = serviceItem.picList;
                    [saveMoneyitemsArr addObject:item];
                }
                itemModel.saveMoneyItemsType = [saveMoneyitemsArr copy];
            }
            
            if (serviceItem.type == FNFreshMrFreshStoreTypeKingKonog) {
                itemModel.unFlodStyle = serviceItem.picList.count > 5 ? FNFreshStoreServiceKingKongCellUnFoldType_unFlod : FNFreshStoreServiceKingKongCellUnFoldType_hidden;
            }
            
            if (serviceItem.type == FNFreshMrFreshStoreTypeGoodsModule) {
                if (serviceItem.tabList.count > 0) {
                    serviceItem.tabList.firstObject.isSelected = true;
                }
            }

            [self.dataArray addObject:itemModel];
        }
    }
}

#pragma mark -<处理数据>
/// 这里是处理头部View的数据
- (void)handleTopAddressHeaderData {
    FNFreshStoreServiceSectionModel *topModel = [FNFreshStoreServiceSectionModel new];
    topModel.sectionType = FNFreshMrFreshStoreTypeTopView;
    topModel.responseModel = self.responseModel;
    FNFreshStoreServicesHeaderAddressCellDataModel *cellDataModel = [FNFreshStoreServicesHeaderAddressCellDataModel new];
    topModel.topAddressHeaderDataModel = cellDataModel;
    cellDataModel.titleString = self.responseModel.storeName;
    cellDataModel.subTitleString = self.responseModel.storeAddress;
    cellDataModel.isShowPhone = ![self isSateliteCapsule];
    cellDataModel.isShowAddress = ![self isSateliteCapsule];
    cellDataModel.member_titleString = [self isLogin]? self.responseModel.saveMoneyTip : @"请登录以查看会员信息";
    cellDataModel.member_isShowLogin = ![self isLogin];
    cellDataModel.member_couponsValueString = [self isLogin] ? (self.responseModel.voucherCount.length > 0 ? self.responseModel.voucherCount : @"0") : @"*";
    cellDataModel.member_growUpValueString = [self isLogin] ? (self.responseModel.growthValue.length > 0 ? self.responseModel.growthValue : @"0") : @"*";
    cellDataModel.member_shoppingCardString = [self isLogin] ? (self.responseModel.shoppingCardBalance .length > 0 ? [NSString stringWithFormat:@"¥%@",[self.responseModel.shoppingCardBalance fn_aesDecrypt]] : @"¥0.0") : @"*";
    cellDataModel.member_myPointValueString = [self isLogin] ? (self.responseModel.integral.length > 0 ? self.responseModel.integral : @"0") : @"*";
    [self.dataArray addObject:topModel];
}

//是否是卫星仓
- (BOOL)isSateliteCapsule {
    return self.responseModel.shopInfo.satelliteWarehouse == 1;
}

- (BOOL)isLogin {
    return [FNFreshUser shareInstance].isLogin;
}

- (NSArray<NSString *> *)imageUrlStringArrayForArray:(NSArray<FNFreshStoreServiceBannerItemModel *> *)dataArray {
    NSMutableArray *array = [NSMutableArray array];
    for (FNFreshStoreServiceBannerItemModel *itemModel in dataArray) {
        if (itemModel.img.length > 0){
            [array addObject:itemModel.img];
        }
    }
    return array.count > 0 ? array : nil;
}

- (NSInteger)numberOfSection {
    return self.dataArray.count;
}

- (NSInteger)numberOfItemsInSection:(NSInteger)section {
    return 1;
}

- (FNFreshStoreServiceSectionModel *)sectionModelAtSection:(NSUInteger)section {
    return [self.dataArray safeObjectAtIndex:section];
}


- (CGSize)sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceSectionModel *sectionModel = [self sectionModelAtSection:indexPath.section];

    if (sectionModel.cellHeight > 0) {
        return CGSizeMake(sectionModel.cellWidth, sectionModel.cellHeight);
    }
    CGFloat cellHeight = 0.0;
    CGFloat cellWidth = SCREEN_WIDTH - 24;
    //351\42 SCREEN_WIDTH - 24
    CGFloat headImgHeight = ceilf((SCREEN_WIDTH - 24)*42/351);
    
    switch (sectionModel.sectionType) {
            
            /// 品牌集合/积分商城 头部cell
        case FNFreshMrFreshStoreTypeCellHeaderStyle: {
            FNFreshStoreServiceModuleTitleCell *cell = [FNFreshStoreServiceModuleTitleCell new];
            cell.dataModel = sectionModel.moduleHeaderDataModel;
            CGSize fitSize = [cell.contentView systemLayoutSizeFittingSize:CGSizeMake(SCREEN_WIDTH - 24, 20) withHorizontalFittingPriority:UILayoutPriorityRequired verticalFittingPriority:UILayoutPriorityFittingSizeLevel];
            sectionModel.cellHeight = fitSize.height;
            sectionModel.cellWidth = SCREEN_WIDTH - 24;
            return CGSizeMake(SCREEN_WIDTH - 24, fitSize.height);
        }
            
        case FNFreshMrFreshStoreTypeKingKonog:{
            FNFreshStoreServicesKingKongCell *cell = [FNFreshStoreServicesKingKongCell new];
            cell.frame = CGRectMake(0, 0, SCREEN_WIDTH, 20);
            cell.sectionModel = sectionModel;
            CGSize fitSize = [cell.contentView systemLayoutSizeFittingSize:CGSizeMake(SCREEN_WIDTH, 20) withHorizontalFittingPriority:UILayoutPriorityRequired verticalFittingPriority:UILayoutPriorityFittingSizeLevel];
            sectionModel.cellHeight = fitSize.height;
            sectionModel.cellWidth = SCREEN_WIDTH;
            return CGSizeMake(SCREEN_WIDTH, fitSize.height);
        }
            // 轮播
        case FNFreshMrFreshStoreTypeCarousel:{
            cellHeight = 89 * Ratio;
            break;
        }
        case FNFreshMrFreshStoreTypeShoppingStreet:
            if (sectionModel.hasShoppingStreetIntegralData) {
                cellHeight = floor(138*Ratio) + 50;
            } else {
                cellHeight = floor(138*Ratio) + 61;
            }
            break;
        case FNFreshMrFreshStoreTypeHotSelling://排行榜
            sectionModel.contentViewHeight = floor(238*2*Ratio) + 6;
            cellHeight = headImgHeight + sectionModel.contentViewHeight + 8;
            break;
        case FNFreshMrFreshStoreTypeSingleBanner://单个banner
            cellHeight = floor(Ratio * 89);
            break;
        case FNFreshMrFreshStoreTypeTopView: // 头部视图
//            cellWidth = SCREEN_WIDTH;
//            cellHeight = floor(Ratio * 175) + 113;
            cellWidth = SCREEN_WIDTH;
            cellHeight = 225;
            break;
        case FNFreshMrFreshStoreTypeSaveMoney: {// 今日省钱
            CGFloat tempHeight = floor(128*Ratio);
            if (self.hasLimitedData) {
                tempHeight += floor(117*Ratio)+32+6;
            } else if (self.hasCouponCenterData) {
                tempHeight += floor(107*Ratio)+6;
            }
            sectionModel.contentViewHeight = tempHeight;
            cellHeight = headImgHeight + sectionModel.contentViewHeight + 8;
        }
            break;
        case FNFreshMrFreshStoreTypeApplyAndPoster: //活动报名+促销海报
            cellHeight = floor(Ratio * 56);
            break;
        case FNFreshMrFreshStoreTypeShoppingStreetNew: { //新版商店街
            sectionModel.contentViewHeight = floor(92*Ratio);
            if (sectionModel.hasShoppingStreetIntegralData) {
                cellHeight = 45 + 2 * sectionModel.contentViewHeight + 8;
            } else {
                cellHeight = headImgHeight + 2 * sectionModel.contentViewHeight + 8;
            }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreetIntegral: //商店街积分模块
            if (sectionModel.storeServiceModel.picList2.count > 4) {
                sectionModel.contentViewHeight = floor(183*Ratio);
            } else {
                //165 201
                //(SCREEN_WIDTH - 46)/2
                sectionModel.contentViewHeight = 201.0/165 *(SCREEN_WIDTH - 46)/2;
            }
            CGFloat signHeight = sectionModel.hasShoppingStreetData ? 28 + 15 : 0 + 8;
            cellHeight = headImgHeight + sectionModel.contentViewHeight + signHeight;
            break;
        case FNFreshMrFreshStoreTypeCarouselAndAds: {//轮播+广告图
            CGFloat signWidth = (SCREEN_WIDTH - 24 - 8)/2.0;
            cellHeight = floor(248/171.5*signWidth);
        }
            break;
        case FNFreshMrFreshStoreTypeImagesCollection: { // 品牌集合
            headImgHeight = 30;
            cellWidth = SCREEN_WIDTH;
            sectionModel.contentViewHeight = 70;
            
            CGFloat signWidth = (SCREEN_WIDTH - 24 - 8)/2.0;
            if ([sectionModel.storeServiceModel.styleType isEqual:@"2"] ||
                [sectionModel.storeServiceModel.styleType isEqual:@"3"]) {
                sectionModel.contentViewHeight = floor(148/171.5*signWidth);
            } else if ([sectionModel.storeServiceModel.styleType isEqual:@"4"]) {
                sectionModel.contentViewHeight = floor(70/171.5*signWidth);
            }
            
            if ([sectionModel.storeServiceModel.styleType isEqual:@"4"]) {
                cellHeight = headImgHeight+sectionModel.contentViewHeight * 2 + 8;
            } else {
                cellHeight = headImgHeight+sectionModel.contentViewHeight;
            }
        }
            break;
        case FNFreshMrFreshStoreTypeGoodsModule: { //商品陈列
            headImgHeight = 30;
            if (sectionModel.storeServiceModel.tabList.count > 1) {
                sectionModel.contentViewHeight = 45 + 200/375.0 * SCREEN_WIDTH;
            } else {
                if (sectionModel.storeServiceModel.tabList.firstObject.goodsList.count > 3) {
                    sectionModel.contentViewHeight = 194;
                } else {
                    sectionModel.contentViewHeight = 194/114.0*(SCREEN_WIDTH - 24 - 6*2)/3.0;
                }
            }
            cellHeight = headImgHeight + sectionModel.contentViewHeight;
            cellWidth = SCREEN_WIDTH;
            break;
        }
        case FNFreshMrFreshStoreTypeCouponCenter: { //领券中心
            headImgHeight = 40;
            // 分版优惠券/分版图片 115 不分版优惠券/不分版图片 78
            // 1：分版展示，2：不分版展示
            if ([sectionModel.storeServiceModel.styleType isEqual:@"1"]) {
                sectionModel.contentViewHeight = 115;
            } else {
                sectionModel.contentViewHeight = 78;
            }
            cellHeight = headImgHeight + sectionModel.contentViewHeight + 12;
        }
            break;
        default:
            cellHeight = 0.1;
        }
    }
    sectionModel.cellWidth = cellWidth;
    sectionModel.cellHeight = cellHeight;
    return CGSizeMake(cellWidth, cellHeight);
}

#pragma mark request
- (void)requestStoreHomeIndexWithCallBack:(void(^)(BOOL isSuccess, NSError *error))callBack {
    WS(weakSelf)
    FNFreshMrFreshStoreHomeParameterModel *parameter = [[FNFreshMrFreshStoreHomeParameterModel alloc] init];
    
    parameter.storeCode = [FNFreshUser shareInstance].errorPracticeShopId?:[FNFreshUser shareInstance].lastShopId;
    parameter.latitude = [FNFreshUser shareInstance].latitude;
    parameter.longitude = [FNFreshUser shareInstance].longitude;
    [FNFreshMrFreshService requestStoreHomeIndexWithParameter:parameter responseClass:[FNFreshStoreServiceResponseModel class] success:^(FNFreshStoreServiceResponseModel *responseObject, BOOL isCache) {
        weakSelf.responseModel = responseObject;
        [weakSelf setDataSource];
        callBack(YES, nil);
    } failure:^(id responseObject, NSError *error) {
        callBack(NO, error);
    }];
}

#pragma mark -<event - 公用部分 header>
- (void)actionStoreServicePublicHeaderForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSLog(@"公用Header-更多点击");
    if (!self.operationReturnAction && !self.operationAction) return;
    NSIndexPath *indexPath = self.operationReturnAction(FNFreshOperationEnumStoreServiceGetIndexPath,object);
    FNFreshStoreServiceSectionModel *sectionModel = [self sectionModelAtSection:indexPath.section];
    
    switch (sectionModel.storeServiceModel.type) {
        default:
            break;
    }
    
}

#pragma mark -<event - 头部区>
- (void)actionStoreServiceKingKongUnfoldViewClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSLog(@"金刚区-折叠点击");
    if (!self.operationReturnAction && !self.operationAction) return;
    NSIndexPath *indexPath = self.operationReturnAction(FNFreshOperationEnumStoreServiceGetIndexPath,object);
    
    FNFreshStoreServiceSectionModel *sectionModel = [self sectionModelAtSection:indexPath.section];
    if (sectionModel.unFlodStyle == FNFreshStoreServiceKingKongCellUnFoldType_unFlod) {
        sectionModel.unFlodStyle = FNFreshStoreServiceKingKongCellUnFoldType_allShow;
    } else {
        sectionModel.unFlodStyle = FNFreshStoreServiceKingKongCellUnFoldType_unFlod;
    }
    sectionModel.cellHeight = 0;
    sectionModel.cellWidth = 0;
    //刷新某一个section
    self.operationAction(FNFreshOperationEnumStoreServiceRefreshSection, @(indexPath.section));
    
}

#pragma mark gette & setter
- (NSMutableArray<FNFreshStoreServiceSectionModel *> *)dataArray {
    if (!_dataArray) {
        _dataArray = [NSMutableArray array];
    }
    return _dataArray;
}

- (NSString *)phoneNumber{
    return self.responseModel.shopInfo.shopPhone;
}

- (NSString *)ruleUrl{
    return self.responseModel.ruleUrl;
}

@end

@implementation FNFreshStoreSectionContentModel

@end
