//
//  FNFreshMemberServiceViewModel.h
//  FNFresh
//
//  Created by wang<PERSON> on 2019/9/20.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "FNFreshStoreServiceModel.h"
#import "FNFreshStoreServiceResponseModel.h"

#import "FNFreshStoreServiceSectionModel.h"
#import "FNFreshBaseViewModel.h"

/// 获取indexPath
extern FNFreshOperationEnum const FNFreshOperationEnumStoreServiceGetIndexPath;
/// 打开url
extern FNFreshOperationEnum const FNFreshOperationEnumStoreServiceOpenUrl;
/// 刷新Section
extern FNFreshOperationEnum const FNFreshOperationEnumStoreServiceRefreshSection;

@class FNFreshStoreSectionContentModel, FNFreshStoreServiceSectionModel;

@interface FNFreshMemberServiceViewModel : FNFreshBaseViewModel

@property (strong, nonatomic, readonly) FNFreshStoreServiceResponseModel *responseModel;

@property (assign, nonatomic) BOOL hasShoppingStreetIntegralData;

@property (nonatomic,strong,readonly)NSString *phoneNumber;

@property (nonatomic,strong,readonly)NSString *ruleUrl;

@property (strong, nonatomic) NSMutableArray<FNFreshStoreServiceSectionModel *> *dataArray;

//是否是卫星仓
- (BOOL)isSateliteCapsule;

- (NSInteger)numberOfSection;
- (NSInteger)numberOfItemsInSection:(NSInteger)section;
- (CGSize)sizeForItemAtIndexPath:(NSIndexPath *)indexPath;
- (FNFreshStoreServiceSectionModel *)sectionModelAtSection:(NSUInteger)section;

/// 请求门店首页数据
/// @param callBack 回调block
- (void)requestStoreHomeIndexWithCallBack:(void(^)(BOOL isSuccess, NSError *error))callBack;


#pragma mark -<event - 公用部分 header>
- (void)actionStoreServicePublicHeaderForObject:(id)object userInfo:(NSDictionary *)userInfo;

#pragma mark -<event - 头部区>
- (void)actionStoreServiceKingKongUnfoldViewClickForObject:(id)object userInfo:(NSDictionary *)userInfo;

@end

@interface FNFreshStoreSectionContentModel : NSObject

@property (assign, nonatomic) FNFreshStoreHomeSectionType type;
//新增
@property (strong, nonatomic) FNFreshStoreServiceModel *storeServiceModel;
@property (strong, nonatomic) FNFreshStoreServiceResponseModel *responseModel;

@end

