//
//  FNFreshStoreServiceTrackDataTool.m
//  FNFresh
//
//  Created by xn on 2022/1/12.
//  Copyright © 2022 FeiNiu. All rights reserved.
//

#import "FNFreshStoreServiceTrackDataTool.h"

@implementation FNFreshStoreServiceTrackDataTool

#pragma mark 浏览门店频道页
- (void)agentStoreServiceVCDidAppear {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164006",
        @"page_id" :@"211",
        @"track_type":@"1",
    }];
}

#pragma mark 门店频道页 点击切店按钮
- (void)agentClickChangeStore {
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"164007",
        @"page_id":@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 点击电话按钮
- (void)agentClickPhone {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164008",
        @"page_id" :@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 点击门店服务按钮组
- (void)agentClickMemberServiceWithIndex:(NSInteger)index {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164009",
        @"page_id":@"211",
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",index+1],
    }];
}

#pragma mark 门店频道页 点击会员卡按钮
- (void)agentClickMemberCard {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164011",
        @"page_id":@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 点击优惠卡券按钮
- (void)agentClickMyCoupons {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164012",
        @"page_id":@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 点击轮播banner
- (void)agentClickStoreBannerWithIndex:(NSInteger)index {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164013",
        @"page_id" :@"211",
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",index + 1],
    }];
}

#pragma mark 门店频道页 今日省钱模块曝光
- (void)agentSaveMoneyModuleDidAppear {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164020",
        @"page_id" :@"211",
        @"track_type":@"6",
    }];
}

#pragma mark 门店频道页 点击今日省钱领券中心
- (void)agentClickCouponModule {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164021",
        @"page_id" :@"221",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 点击今日省钱当日限定
- (void)agentClickLimitSales {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164022",
        @"page_id" :@"221",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 点击今日省钱左下banner 164023 & 点击今日省钱右下banner 164024
- (void)agentClickSaveMoneyPicResourcesIsRight:(BOOL)isRight {
    NSString *page_col = isRight ? @"164024": @"164023";
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":page_col,
        @"page_id" :@"221",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 活动报名&促销海报模块曝光
- (void)agentApplyAndPosterModuleDidAppear {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164027",
        @"page_id" :@"211",
        @"track_type":@"6",
    }];
}

#pragma mark 门店频道页 点击活动报名模块 164028 & 点击促销海报模块 164029
- (void)agentClickApplyOrPosterItem:(BOOL)isApply {
    NSString *page_col = isApply ? @"164028": @"164029";
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":page_col,
        @"page_id" :@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 商店街模块曝光
- (void)agentShoppingStreetModuleDidAppear:(BOOL)isNew {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164030",
        @"page_id" :@"211",
        @"track_type":@"6",
        @"col_position":isNew ? @"2": @"1"
    }];
}

#pragma mark 门店频道页 点击商店街模块
- (void)agentClickShoppingStreet {
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"164031",
        @"page_id":@"211",
        @"track_type":@"2",
    }];
}
#pragma mark 门店频道页 点点击新版商店街模块
- (void)agentClickShoppingStreetNew:(NSInteger)index {
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"171030",
        @"page_id":@"211",
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",index + 1],
    }];
}

#pragma mark 门店频道页 点击积分模块图片
- (void)agentClickShoppingStreetIntegralItem {
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"176019",
        @"page_id":@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 门店频道页 排行榜大模块曝光
- (void)agentHotSellingModuleDidAppear {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164032",
        @"page_id" :@"211",
        @"track_type":@"6",
    }];
}

#pragma mark 门店频道页 点击排行榜大模块进内页
- (void)agentClickHotSellingItemWithIndex:(NSInteger)index {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164033",
        @"page_id" :@"211",
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",index + 1],
    }];
}

#pragma mark 门店频道页 横幅banner模块曝光
- (void)agentSingleBannerDidAppear {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164034",
        @"page_id" :@"211",
        @"track_type":@"6",
    }];
}

#pragma mark 门店频道页 商店街联合模块积分兑券模块曝光
- (void)agentShoppingStreetIntegralDidAppear:(NSInteger)picCount {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"176017",
        @"page_id" :@"211",
        @"track_type":@"6",
        @"col_position":[NSString stringWithFormat:@"%ld", (long)picCount],
    }];
}

- (void)agentShoppingStreetIntegralDidAppearforColPosition:(NSString*)col_position {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"176017",
        @"page_id" :@"211",
        @"track_type":@"6",
        @"col_position":col_position,
    }];
}

#pragma mark 门店频道页 点击横幅banner
- (void)agentClickSingleBanner {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"164035",
        @"page_id" :@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 点击购物卡按钮
- (void)agentClickShoppingCardButton {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"193058",
        @"page_id" :@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 点击成长值按钮
- (void)agentClickGrowUpButton {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"193059",
        @"page_id" :@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 点击规则按钮
- (void)agentClickRuleInfo {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"193060",
        @"page_id" :@"211",
        @"track_type":@"2",
    }];
}

#pragma mark 金刚区曝光
- (void)agentKingKongDidAppear {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"193061",
        @"page_id" :@"211",
        @"track_type":@"6",
    }];
}

#pragma mark 点击金刚区模块
- (void)agentClickKingKongItem:(NSInteger)index {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"193062",
        @"page_id" :@"211",
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",(long)index],
    }];
}

#pragma mark 图片集合模块图片资源位曝光
- (void)agentImagesCollectionDidAppear:(NSString *)styleType {
    //（1：一排横滑，2 ：1+1 3：1+2 4：2+2 ）
    NSString *pageCol = @"200027";
    if ([styleType isEqual:@"2"]) {
        pageCol = @"200029";
    } else if ([styleType isEqual:@"3"]) {
        pageCol = @"200031";
    } else if ([styleType isEqual:@"4"]) {
        pageCol = @"200033";
   }
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":pageCol,
        @"page_id" :@"211",
        @"track_type":@"6",
    }];
}

#pragma mark 点击图片集合模块图片资源位
- (void)agentClickImagesCollection:(NSString *)styleType index:(NSInteger)index {
    //（1：一排横滑，2 ：1+1 3：1+2 4：2+2 ）
    NSString *pageCol = @"200028";
    if ([styleType isEqual:@"2"]) {
        pageCol = @"200030";
    } else if ([styleType isEqual:@"3"]) {
        pageCol = @"200032";
    } else if ([styleType isEqual:@"4"]) {
        pageCol = @"200034";
   }
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":pageCol,
        @"page_id" :@"211",
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",(long)index],
    }];
}

#pragma mark 点击商品陈列模块线上商品进商详 / 点击商品陈列模块线下商品
- (void)agentClickGoodsModuleEnterDeatil:(NSString *_Nullable)goodsId isOffline:(BOOL)isOffline {
    if (isOffline) {
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"198012",
            @"page_id" :@"211",
            @"track_type":@"2",
        }];
    } else {
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"178001",
            @"page_id" :@"211",
            @"track_type":@"2",
            @"col_pos_content": goodsId ?: @""
        }];
    }
}

#pragma mark 点击商品陈列模块线上商品加购
- (void)agentClickGoodsModuleAddToShopCart:(NSString *_Nullable)goodsId {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"100028",
        @"page_id" :@"211",
        @"track_type":@"2",
        @"col_pos_content": goodsId ?: @""
    }];
}

#pragma mark 新轮播banner曝光
+ (void)agentCarouserDidAppear:(NSInteger)index {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"193067",
        @"page_id" :@"211",
        @"track_type":@"6",
        @"col_position":[NSString stringWithFormat:@"%ld",(long)index],
    }];
}

#pragma mark 点击新轮播banner
+ (void)agentClickCarouserItem:(NSInteger)index {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"193068",
        @"page_id" :@"211",
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",(long)index],
    }];
}

#pragma mark 点击点击领券中心模块
- (void)agentClickCouponCenter:(NSString *)type {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"215011",
        @"page_id" :@"211",
        @"track_type":@"2",
        @"col_position":type ?: @"",
    }];
}

//模块曝光埋点
- (void)handleExposureTrackData:(FNFreshStoreServiceSectionModel *)sectionModel {
    switch (sectionModel.sectionType) {
        case FNFreshMrFreshStoreTypeKingKonog:{
            [self agentKingKongDidAppear];
            break;
        }
        case FNFreshMrFreshStoreTypeImagesCollection:{
            [self agentImagesCollectionDidAppear: sectionModel.storeServiceModel.styleType];
            break;
        }
            break;
        case FNFreshMrFreshStoreTypeSaveMoney: {
            [self agentSaveMoneyModuleDidAppear];
        }
            break;
        case FNFreshMrFreshStoreTypeApplyAndPoster: {
            [self agentApplyAndPosterModuleDidAppear];
        }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreet: {
            [self agentShoppingStreetModuleDidAppear:false];
        }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreetNew: {
            [self agentShoppingStreetModuleDidAppear:true];
        }
            break;
        case FNFreshMrFreshStoreTypeHotSelling: {
            [self agentHotSellingModuleDidAppear];
        }
            break;
        case FNFreshMrFreshStoreTypeSingleBanner: {
            [self agentSingleBannerDidAppear];
        }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreetIntegral: {
//            [self agentShoppingStreetIntegralDidAppear:sectionModel.storeServiceModel.picList2.count];
            if(sectionModel.storeServiceModel.picList2.count > 0){
                [self agentShoppingStreetIntegralDidAppearforColPosition:sectionModel.storeServiceModel.picList2.firstObject.tag.description];
            }
        }
        case FNFreshMrFreshStoreTypeCarouselAndAds: {
            [FNFreshAgent eventWithTrackDataPrameters:@{
                @"page_col":@"198007",
                @"page_id" :@"211",
                @"track_type":@"6",
            }];
        }
            break;
        case FNFreshMrFreshStoreTypeGoodsModule: {
            [FNFreshAgent eventWithTrackDataPrameters:@{
                @"page_col":@"198010",
                @"page_id" :@"211",
                @"track_type":@"6",
            }];
        }
            break;
        case FNFreshMrFreshStoreTypeCouponCenter: {
            [FNFreshAgent eventWithTrackDataPrameters:@{
                @"page_col":@"215010",
                @"page_id" :@"211",
                @"track_type":@"6",
                @"col_position":sectionModel.storeServiceModel.styleType ?: @"",
            }];
        }
            break;
        default:
            break;
    }
}

//点击商品陈列模块头部更多按钮 和 点击商品陈列模块滑动到尾部更多按钮
- (void)handleClickGoodsModuleMore:(NSString *)style {
    if ([style isEqual: @"goodsTailMore"]) {
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"198013",
            @"page_id" :@"211",
            @"track_type":@"2",
        }];
    } else if ([style isEqual: @"tabViewMore"] || [style isEqual: @"headerViewMore"]) {
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"198011",
            @"page_id" :@"211",
            @"track_type":@"2",
        }];
    }
}

//点击埋点
- (void)handleClickTrackData:(FNFreshStoreHomeSectionType)type index:(NSInteger)index {
    switch (type) {
        case FNFreshMrFreshStoreTypeKingKonog: {
            [self agentClickKingKongItem:index];
            break;
        }
        case FNFreshMrFreshStoreTypeCarouselAndAds: {
            if (index) {
                [FNFreshAgent eventWithTrackDataPrameters:@{
                    @"page_col":@"198008",
                    @"page_id" :@"211",
                    @"track_type":@"2",
                    @"col_position":[NSString stringWithFormat:@"%ld",(long)index],
                }];
            } else {
                [FNFreshAgent eventWithTrackDataPrameters:@{
                    @"page_col":@"198009",
                    @"page_id" :@"211",
                    @"track_type":@"2",
                }];
            }
        }
        default:
            break;
    }
}

/** 埋点 */
+ (void)eventTrackFor:(FNFreshStoreServiceTrackType)type info:(NSDictionary *_Nullable)info{
    FNFreshBuryStartTempVariable
    page_id = @"211";
    col_position = info[FNFreshBuryParamsColPositionKeyValue];
    switch (type) {
        case FNFreshStoreServiceTrack_clickPointsMall:
        {
            track_type = @"2";
            page_col = @"193066";
            break;
        }
        default:
            break;
    }
    
   
  
    FNFreshBuryEndSendTempVariable
    
}

@end

