//
//  FNFreshStoreMembershipServiceViewController.m
//  FNFresh
//
//  Created by wang<PERSON> on 2019/9/16.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

/**
 * FNFreshStoreMembershipServiceViewController
 * 会员服务门店首页视图控制器实现
 * 
 * 主要功能模块:
 * 1. UI展示与布局
 *    - 导航栏和状态栏管理
 *    - CollectionView列表展示
 *    - 空状态和错误状态处理
 * 2. 数据管理
 *    - 网络请求和数据刷新
 *    - 本地缓存处理
 *    - 状态维护
 * 3. 用户交互
 *    - 手势和点击事件处理
 *    - 页面跳转和导航
 *    - 动画效果
 * 4. 业务功能
 *    - 会员服务
 *    - 商品展示
 *    - 购物车操作
 *    - 活动促销
 */

#import "FNFreshStoreMembershipServiceViewController.h"
#import "FNFreshMemberCardContainerVC.h"
#import "FNFreshTabBarController.h"
#import "FNFreshNewBindCardResponseModel.h"
#import "FNAlertView+UIAlertController.h"
#import "FNFreshOfflineBaseViewController.h"
#import "UIScrollView+MJRefresh.h"
#import "FNFreshNormalRefreshHeader.h"
#import "FNFreshShoppingStreetCollectionCell.h"
#import <FNCacheManager.h>
#import "FNFreshUrlRouter.h"
#import "FNFreshMrFreshGradientView.h"
#import "FNFreshMemberServiceViewModel.h"
#import "FNFreshMrFreshConstantCacheHandler.h"
#import "UIViewController+FNMrFreshAnimatedTransitioning.h"
#import "UIViewController+FNNavigationBarHidden.h"
#import "UIViewController+FNFreshEmptyDataSet.h"
#import "FNFreshMrFreshFooterCollectionReusableView.h"
#import "FNMediator+FreshAddressModule.h"
#import "FNMediator+FNFreshUserCenterModule.h"
#import "FNFreshMrFreshService.h"
#import "FNFreshNewStoreGiftPopWindowViewController.h"
#import "FNFreshCouponViewController.h"
#import "FNFreshNewStoreHomePopGiftWindowResponseModel.h"
#import "UIFont+FNFont.h"

//新增
#import "FNFreshStoreServiceBaseCell.h"
#import "FNFreshStoreServiceBgViewCell.h"
#import "FNFreshStoreServiceSaveMoneyCell.h"
#import "FNFreshStoreServiceApplyAndPosterCell.h"
#import "FNFreshStoreServiceTrackDataTool.h"
#import "FNFreshStoreServiceSectionModel.h"
#import "FNFreshRankSecondCategoryViewController.h"
#import "FNFreshBaseResponseModel.h"
#import "FNFreshStoreServiceBannerCell.h"
#import "FNFreshShoppingStreetIntegralCell.h"
#import "FNFreshImproveInfoManager.h"
#import "UIViewController+FNTopVC.h"
#import "FNFreshStoreServicesHeaderAddressCell.h"
#import "FNFreshStoreServiceModuleTitleCell.h"
#import "FNFreshStoreServicesKingKongCell.h"
#import "FNFreshStoreServiceCarouseCell.h"
#import "FNFreshGradientBaseView.h"
#import "UIView+Corners.h"
#import "FNMediator+FNCreditChannelModule.h"
#import "UIDevice+FNScreenSize.h"
#import "FNFreshStoreServiceBannerAndAdsCell.h"
#import "FNFreshStoreServiceImagesCell.h"
#import "FNFreshStoreServiceGoodsModuleCell.h"
#import "FNFreshMerchandiseAddToCartHandler.h"
#import "FNMediator+FNFreshMerchandiseDetail.h"
#import "FNFreshShopCartAnimateManager.h"
#import "UIView+AddShopcartAnimation.h"
#import "FNFreshStoreServiceGoodsItemCell.h"
#import "FNFreshStoreServiceCouponCenterCell.h"

/**
 * 通知常量定义
 */
/// 刷新首页数据通知 - 用于触发首页数据的刷新
extern NSString * const kFNFreshRefreshHomeDataNotification;
/// 滚动到顶部通知 - 用于控制页面滚动到顶部
extern NSString * const kFNFreshScrollToTopNotification;

/// 刷新线下首页数据通知 - 用于触发线下门店数据的刷新
NSNotificationName const kFNFreshRefreshOfflineHomeDataNotification = @"FNRefreshOfflineHomeDataNotification";
/// 门店首页弹窗通知 - 用于控制首页弹窗的显示
NSNotificationName const kFreshStoreHomePopWindow = @"fnFreshStoreHomePopWindow";
/// 底部CollectionView重用标识符 - 用于注册和复用底部视图
static NSString *const FNFreshMrFreshFooterCollectionReusableViewIdentifier = @"FNFreshMrFreshFooterCollectionReusableView";

@interface FNFreshStoreMembershipServiceViewController ()<UICollectionViewDataSource,UICollectionViewDelegateFlowLayout, FNMrFreshStoreServiceCellDelegate,FNHumanInteractionTransition, FNFreshMerchandiseAddToCartHandlerDelegate>

#pragma mark - UI Properties
/**
 * UI相关属性
 * 包含导航栏、列表视图、空状态视图等界面元素
 */

/// 导航栏视图 - 自定义导航栏容器
@property (weak, nonatomic) IBOutlet UIView *navigationBar;
/// 导航栏高度约束 - 控制导航栏高度
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *navigationBarHeightConstraint;
/// 导航栏标题标签 - 显示页面标题
@property (weak, nonatomic) IBOutlet UILabel *naviTitleLab;
/// 导航栏切换图标 - 用于切换功能的指示器
@property (weak, nonatomic) IBOutlet UIImageView *naviSwitchImg;
/// 导航栏底部分割线 - 视觉分隔线
@property (weak, nonatomic) IBOutlet UILabel *navBottomLine;
/// 主要内容展示的CollectionView - 用于展示页面主要内容
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
/// 空状态展示的ScrollView - 用于展示空数据状态
@property (strong, nonatomic) UIScrollView *emptyScrollView;
/// 空状态导航栏背景视图 - 空状态下的导航栏背景
@property (weak, nonatomic) IBOutlet FNFreshMrFreshGradientView *emptyNavBgView;
/// 空状态导航栏标题标签 - 空状态下的标题显示
@property (weak, nonatomic) IBOutlet UILabel *emptyNavTitleLab;
/// 背景图片视图 - 页面背景图片
@property (weak, nonatomic) IBOutlet UIImageView *bgImageView;
/// 背景图片顶部约束 - 控制背景图片位置
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bgImgViewTopConstraint;
/// 背景图片高度约束 - 控制背景图片大小
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bgImgViewHeightConstraint;
/// CollectionView顶部约束 - 控制列表位置
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewTopConstraint;

#pragma mark - View Properties
/**
 * 视图相关属性
 * 包含自定义视图和渐变效果
 */

/// 背景渐变视图 - 整体背景渐变效果
@property (nonatomic,strong)FNFreshGradientBaseView *bgGradientView;
/// 背景中心渐变视图 - 中心区域渐变效果
@property (nonatomic,strong)FNFreshGradientBaseView *bgCenterGradientView;

#pragma mark - Data Properties
/**
 * 数据相关属性
 * 包含数据模型、状态标记和工具类
 */

/// 视图模型 - 处理数据和业务逻辑
@property (strong, nonatomic) FNFreshMemberServiceViewModel *viewModel;
/// 埋点数据工具 - 用于统计和分析
@property (strong, nonatomic) FNFreshStoreServiceTrackDataTool *trackDataTool;
/// 小火箭是否显示 - 控制返回顶部按钮显示状态
@property (assign, nonatomic) BOOL isRocketShow;
/// 当前页面是否可见 - 标记页面显示状态
@property (assign, nonatomic, getter=isCurrentPage) BOOL currentPage;
/// 网络错误页面是否点击重新加载 - 标记错误重试状态
@property (assign, nonatomic) BOOL isErrorViewRefresh;
/// 是否下拉刷新 - 标记刷新状态
@property (assign, nonatomic) BOOL isDropDownRefresh;

/// 轮播图Cell引用 - 用于管理轮播定时器
@property (nonatomic,weak)FNFreshStoreServiceCarouseCell *carouseCell;

#pragma mark - Shopping Cart Properties
/**
 * 购物车相关属性
 * 包含加购处理和动画视图
 */

/// 加购处理器 - 处理商品加入购物车逻辑
@property (nonatomic, strong)FNFreshMerchandiseAddToCartHandler *addToCartHandler;
/// 加购目标视图 - 购物车图标视图
@property (nonatomic, strong)UIView *addShopCartTargetView;
/// 加购动画图片视图 - 飞入购物车动画
@property (nonatomic, strong) UIImageView *animateImgView;
/// 商品临时Cell - 用于加购动画起始位置
@property (nonatomic,weak) FNFreshStoreServiceGoodsItemCell *goodsTempCell;

@end

@implementation FNFreshStoreMembershipServiceViewController

#pragma mark - Life Cycle Methods
/**
 * 生命周期方法
 * 管理视图控制器的创建、显示、消失等生命周期
 */

/**
 * 初始化方法
 * 从storyboard加载时调用
 * @param coder 解码器对象
 * @return 实例对象
 */
- (instancetype)initWithCoder:(NSCoder *)coder {
    if (self = [super initWithCoder:coder]) {
    }
    return self;
}

/**
 * 视图布局完成
 * 在视图布局完成后调用
 * 主要处理:
 * - 设置背景渐变视图的圆角
 * - 更新UI布局
 */
- (void)viewDidLayoutSubviews{
    [super viewDidLayoutSubviews];
    [self.bgGradientView cutCornerRadius:16 bounds:self.bgGradientView.bounds rectCorner:UIRectCornerTopLeft | UIRectCornerTopRight];
}

/**
 * 视图加载完成
 * 视图控制器加载视图时调用
 * 主要处理:
 * - 初始化UI界面
 * - 绑定事件处理
 * - 添加通知观察
 * - 请求首页数据
 */
- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setBindEvent];
    [self addObserver];
    [self requestHomeData];
}

/**
 * 视图即将显示
 * 视图控制器的视图即将显示在屏幕上时调用
 * 主要处理:
 * - 更新页面显示状态
 * - 处理埋点统计
 * - 检查信息完善状态
 * - 启动轮播定时器
 * @param animated 是否使用动画
 */
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.currentPage = YES;
    //门店首页 访问页面
    [self.trackDataTool agentStoreServiceVCDidAppear];
    WS(weakSelf);
    [FNFreshImproveInfoManager.shared alertImproveInfoIfNeeded:self completion:^(BOOL isSuccess, NSString * _Nonnull toast) {
        if (toast.length > 0) {
            [UIViewController.topViewController  startProgressText:toast];
        }
        [weakSelf requestNewGiftPopupWindowInfo];
    }];
    
    if (self.carouseCell){
        [self.carouseCell.bannerView timerInvalid];
        [self.carouseCell.bannerView timerFire];
    }
}

- (void)requestNewGiftPopupWindowInfo {
    [[FNFreshOfflineBaseViewController shareInstance] requestPopupWindowInfo];
}

/**
 * 视图已经显示
 * 视图控制器的视图已经显示在屏幕上时调用
 * 主要处理:
 * - 信息完善相关逻辑
 * @param animated 是否使用动画
 */
- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [[FNFreshImproveInfoManager shared] viewDidAppearWith:self];
}

/**
 * 视图即将消失
 * 视图控制器的视图即将从屏幕上消失时调用
 * 主要处理:
 * - 更新页面显示状态
 * - 停止轮播定时器
 * @param animated 是否使用动画
 */
- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.currentPage = NO;
    if (self.carouseCell){
        [self.carouseCell.bannerView timerInvalid];
    }
}

#pragma mark - Private Methods

/**
 * 添加通知观察者
 * 注册监听各种通知事件
 * 主要包括:
 * - 地址变更通知
 * - 登录状态通知
 * - 数据刷新通知
 * - 位置更新通知
 * - 滚动到顶部通知
 */
- (void)addObserver {
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFNFreshAddressChangedNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFNFreshLoginStateDidChangeNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFNFreshRefreshHomeDataNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFreshLocationNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFNFreshRefreshOfflineHomeDataNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(scrollToTop) name:kFNFreshScrollToTopNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleWillEnterForeground) name:UIApplicationWillEnterForegroundNotification object:nil];
}

- (void)handleWillEnterForeground {
    [self requestHomeData];
    [self requestNewGiftPopupWindowInfo];
}

#pragma mark - Event Binding

/**
 * 设置事件绑定
 * 处理视图模型的回调事件
 * 主要功能:
 * - 获取CollectionView的IndexPath
 * - 处理操作事件回调
 */
- (void)setBindEvent {
    __weak FNFreshStoreMembershipServiceViewController *weakSelf = self;
    self.viewModel.operationReturnAction = ^id _Nullable(FNFreshOperationEnum  _Nonnull operationType, id  _Nullable data) {
        if ([operationType isEqualToString:FNFreshOperationEnumStoreServiceGetIndexPath]){
            return [weakSelf.collectionView indexPathForCell:data];
        }
        return nil;
    };
    
    self.viewModel.operationAction = ^(FNFreshOperationEnum  _Nonnull operationType, id data) {
        [weakSelf actionOperationForEventName:operationType data:data];
    };
}

#pragma mark - UI Setup

/**
 * 初始化UI界面
 * 设置界面的基本布局和样式
 * 主要功能:
 * - 设置背景渐变视图
 * - 配置导航栏样式
 * - 设置CollectionView布局
 * - 注册Cell类型
 * - 配置下拉刷新
 * - 设置默认背景图
 */
- (void)setupUI {
    self.bgImageView.clipsToBounds = YES;

    //添加渐变背景
    [self.view insertSubview:self.bgGradientView belowSubview:self.collectionView];
    [self.bgGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(self.view);
        make.top.mas_equalTo(self.view.mas_top).mas_offset(225 + [UIDevice fnStatusBarHeight] - 2);
        make.height.mas_equalTo(116);
    }];
    
    [self.view insertSubview:self.bgCenterGradientView belowSubview:self.bgGradientView];
    [self.bgCenterGradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.mas_equalTo(self.view);
        make.top.mas_equalTo(self.view.mas_top).mas_offset(175 + [UIDevice fnStatusBarHeight]);
        make.height.mas_equalTo(80);
    }];
    
    self.fnPreferNavigationBarHidden = YES;
    self.naviTitleLab.font = [UIFont boldSystemFontOfSize:SCREEN_WIDTH > 375?18.f:17.f];
    [self.navigationBar setHidden:YES];
    self.navigationBarHeightConstraint.constant = [UIApplication sharedApplication].statusBarFrame.size.height + 44;
    self.collectionViewTopConstraint.constant = [UIApplication sharedApplication].statusBarFrame.size.height;
    self.edgesForExtendedLayout = UIRectEdgeTop;
    self.collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 0, 12, 0);
    [self.emptyNavBgView setColors:@[(id)[UIColor hex:@"F64139"].CGColor,
                                     (id)[UIColor hex:@"E50113"].CGColor]];
    [self.emptyNavBgView setHidden:YES];
    if (isFNFreshTarget) {
        self.emptyNavTitleLab.text = @"大润发优鲜";
    } else {
        self.emptyNavTitleLab.text = @"欧尚";
    }
    
    NSArray *cellClassArray = @[
        NSStringFromClass([FNFreshShoppingStreetCollectionCell class]),
        //新增
        NSStringFromClass([FNFreshStoreServiceBgViewCell class]),
        NSStringFromClass([FNFreshStoreServiceSaveMoneyCell class]),
        NSStringFromClass([FNFreshStoreServiceApplyAndPosterCell class]),
        NSStringFromClass([FNFreshStoreServiceBannerCell class]),
        NSStringFromClass([FNFreshShoppingStreetIntegralCell class]),
        NSStringFromClass([FNFreshStoreServiceBannerAndAdsCell class]),
        NSStringFromClass([FNFreshStoreServiceCarouseCell class]),
        NSStringFromClass([FNFreshStoreServiceImagesCell class]),
        NSStringFromClass([FNFreshStoreServiceGoodsModuleCell class]),
        NSStringFromClass([FNFreshStoreServiceCouponCenterCell class]),
    ];
    for (NSString *cellClassString in cellClassArray) {
        [self.collectionView registerNib:[UINib nibWithNibName:cellClassString bundle:[FNFreshBundleHandler fnFreshBundle]] forCellWithReuseIdentifier:cellClassString];
    }
    
    [self.collectionView registerClass:[FNFreshStoreServicesHeaderAddressCell class] forCellWithReuseIdentifier:[FNFreshStoreServicesHeaderAddressCell identifierDescString]];
    [self.collectionView registerClass:[FNFreshStoreServiceModuleTitleCell class] forCellWithReuseIdentifier:[FNFreshStoreServiceModuleTitleCell identifierDescString]];
    [self.collectionView registerClass:[FNFreshStoreServicesKingKongCell class] forCellWithReuseIdentifier:[FNFreshStoreServicesKingKongCell identifierDescString]];
    
    [self.collectionView registerNib:[FNFreshMrFreshFooterCollectionReusableView fnFreshNib]
          forSupplementaryViewOfKind:UICollectionElementKindSectionFooter
                 withReuseIdentifier:FNFreshMrFreshFooterCollectionReusableViewIdentifier];
    
    [self.collectionView registerClass:[UICollectionViewCell class] forCellWithReuseIdentifier:@"StoreServicesDefaultCell"];
    
    [self configureRefreshHeaderView];
    [self showDefaultBgImage];
}

/**
 * 配置下拉刷新
 * 设置CollectionView的下拉刷新功能
 * 主要功能:
 * - 创建刷新头部视图
 * - 设置刷新回调
 * - 处理刷新状态
 */
- (void)configureRefreshHeaderView {
    __weak typeof(self)weakSelf = self;
    if (!self.collectionView.mj_header) {
        FNFreshNormalRefreshHeader *herder = [FNFreshNormalRefreshHeader headerWithRefreshingBlock:^{
            weakSelf.isDropDownRefresh = true;
            [weakSelf requestHomeData];
        }];
        self.collectionView.mj_header = herder;
    }
}

/**
 * 更新背景图片
 * 根据服务器返回的图片URL更新页面背景
 * 主要功能:
 * - 加载网络图片
 * - 处理加载失败情况
 * - 设置图片尺寸
 */
- (void)updateBgImage {
    // 全局背景图片
    NSString *bgImgUrlStr = self.viewModel.responseModel.headBannerUrl;
    if (bgImgUrlStr.length > 0) {
        WS(weakSelf)
        [[SDWebImageManager sharedManager]
         loadImageWithURL:[NSURL URLWithString:bgImgUrlStr]
         options:SDWebImageRetryFailed
         progress:nil
         completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            if (!image) {
                [weakSelf showDefaultBgImage];
                return;
            }
            weakSelf.bgImageView.image = image;
//            NSUInteger height = floor(SCREEN_WIDTH * image.size.height / image.size.width);
//            weakSelf.bgImgViewHeightConstraint.constant = height;
        }];
    } else {
        [self showDefaultBgImage];
    }
}

/**
 * 显示默认背景图片
 * 当无法获取服务器图片时显示默认背景
 * 主要功能:
 * - 设置默认图片
 * - 调整图片尺寸
 */
- (void)showDefaultBgImage {
//    self.bgImgViewHeightConstraint.constant = floor(SCREEN_WIDTH * 600 / 750);
    [self.bgImageView setImage:[UIImage imageNamed:@"bg_store_service"]];
}

/**
 * 获取当前页面状态
 * 判断页面是否处于前台显示状态
 * @return 是否为当前显示页面
 */
- (BOOL)isCurrentPage {
    return _currentPage && ![FNFreshTabBarController shareInstance].navigationController.hasPresentedViewController;
}

#pragma mark - Operation Events

/**
 * 处理操作事件
 * 根据不同的操作类型执行相应的动作
 * 主要功能:
 * - 处理URL跳转
 * - 处理页面刷新
 * @param operationType 操作类型
 * @param data 操作相关数据
 */
- (void)actionOperationForEventName:(FNFreshOperationEnum _Nonnull)operationType data:(id)data {
    if ([operationType isEqualToString:FNFreshOperationEnumStoreServiceOpenUrl]) {
        NSString *url = data;
        [[FNFreshUrlRouter new] jumpControllerWithRemoteURLString:url params:nil completion:nil];
    } else if ([operationType isEqualToString:FNFreshOperationEnumStoreServiceRefreshSection]){
        NSInteger section = [((NSNumber *)data) integerValue];
        [self.collectionView reloadSections:[NSIndexSet indexSetWithIndex:section]];
    }
}

#pragma mark - Network Requests

/**
 * 请求首页数据
 * 获取并展示店铺首页相关信息
 * 主要功能:
 * - 显示加载进度
 * - 请求网络数据
 * - 处理返回结果
 * - 更新UI显示
 * - 处理错误情况
 */
- (void)requestHomeData {
    WS(weakSelf);
    if (!self.isDropDownRefresh) {
        [self startProgress];
    }
    [self.viewModel requestStoreHomeIndexWithCallBack:^(BOOL isSuccess, NSError *error) {
        weakSelf.isDropDownRefresh = false;
        [weakSelf.collectionView.mj_header endRefreshing];
        [weakSelf stopProgress];
        if (weakSelf.emptyScrollView) {
            [weakSelf.emptyScrollView removeFromSuperview];
            weakSelf.emptyScrollView = nil;
            [weakSelf.emptyNavBgView setHidden:YES];
        }
        
        if (isSuccess) {
            if (weakSelf.viewModel.responseModel.shopInfo.storeStatus == 4 ||
                weakSelf.viewModel.responseModel.shopInfo.storeStatus == 5) { // 对应门店闭店 展示容错页
                [weakSelf addCloseStoreErrorView];
                return;
            }
            if (weakSelf.viewModel.responseModel.shopInfo.isNewStore) { //展示新店
                [[FNFreshOfflineBaseViewController shareInstance] chooseNewStore];
                return;
            } else if (weakSelf.viewModel.responseModel.shopInfo.storeType == 3) { //展示mini店
                [[FNFreshOfflineBaseViewController shareInstance] chooseMini];
                return;
            } else {
                [[FNFreshOfflineBaseViewController shareInstance] chooseOffLine];
                [weakSelf updateBgImage];
            }
            //如果是卫星仓
            if ([weakSelf.viewModel isSateliteCapsule]) {
                weakSelf.naviTitleLab.text = @"";
                weakSelf.naviSwitchImg.hidden = true;
            } else {
                weakSelf.naviTitleLab.text = weakSelf.viewModel.responseModel.storeName;
                weakSelf.naviSwitchImg.hidden = false;
            }
            
            [weakSelf.collectionView reloadData];
            weakSelf.collectionView.contentOffset = CGPointZero;
        } else {
            if (weakSelf.viewModel.responseModel) {
                [weakSelf startProgressText:error.localizedDescription];
                return;
            }
            
            [weakSelf.emptyNavBgView setHidden:NO];
            [weakSelf fnFresh_addNetworkErrorEmptyDataSetWithTarget:weakSelf.emptyScrollView errorCode:@(error.code) errorDesc:error.localizedDescription refreshEvent:^{
                weakSelf.isErrorViewRefresh = true;
                [weakSelf requestHomeData];
            }];
        }
        if (weakSelf.isErrorViewRefresh) {
            [weakSelf startProgressText:error.localizedDescription];
        }
    }];
}

/**
 * 添加闭店错误视图
 * 当店铺处于闭店状态时展示的错误页面
 */
- (void)addCloseStoreErrorView {
    [self.emptyNavBgView setHidden:NO];
    NSAttributedString *content = [[NSAttributedString alloc] initWithString:@"您的当前位置暂无到店服务" attributes:@{NSFontAttributeName: [UIFont fn_FontWithFontKey:kFC_C],                                       NSForegroundColorAttributeName:[UIColor fn_colorWithColorKey:kFN999999]}];
    [self fnFresh_addEmptyDataSetWithTarget:self.emptyScrollView
                                      image:[UIImage fnFresh_imageNamed:@"pic_empty_3"]
                             attributeTitle:content
                       attributeButtonTitle:nil
                                buttonImage:nil
                       buttonBackgroudImage:nil
                                buttonEvent:nil];
}

/**
 * 请求绑卡接口
 * 处理会员卡绑定状态
 * 成功后跳转会员卡页面
 */
- (void)requestBindStatus {
    WS(weakSelf)
    [[FNMediator sharedInstance] fnFreshUserCenterService_RequestBindMemberCard:^(FNFreshNewBindCardResponseModel *responseModel, BOOL isCache) {
        [weakSelf showMembershipCardVc];
    } failure:^(FNFreshNewBindCardResponseModel *responseModel, NSError *error) {
        [weakSelf startProgressText:responseModel.errorDesc];
//        [FNAlertView showWithTitle:@"温馨提示" message:responseModel.errorDesc cancelButtonTitle:@"好的" otherButtonTitles:nil tapBlock:nil];
    }];
}

#pragma mark private
- (void)scrollToTop {
    if (!self.isCurrentPage) {
        return;
    }
    [self.collectionView setContentOffset:CGPointZero animated:YES];
}

/**
 * 打开指定URL的页面
 * @param URLString 目标页面的URL
 */
- (void)openPageWithURLString:(NSString *)URLString {
    FNFreshUrlRouter *URLRouter = [[FNFreshUrlRouter alloc] init];
    [URLRouter jumpControllerWithRemoteURLString:URLString completion:nil];
}

/**
 * 显示会员卡页面
 * 处理会员卡展示和埋点
 */
- (void)showMembershipCardVc {
    NSString *attStr = [NSString stringWithFormat:@"%@_%@_%@_%@_%@", @"83",@"2",@"",@"",@""];
    [FNFreshAgent eventWithPage_col:@"116008" attributeStr:attStr];
    FNFreshMemberCardContainerVC *vc = [[FNFreshMemberCardContainerVC alloc] initWithBarCodeVcWithEnterType:0 memCardVCType:0 cardId: @""];
    [FNFreshTabBarController pushViewController:vc animated:YES];
}

/**
 * 切换到门店列表
 * 导航栏切店按钮点击事件
 */
- (IBAction)switchToStoreList:(id)sender {
    if (![self.viewModel isSateliteCapsule]) {
        [self gotoStoreListVC];
    }
}

/**
 * 跳转到门店列表页面
 * 处理门店切换埋点和页面跳转
 */
- (void)gotoStoreListVC {
    WS(weakSelf);
    [FNFreshTarget_LoginModule_Helper loginFor215WithComplete:^{
        [weakSelf.trackDataTool agentClickChangeStore];
        NSDictionary *parameterDict = @{
            @"parameter_FNFreshStoreMapIntroduceVCAnimation":@(2),
            @"parameter_FNFreshStoreListEnterType":@(9),
        };
        UIViewController *viewController = [[FNMediator sharedInstance] getFreshStoreListModule_StoreListViewControllerWithParameter:parameterDict freshChoiceStoreIdBlock:nil];
        [FNFreshTabBarController pushViewController:viewController animated:YES];
    }];
//    [self.trackDataTool agentClickChangeStore];
//    NSDictionary *parameterDict = @{
//        @"parameter_FNFreshStoreMapIntroduceVCAnimation":@(2),
//        @"parameter_FNFreshStoreListEnterType":@(9),
//    };
//    UIViewController *viewController = [[FNMediator sharedInstance] getFreshStoreListModule_StoreListViewControllerWithParameter:parameterDict freshChoiceStoreIdBlock:nil];
//    [FNFreshTabBarController pushViewController:viewController animated:YES];
}

#pragma mark scrollView delegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat YOffect = scrollView.contentOffset.y;
    CGFloat rise = YOffect < 0 ? 0 : MIN(1, YOffect / 30);
    self.navigationBar.backgroundColor = [UIColor fn_colorWithColorKey:kFNWhite alpha:rise];
    self.naviTitleLab.textColor = [UIColor fn_colorWithColorKey:kFNBlack alpha:rise];
    self.naviSwitchImg.alpha = rise;
    self.navBottomLine.backgroundColor = [UIColor colorWithHex:0xE5E5E5 alpha:rise];

    if (rise) {
        [self.navigationBar setHidden:NO];
    } else {
        [self.navigationBar setHidden:YES];
    }

    [self updateBgImageViewWithContentOffset:scrollView.contentOffset];
    [self updateBgGradientViewPosition];
    [self updateBgGlobalGradientViewPosition];
    [self setNeedsStatusBarAppearanceUpdate];
}

/**
 * 更新背景图片视图位置
 * 根据滚动位置动态调整背景图片效果
 * 主要功能:
 * - 计算背景图片偏移量
 * - 处理视差滚动效果
 * @param contentOffset 滚动视图的当前偏移量
 */
- (void)updateBgImageViewWithContentOffset:(CGPoint)contentOffset {
    CGFloat bgImgTopDiff = 0;
    if (contentOffset.y > 0) {
        CGFloat bgImgViewHeight = CGRectGetHeight(self.bgImageView.bounds)+bgImgTopDiff;
        if (contentOffset.y <= bgImgViewHeight) {
            self.bgImgViewTopConstraint.constant = -contentOffset.y+bgImgTopDiff;
        } else {
            self.bgImgViewTopConstraint.constant = -CGRectGetHeight(self.bgImageView.bounds);
        }
    } else {
        self.bgImgViewTopConstraint.constant = bgImgTopDiff;
    }
}

/**
 * 更新背景渐变视图位置
 * 根据滚动位置调整渐变效果
 * 主要功能:
 * - 计算渐变视图高度
 * - 处理下拉放大效果
 */
- (void)updateBgGradientViewPosition {
    CGFloat offsetY = self.collectionView.contentOffset.y;
    if (offsetY <= 0){
        self.bgImgViewHeightConstraint.constant = 300 - offsetY;
    }
}

/**
 * 更新全局背景渐变视图位置
 * 处理整体背景的渐变效果
 * 主要功能:
 * - 计算渐变视图偏移量
 * - 更新渐变视图约束
 */
- (void)updateBgGlobalGradientViewPosition {
    CGFloat offsetY = self.collectionView.contentOffset.y;
//    NSLog(@"offsetY:%f",offsetY);
    CGFloat bgOffsetY = 225 + [UIDevice fnStatusBarHeight] - offsetY;
    [self.bgGradientView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_top).mas_offset(bgOffsetY);
    }];
    
    CGFloat bgCenterOffsetY = 175 + [UIDevice fnStatusBarHeight] - offsetY;
    [self.bgCenterGradientView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_top).mas_offset(bgCenterOffsetY);
    }];
}

#pragma mark - UICollectionViewDataSource

/**
 * 返回CollectionView的分组数量
 * @param collectionView 集合视图对象
 * @return 分组数量
 */
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return [self.viewModel numberOfSection];
}

/**
 * 返回指定分组的Item数量
 * @param collectionView 集合视图对象
 * @param section 分组索引
 * @return Item数量
 */
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self.viewModel numberOfItemsInSection:section];
}

/**
 * 配置并返回CollectionView的Cell
 * 根据不同的分组和索引返回对应的cell
 * 主要功能:
 * - 获取对应的cell类型
 * - 配置cell数据
 * - 设置cell代理
 * - 处理埋点统计
 * @param collectionView 集合视图对象
 * @param indexPath 索引路径
 * @return 配置好的cell对象
 */
- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    WS(weakSelf);
    FNFreshStoreServiceSectionModel *sectionModel = [self.viewModel sectionModelAtSection:indexPath.section];
    [self.trackDataTool handleExposureTrackData:sectionModel];
     
    switch (sectionModel.sectionType) {
            //积分商城 头部cell
        case FNFreshMrFreshStoreTypeCellHeaderStyle: {
            FNFreshStoreServiceModuleTitleCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshStoreServiceModuleTitleCell identifierDescString] forIndexPath:indexPath];
            cell.dataModel = sectionModel.moduleHeaderDataModel;
            return cell;
        }
            //图片集合
        case FNFreshMrFreshStoreTypeImagesCollection: {
            FNFreshStoreServiceImagesCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceImagesCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            return cell;
        }
            //金刚区
        case FNFreshMrFreshStoreTypeKingKonog: {
            FNFreshStoreServicesKingKongCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshStoreServicesKingKongCell identifierDescString] forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            return cell;
        }
            // 轮播
        case FNFreshMrFreshStoreTypeCarousel: {
            FNFreshStoreServiceCarouseCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceCarouseCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            self.carouseCell = cell;
            return cell;
        }
            // 旧版商店街
        case FNFreshMrFreshStoreTypeShoppingStreet: {
            FNFreshShoppingStreetCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshShoppingStreetCollectionCell class]) forIndexPath:indexPath];
            [cell setDataWith:sectionModel.storeServiceModel clickHandler:^(NSString *linkUrl) {
                [weakSelf.trackDataTool agentClickShoppingStreet];
                [weakSelf openPageWithURLString:linkUrl];
            }];
            return cell;
        }
            //单个横幅banner
        case FNFreshMrFreshStoreTypeSingleBanner: {
            FNFreshStoreServiceBannerCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceBannerCell class]) forIndexPath:indexPath];
            sectionModel.indexPath = indexPath;
            cell.sectionModel = sectionModel;
            cell.delegate = self;
            return cell;
        }
        case FNFreshMrFreshStoreTypeHotSelling:  //排行榜
        case FNFreshMrFreshStoreTypeShoppingStreetNew: { //新版商店街
            FNFreshStoreServiceBgViewCell *baseCell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceBgViewCell class]) forIndexPath:indexPath];
            baseCell.sectionModel = sectionModel;
            baseCell.delegate = self;
            return baseCell;
        }
            //头部门店信息
        case FNFreshMrFreshStoreTypeTopView: {
            FNFreshStoreServicesHeaderAddressCell *topCell = [collectionView dequeueReusableCellWithReuseIdentifier:[FNFreshStoreServicesHeaderAddressCell identifierDescString] forIndexPath:indexPath];
//            topCell.sectionModel = sectionModel;
//            topCell.delegate = self;
            topCell.dataModel = sectionModel.topAddressHeaderDataModel;
            return topCell;
        }
            //今日省钱
        case FNFreshMrFreshStoreTypeSaveMoney: {
            FNFreshStoreServiceSaveMoneyCell *saveMoneyCell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceSaveMoneyCell class]) forIndexPath:indexPath];
            saveMoneyCell.sectionModel = sectionModel;
            saveMoneyCell.delegate = self;
            return saveMoneyCell;
        }
            //活动报名+促销海报
        case FNFreshMrFreshStoreTypeApplyAndPoster: {
            FNFreshStoreServiceApplyAndPosterCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceApplyAndPosterCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            cell.delegate = self;
            return cell;
        }
            //商店街积分模块
        case FNFreshMrFreshStoreTypeShoppingStreetIntegral: {
            FNFreshShoppingStreetIntegralCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshShoppingStreetIntegralCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            cell.handleClickItem = ^(NSString *jumpUrl) {
                [weakSelf.trackDataTool agentClickShoppingStreetIntegralItem];
                [weakSelf openPageWithURLString:jumpUrl];
            };
            return cell;
        }
            //轮播+广告图
        case FNFreshMrFreshStoreTypeCarouselAndAds: {
            FNFreshStoreServiceBannerAndAdsCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceBannerAndAdsCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            return cell;
        }
            //商品陈列
        case FNFreshMrFreshStoreTypeGoodsModule: {
            FNFreshStoreServiceGoodsModuleCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceGoodsModuleCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            return cell;
        }
            //领券中心
        case FNFreshMrFreshStoreTypeCouponCenter: {
            FNFreshStoreServiceCouponCenterCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceCouponCenterCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            return cell;
        }
        default:
            break;
    }
    UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"StoreServicesDefaultCell" forIndexPath:indexPath];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    FNFreshStoreServiceSectionModel *sectionModel = [self.viewModel sectionModelAtSection:indexPath.section];
    switch (sectionModel.sectionType) {
        case FNFreshMrFreshStoreTypeSingleBanner: {
            [self.trackDataTool agentClickSingleBanner];
            //点击单个的横幅banner
            [self openPageWithURLString:sectionModel.storeServiceModel.pic.url];
        }
            //点击领券中心模块
        case FNFreshMrFreshStoreTypeCouponCenter: {
            [self.trackDataTool agentClickCouponCenter:sectionModel.storeServiceModel.styleType];
            [self openPageWithURLString:sectionModel.storeServiceModel.hotLinkUrl];
        }
            break;
        default:
            break;
    }
}

#pragma mark - UICollectionViewDelegateFlowLayout
/**
 * 返回补充视图
 * @param collectionView 集合视图对象
 * @param kind 补充视图类型
 * @param indexPath 索引路径
 * @return 配置好的补充视图
 */
- (UICollectionReusableView *)collectionView:(UICollectionView *)collectionView viewForSupplementaryElementOfKind:(NSString *)kind atIndexPath:(NSIndexPath *)indexPath {
    UICollectionReusableView *reusableView = nil;
    if ([kind isEqualToString:UICollectionElementKindSectionFooter]) {
        FNFreshMrFreshFooterCollectionReusableView *footerView = [collectionView dequeueReusableSupplementaryViewOfKind:kind withReuseIdentifier:FNFreshMrFreshFooterCollectionReusableViewIdentifier forIndexPath:indexPath];
        reusableView = footerView;
    }
    return reusableView;
}

/**
 * 返回分组的内边距
 * @param collectionView 集合视图对象
 * @param collectionViewLayout 布局对象
 * @param section 分组索引
 * @return 内边距
 */
- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    FNFreshStoreServiceSectionModel *sectionModel = [self.viewModel sectionModelAtSection:section];
    if (sectionModel.sectionType == FNFreshMrFreshStoreTypeTopView) {
        return UIEdgeInsetsZero;
    } else if ((sectionModel.sectionType == FNFreshMrFreshStoreTypeShoppingStreet ||
               sectionModel.sectionType == FNFreshMrFreshStoreTypeShoppingStreetNew) &&
               self.viewModel.hasShoppingStreetIntegralData) {
        return UIEdgeInsetsMake(-15, 0, 0, 0);
    } else if (sectionModel.sectionType == FNFreshMrFreshStoreTypeCellHeaderStyle){
        return UIEdgeInsetsMake(12, 12, 0, 12);
    } else if (sectionModel.sectionType == FNFreshMrFreshStoreTypeKingKonog) {
        return UIEdgeInsetsMake(0, 0, 0, 0);
    } else {
        return UIEdgeInsetsMake(12, 0, 0, 0);
    }
}

/**
 * 返回Item的尺寸
 * @param collectionView 集合视图对象
 * @param collectionViewLayout 布局对象
 * @param indexPath 索引路径
 * @return Item尺寸
 */
- (CGSize)collectionView:(UICollectionView *)collectionView
                  layout:(UICollectionViewLayout*)collectionViewLayout
  sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceSectionModel *sectionModel = [self.viewModel sectionModelAtSection:indexPath.section];
    CGSize size = [self.viewModel sizeForItemAtIndexPath:indexPath];
    NSLog(@"11-11 %lu--%f--%f",(unsigned long)sectionModel.sectionType, size.width,size.height);
    return size;
}

/**
 * 返回分组头部视图的尺寸
 * @param collectionView 集合视图对象
 * @param collectionViewLayout 布局对象
 * @param section 分组索引
 * @return 头部视图尺寸
 */
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section {
    return CGSizeZero;
}

/**
 * 返回分组尾部视图的尺寸
 * @param collectionView 集合视图对象
 * @param collectionViewLayout 布局对象
 * @param section 分组索引
 * @return 尾部视图尺寸
 */
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForFooterInSection:(NSInteger)section {
    return CGSizeZero;
}

/**
 * Cell即将显示时的回调
 * @param collectionView 集合视图对象
 * @param cell 即将显示的cell
 * @param indexPath cell的索引路径
 */
- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath{
    
}

#pragma mark - FNMrFreshStoreServiceCellDelegate
/**
 * 处理排行榜item点击
 * @param pondId 榜单ID
 * @param index 点击的索引位置
 * 包含埋点统计和榜单页面跳转
 */
- (void)storeServiceCellDidClickHotSellingItemWithPondId:(NSString *)pondId index:(NSInteger)index {
    WS(weakSelf);
    [FNFreshTarget_LoginModule_Helper loginFor215WithComplete:^{
        [weakSelf.trackDataTool agentClickHotSellingItemWithIndex:index];
        //进入榜单内页
        FNFreshRankSecondCategoryViewController *rankVc = [FNFreshRankSecondCategoryViewController offlineViewControllerWithCategoryName:@"" CmsId:pondId];
        [FNFreshTabBarController pushViewController:rankVc animated:YES];
    }];
//    [self.trackDataTool agentClickHotSellingItemWithIndex:index];
//    //进入榜单内页
//    FNFreshRankSecondCategoryViewController *rankVc = [FNFreshRankSecondCategoryViewController offlineViewControllerWithCategoryName:@"" CmsId:pondId];
//    [FNFreshTabBarController pushViewController:rankVc animated:YES];
}

/**
 * 处理新版商店街item点击
 * @param url 跳转链接
 * @param index 点击的索引位置
 * 包含埋点统计和页面跳转
 */
- (void)storeServiceCellDidClickNewShoppingStreetItemWithUrl:(NSString *)url index:(NSInteger)index {
    [self.trackDataTool agentClickShoppingStreetNew:index];
    [self openPageWithURLString:url];
}

/**
 * 处理领券中心点击
 * @param url 跳转链接
 * 包含埋点统计和页面跳转
 */
- (void)storeServiceCellDidClickCouponCenterWithUrl:(NSString *)url {
    [self.trackDataTool agentClickCouponModule];
    [self openPageWithURLString:url];
}

/**
 * 处理图片资源点击
 * @param url 跳转链接
 * @param isRight 是否为右侧图片
 * 包含埋点统计和页面跳转
 */
- (void)storeServiceCellDidClickPicResourcesWithUrl:(NSString *)url isRightItem:(BOOL)isRight {
    [self.trackDataTool agentClickSaveMoneyPicResourcesIsRight:isRight];
    [self openPageWithURLString:url];
}

/**
 * 处理限购商品点击
 * @param toastMessage 提示信息
 * 包含埋点统计和提示展示
 */
- (void)storeServiceCellDidClickLimitPurchaseGoodsItem:(NSString *)toastMessage {
    [self.trackDataTool agentClickLimitSales];
    [self startProgressText:toastMessage];
}

/**
 * 处理活动报名或促销海报点击
 * @param url 跳转链接
 * @param isApply 是否为活动报名
 * 包含:
 * - 埋点统计
 * - 登录状态检查
 * - 页面跳转
 */
- (void)storeServiceCellDidClickApplyAndPosterWithUrl:(NSString *)url isApply:(BOOL)isApply {
    [self.trackDataTool agentClickApplyOrPosterItem:isApply];
    
    if (isApply && ![[FNFreshUser shareInstance] isLogin]) {
        WS(weakSelf);
        [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
            [weakSelf openPageWithURLString:url];
        }];
    } else  {
        [self openPageWithURLString:url];
    }
}

/**
 * 更新banner高度
 * @param cell 需要更新的cell
 * @param indexPath cell的位置
 * 处理banner图片加载完成后的高度更新
 */
- (void)storeServiceCellBannerUpdateHeight:(FNFreshStoreServiceBaseCell *)cell indexPath:(NSIndexPath *)indexPath {
    if ([cell isKindOfClass:[FNFreshStoreServiceBannerCell class]]) {
        if (indexPath && (indexPath.section < [self.viewModel numberOfSection])) {
            WS(weakSelf);
            [UIView performWithoutAnimation:^{
                [weakSelf.collectionView reloadItemsAtIndexPaths:@[indexPath]];
            }];
        }
    }
}

#pragma mark FNHumanInteractionTransition
- (void)humanInteractionTransition:(NSString *)name object:(id)object userInfo:(NSDictionary *)userInfo{
    
    //公用部分header
    if ([name isEqualToString:FNFreshStoreServiceEventNameEnumMoreClickValue]){
        [self actionStoreServicePublicHeaderForObject:object userInfo:userInfo];
        return;
    }
    
    /**
     1.页面头部
     */
    if ([name isEqualToString:FNFreshStoreServiceEventAddressModuleClick]){
        [self actionStoreServiceAddressClick];
    } else if ([name isEqualToString:FNFreshStoreServiceEventPhoneButtonClick]){
        [self actionStoreServicePhoneButtonClick];
    } else if ([name isEqualToString:FNFreshStoreServiceEventInfoButtonClick]){
        [self actionStoreServiceInfoButtonClick];
    } else if ([name isEqualToString:FNFreshStoreServiceEventAtOnceLoginClick]){
        [self actionStoreServiceAtOnceLoginClick];
    } else if ([name isEqualToString:FNFreshStoreServiceEventCouponsClick]){
        [self actionStoreServiceDidClickCoupons];
    } else if ([name isEqualToString:FNFreshStoreServiceEventGrowUpClick]) {
        [self actionStoreServiceGrowUpClick];
    } else if ([name isEqualToString:FNFreshStoreServiceEventShoppingCardClick]) {
        [self actionStoreServiceShoppingCardClick];
    } else if ([name isEqualToString:FNFreshStoreServiceEventMemberShipCardClick]) {
        [self actionStoreServiceMemberShipCardClick];
    }
    
    /**
     2.金刚区
     */
   if ([name isEqualToString:FNFreshStoreServiceEventNameEnumKingKongUnfoldViewClickValue]){
        [self actionStoreServiceKingKongUnfoldViewClickForObject:object userInfo:userInfo];
        return;
    }
    
    //进入商详
    if ([name isEqualToString:FNFreshStoreServiceEventJumpToProductDetail]) {
        [self actionStoreServiceJumpToProductDetailForObject:object userInfo:userInfo];
        return;
    }
    
    //加购
    if ([name isEqualToString:FNFreshStoreServiceEventAddToShopCart]) {
        [self actionStoreServiceAddToShopCart:object userInfo:userInfo];
        return;
    }
    
    //打开链接
    if ([name isEqualToString:FNFreshStoreServiceEventOpenUrl]) {
        [self actionStoreServiceOpenUrlForObject:object userInfo:userInfo];
    }
}

- (void)actionStoreServiceOpenUrlForObject:(id)object userInfo:(NSDictionary *)userInfo {
    NSNumber *type = [userInfo safeObjectForKey:FNFreshUserInfoConstKey.type];
    NSNumber *index = [userInfo safeObjectForKey:FNFreshUserInfoConstKey.index] ?: [NSNumber numberWithInt:0];
    if (type != NULL) {
        if ([type integerValue] == FNFreshMrFreshStoreTypeGoodsModule) {
            NSString *style = [userInfo safeObjectForKey:FNFreshUserInfoConstKey.style];
            [self.trackDataTool handleClickGoodsModuleMore:style];
        } else if ([type integerValue] == FNFreshMrFreshStoreTypeImagesCollection) {
            NSString *style = [userInfo safeObjectForKey:FNFreshUserInfoConstKey.style];
            [self.trackDataTool agentClickImagesCollection:style index:[index integerValue]];
        } else {
            [self.trackDataTool handleClickTrackData:[type integerValue] index:[index integerValue]];
        }
    }
    
    NSString *url = [userInfo safeObjectForKey:FNFreshUserInfoConstKey.content];
    if (url != NULL && url.length > 0) {
        [[[FNFreshUrlRouter alloc] init] jumpControllerWithRemoteURLString:url completion:nil];
    }
}

#pragma mark -<event - 公用部分 header>
- (void)actionStoreServicePublicHeaderForObject:(id)object userInfo:(NSDictionary *)userInfo {
    [self.viewModel actionStoreServicePublicHeaderForObject:object userInfo:userInfo];
}

/**
 * 处理地址栏点击事件
 * 跳转到门店列表页面
 */
- (void)actionStoreServiceAddressClick {
    [self gotoStoreListVC];
}

/**
 * 处理电话按钮点击事件
 * 包含:
 * - 埋点统计
 * - 拨打电话功能
 * - 错误提示
 */
- (void)actionStoreServicePhoneButtonClick {
    [self.trackDataTool agentClickPhone];
    if ([self.viewModel phoneNumber].length > 0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            NSURL * phoneNumberUrl = [NSURL URLWithString:[NSString stringWithFormat:@"tel:%@",[self.viewModel phoneNumber]]];
            if([[UIApplication sharedApplication] canOpenURL:phoneNumberUrl]) {
                [[UIApplication sharedApplication] openURL:phoneNumberUrl options:@{} completionHandler:nil];
            }
        });
    } else {
        [self startProgressText:@"暂无电话"];
    }
}

/**
 * 处理规则按钮点击事件
 * 包含:
 * - 埋点统计
 * - 规则页面跳转
 */
- (void)actionStoreServiceInfoButtonClick {
    [self.trackDataTool agentClickRuleInfo];
    [self openPageWithURLString:[self.viewModel ruleUrl]];
}

/**
 * 处理立即登录点击事件
 * 跳转登录页面并刷新数据
 */
- (void)actionStoreServiceAtOnceLoginClick {
    [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
        [self requestHomeData];
    }];
}

/**
 * 处理优惠券点击事件
 * 包含:
 * - 埋点统计
 * - 优惠券页面跳转
 */
- (void)actionStoreServiceDidClickCoupons {
    [self.trackDataTool agentClickMyCoupons];
    [[FNFreshUrlRouter new] jumpControllerWithRemoteURLString:@"fnfresh://mycoupon" completion:nil];
}

/**
 * 处理成长值点击事件
 * 包含:
 * - 埋点统计
 * - 成长值页面跳转
 */
- (void)actionStoreServiceGrowUpClick {
    [self.trackDataTool agentClickGrowUpButton];
    [[FNFreshUrlRouter new] jumpControllerWithRemoteURLString:@"fnfresh://growthValue" completion:nil];
}

/**
 * 处理购物卡点击事件
 * 包含:
 * - 埋点统计
 * - 购物卡页面跳转
 */
- (void)actionStoreServiceShoppingCardClick {
    [self.trackDataTool agentClickShoppingCardButton];
    [[FNFreshUrlRouter new] jumpControllerWithRemoteURLString:@"fnfresh://onlineShopcard" completion:nil];
}

/**
 * 处理会员卡点击事件
 * 包含:
 * - 埋点统计
 * - 登录状态检查
 * - 绑卡状态请求
 */
- (void)actionStoreServiceMemberShipCardClick {
    [self.trackDataTool agentClickMemberCard];
    if ([FNFreshUser shareInstance].isLogin) {
        [self requestBindStatus];
    } else {
        WS(weakSelf)
        [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
            [weakSelf requestBindStatus];
        }];
    }
}

#pragma mark -<event - 金刚区>
- (void)actionStoreServiceKingKongUnfoldViewClickForObject:(id)object userInfo:(NSDictionary *)userInfo {
//    NSLog(@"金刚区-折叠点击");
    [self.viewModel actionStoreServiceKingKongUnfoldViewClickForObject:object userInfo:userInfo];
}

/**
 * 处理商品详情跳转事件
 * @param object 事件对象
 * @param userInfo 事件信息
 * 包含:
 * - 埋点统计
 * - 线下商品提示
 * - 商品详情页跳转
 */
- (void)actionStoreServiceJumpToProductDetailForObject:(id)object userInfo:(NSDictionary *)userInfo {
    FNFreshProductListMerchandiseModel *model = [userInfo safeObjectForKey:FNFreshUserInfoConstKey.content];
    [self.trackDataTool agentClickGoodsModuleEnterDeatil:model.productID isOffline:model.isOfflineGoods];
    
    if (model.isOfflineGoods) {
        [self startProgressText:@"该商品仅支持门店购买，实际售卖价格以门店价格为准"];
        return;
    }
    
    NSString *parmString = kMerchandiseParmString(model.productID,
                                                  model.saleStoreId,
                                                  model.channelStoreId,
                                                  model.isVoucherGoods,
                                                  model.merchantStoreId,
                                                  model.merchantCode,
                                                  model.merchantType);
    WS(weakSelf);
    [FNFreshTarget_LoginModule_Helper loginFor215WithComplete:^{
        UIViewController *detailVC = [[FNMediator sharedInstance] freshMerchandiseDetail_InitializeWithMerchandiseParmString:parmString];
       [weakSelf.navigationController pushViewController:detailVC animated:YES];
    }];
//     UIViewController *detailVC = [[FNMediator sharedInstance] freshMerchandiseDetail_InitializeWithMerchandiseParmString:parmString];
//    [self.navigationController pushViewController:detailVC animated:YES];
}

/**
 * 处理加购事件
 * @param object 事件对象
 * @param userInfo 事件信息
 * 包含:
 * - 埋点统计
 * - 加购动画
 * - 限购检查
 * - 加购请求
 */
- (void)actionStoreServiceAddToShopCart:(id)object userInfo:(NSDictionary *)userInfo {
    if (![object isKindOfClass:[FNFreshStoreServiceGoodsItemCell class]]) {
        return;
    }
    self.goodsTempCell = (FNFreshStoreServiceGoodsItemCell *)object;
    
    FNFreshProductListMerchandiseModel *model = [userInfo safeObjectForKey:FNFreshUserInfoConstKey.content];
    UIImageView *productImageView = [userInfo safeObjectForKey:@"productImgView"];
    self.animateImgView = [self animateImageViewWithProductImageView:productImageView];
    [self.trackDataTool agentClickGoodsModuleAddToShopCart:model.productID];
    
    FNFreshMerchandiseAddToCartItemModel *addCartItemModel = [model fn_toAddToCartItemModel];
    FNMerchandiseAddToCartExtraModel *extraModel = [FNMerchandiseAddToCartExtraModel new];
    extraModel.isPop = model.isPop.boolValue;
    extraModel.isMultiSpec = model.isMultiSpecifition.boolValue;
    extraModel.isPairItem = model.isPairItem.boolValue;
    WS(weakSelf);
    if (model.isLimit.integerValue == 1 && ![FNFreshUser shareInstance].isLogin) {//限购商品
        [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
            [weakSelf.addToCartHandler addToShopcartWithParameterItemArray:(@[addCartItemModel])
                                                                       extra:extraModel];
        }];
    } else {
        [self.addToCartHandler addToShopcartWithParameterItemArray:(@[addCartItemModel])
                                                               extra:extraModel];
    }
}

#pragma mark - FNFreshMerchandiseAddToCartHandlerDelegate
// 开始请求规格信息
- (void)addToCartHandlerDidStartRequestSpecification:(FNFreshMerchandiseAddToCartHandler *)handler {
    [self startProgress];
}

// 规格信息请求完成
- (void)addToCartHandlerDidCompleteRequestSpecification:(FNFreshMerchandiseAddToCartHandler *)handler error:(NSError *)error {
    [self stopProgress];
    if (error) {
        NSString *errStr = [error.userInfo safeObjectForKey:NSLocalizedDescriptionKey];
        [self startProgressText:errStr];
    }
}

// 开始加入购物车
- (void)addToCartHandlerDidStartAddToShopCart:(FNFreshMerchandiseAddToCartHandler *)handler {
    [self startProgress];
    
}
// 已经加入到购物车
- (void)addToCartHandlerDidCompleteAddToShopCart:(FNFreshMerchandiseAddToCartHandler *)handler resultModel:(FNFreshMerchandiseAddToCartResultModel *)resultModel {
    [self stopProgress];
    switch (resultModel.resultType) {
        case FNMerchandiseAddToCartResultTypeSuccess: {
            [self customAddShopCartAnimationWithAnimateImageView:self.animateImgView targetView:self.addShopCartTargetView];
            if ([self.goodsTempCell respondsToSelector:@selector(updatePurchasedNumBadge:)]) {
                [self.goodsTempCell updatePurchasedNumBadge:[NSString stringWithFormat:@"%ld",resultModel.goodsCount]];
                
            }
        }
            break;
        case FNMerchandiseAddToCartResultTypeFailure: {
            [self startProgressText:resultModel.addCartPromptMsg];
            break;
        }
        case FNMerchandiseAddToCartResultTypeLimitPurchase: {
            [self startProgressText:resultModel.addCartPromptMsg delay:1.5];
            WS(weakSelf);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)),
                           dispatch_get_main_queue(), ^{
                [weakSelf customAddShopCartAnimationWithAnimateImageView:self.animateImgView targetView:self.addShopCartTargetView];
                if ([weakSelf.goodsTempCell respondsToSelector:@selector(updatePurchasedNumBadge:)]) {
                    [weakSelf.goodsTempCell updatePurchasedNumBadge:[NSString stringWithFormat:@"%ld",resultModel.goodsCount]];
                }

            });
        }
            break;
        default:
            break;
    }
}

- (UIImageView *)animateImageViewWithProductImageView:(UIImageView *)productImageView {
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    CGRect imgRect = [keyWindow convertRect:productImageView.frame fromView:productImageView];
    UIImageView *animateImgView = [[UIImageView alloc] initWithFrame:imgRect];
    UIImage *animateImg = productImageView.image;
    if (animateImg == nil) {
        animateImg = [UIImage fnFresh_imageNamed:@"icon_placeholder"];
    }
    animateImgView.image = animateImg;
    return animateImgView;
}

- (void)customAddShopCartAnimationWithAnimateImageView:(UIImageView *)animateImageView targetView:(UIView *)targetView {
    CGRect fromRect = CGRectMake(animateImageView.frame.origin.x + 30, animateImageView.frame.origin.y - 150, 110, 110);
    CGRect toRect = CGRectMake(targetView.frame.origin.x, targetView.frame.origin.y, 40, 40);
    NSArray<UIView *> *targets = @[targetView];
    
    FNFreshShopCartAnimateManager *shopCartAnimateManager = [FNFreshShopCartAnimateManager shareInstance];
    [shopCartAnimateManager addToShopCardAnmation:animateImageView
                                       placeImage:animateImageView.image
                                     cornerRadius:55
                                             from:fromRect
                                               to:toRect
                                          targets:targets
                                        completed:^{
        UILabel *badgeLabel = [FNFreshTabBarController shareInstance].badgeLabel;
        UIImageView *shopCartTabIcon = [[FNFreshTabBarController shareInstance] shopCartTabIcon];
        //购物车气泡 动画
        [shopCartTabIcon iconTransformAnimationWithScale:1.2 duration:0.2 completed:nil];
        [badgeLabel iconTransformAnimationWithScale:1.2 duration:0.2 completed:nil];
    }];
}


#pragma mark getter 、setter
- (FNFreshMemberServiceViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [[FNFreshMemberServiceViewModel alloc] init];
    }
    return _viewModel;
}

- (FNFreshStoreServiceTrackDataTool *)trackDataTool {
    if (!_trackDataTool) {
        _trackDataTool = [[FNFreshStoreServiceTrackDataTool alloc] init];
    }
    return _trackDataTool;
}

- (UIScrollView *)emptyScrollView {
    if (!_emptyScrollView) {
        _emptyScrollView = [[UIScrollView alloc] init];
        [self.view addSubview:_emptyScrollView];
        [_emptyScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.view.mas_leading);
            make.top.equalTo(self.navigationBar.mas_bottom);
            make.trailing.equalTo(self.view.mas_trailing);
            make.bottom.equalTo(self.view.mas_bottom);
        }];
    }
    return _emptyScrollView;
}

- (FNFreshGradientBaseView *)bgGradientView{
    if (!_bgGradientView){
        _bgGradientView = [[FNFreshGradientBaseView alloc] initWithFrame:CGRectZero];
//        _bgGradientView.colors = @[(__bridge id)[UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.0].CGColor, (__bridge id)[UIColor colorWithRed:0/255.0 green:0/255.0 blue:0/255.0 alpha:0.2000].CGColor];
        
        _bgGradientView.colors = @[(__bridge id)[UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:1.0].CGColor, (__bridge id)[UIColor colorWithRed:242/255.0 green:242/255.0 blue:242/255.0 alpha:1.0].CGColor];
        _bgGradientView.startPoint = CGPointMake(0.5, 0);
        _bgGradientView.endPoint = CGPointMake(0.5, 1);
        _bgGradientView.locations = @[@(0), @(1.0f)];
    }
    return _bgGradientView;
}

- (FNFreshGradientBaseView *)bgCenterGradientView{
    if (!_bgCenterGradientView){
        _bgCenterGradientView = [[FNFreshGradientBaseView alloc] initWithFrame:CGRectZero];
        _bgCenterGradientView.colors = @[(__bridge id)[UIColor colorWithRed:242/255.0 green:242/255.0 blue:242/255.0 alpha:0.0].CGColor, (__bridge id)[UIColor colorWithRed:242/255.0 green:242/255.0 blue:242/255.0 alpha:1.0].CGColor];
        _bgCenterGradientView.startPoint = CGPointMake(0.5, 0);
        _bgCenterGradientView.endPoint = CGPointMake(0.5, 1);
        _bgCenterGradientView.locations = @[@(0), @(1.0f)];
    }
    return _bgCenterGradientView;
}

- (FNFreshMerchandiseAddToCartHandler *)addToCartHandler {
    if (!_addToCartHandler) {
        _addToCartHandler = [[FNFreshMerchandiseAddToCartHandler alloc] initWithDelegate:self];
    }
    return _addToCartHandler;
}

- (UIView *)addShopCartTargetView {
    if (!_addShopCartTargetView) {
        UITabBar *tabBar = [[FNFreshTabBarController shareInstance] tabBar];
        UIWindow *window = [UIApplication sharedApplication].delegate.window;
        CGPoint targetPoint = [tabBar convertPoint:[FNFreshTabBarController shareInstance].shopCartTabIconCenter toView:window];
        _addShopCartTargetView = [[UIView alloc] initWithFrame:CGRectMake(targetPoint.x, targetPoint.y, 40, 40)];
    }
    return _addShopCartTargetView;
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return self.collectionView.contentOffset.y > 70?UIStatusBarStyleDefault:UIStatusBarStyleLightContent;
}

@end
