//
//  FNFreshStoreMembershipServiceViewController.h
//  FNFresh
//
//  Created by wang<PERSON> on 2019/9/16.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

/**
 * FNFreshStoreMembershipServiceViewController
 * 会员服务门店首页视图控制器
 * 主要功能:
 * 1. 展示会员服务门店的主页面
 * 2. 管理店铺信息展示
 * 3. 处理会员服务相关功能
 * 4. 展示活动和促销信息
 * 5. 处理用户交互和导航
 * 6. 管理页面状态和数据刷新
 */

#import "FNFreshBaseViewController.h"

/**
 * 位置更新通知常量
 * 当用户位置发生变化时发送此通知
 * 用于触发页面数据的刷新，更新附近门店信息
 */
extern NSNotificationName const kFreshLocationNotification;

/**
 * 会员服务门店首页控制器
 * 继承自FNFreshBaseViewController基类
 * 负责展示和管理会员服务门店的主要界面
 * 包含店铺信息、会员服务、活动促销等功能模块
 */
@interface FNFreshStoreMembershipServiceViewController : FNFreshBaseViewController

@end

