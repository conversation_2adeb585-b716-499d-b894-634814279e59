//
//  FNFreshMiniStoreViewController.h
//  FNFresh
//
//  Created by wangbo on 2020/6/3.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

/**
 * FNFreshMiniStoreViewController
 * 迷你店首页视图控制器
 * 负责展示迷你店铺的主页面，包含店铺信息展示、会员卡绑定等功能
 */

#import "FNFreshBaseViewController.h"

@class FNFreshMiniStoreHomeResponseModel;

/// 位置更新通知
extern NSNotificationName const kFreshLocationNotification;
/// 滚动到顶部通知
extern NSString *const kFNFreshScrollToTopNotification;

/**
 * 迷你店首页控制器
 * 继承自FNFreshBaseViewController基类
 */
@interface FNFreshMiniStoreViewController : FNFreshBaseViewController

/// 首页数据模型，只读属性
@property (strong, nonatomic, readonly) FNFreshMiniStoreHomeResponseModel *responseModel;

@end

