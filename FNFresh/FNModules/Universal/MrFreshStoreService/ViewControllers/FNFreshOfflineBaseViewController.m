//
//  FNFreshOfflineBaseViewController.m
//  FNFresh
//
//  Created by wangbo on 2020/6/8.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

/**
 * FNFreshOfflineBaseViewController
 * 线下门店基础控制器，管理三种不同类型的门店视图控制器：
 * 1. 会员服务门店(memberVC)
 * 2. 迷你店(miniVC)
 * 3. 新店(newStoreVC)
 */

#import "FNFreshOfflineBaseViewController.h"
#import "FNFreshStoreMembershipServiceViewController.h"
#import "FNFreshNewStoreViewController.h"
#import "FNFreshMiniStoreViewController.h"
#import "UIViewController+FNFreshBundleHandler.h"
#import "FNFreshNewStorePopWindowParameter.h"
#import "FNFreshNewStorePopWindowResponse.h"
#import "FNFreshMrFreshService.h"
#import "FNFreshChooceMiniStoreViewController.h"
#import "UIViewController+FNMrFreshAnimatedTransitioning.h"
#import "FNFreshTabBarController.h"
#import "FNFreshGPSAddressResponseModel.h"
#import "FNFreshADPageViewController.h"

#import "FNFreshMrFreshPopupWindowParameterModel.h"
#import "FNFreshMrFreshPopupWindowResponseModel.h"
#import "FNFreshMrFreshNoviceGiftAlertViewController.h"
#import "FNFreshNewcomerChannelViewController.h"
#import "NSObject+FNFreshEncryptPersistence.h"
#import "FNFreshMrFreshSendGiftOnLoadResponseModel.h"


extern NSString * const kFreshStoreHomePopWindow;
extern NSString *const FNMrFreshLastNewGuidancePopupWindowTime;
extern NSString *const FNMrFreshLastCommonPopupWindowTime;
extern NSString *const FNMrFreshLastCommonPopupWindowSchedule;

@interface FNFreshOfflineBaseViewController ()

// 当前显示的视图控制器
@property (strong, nonatomic) UIViewController *currentVC;
// 会员服务门店视图控制器
@property (strong, nonatomic) FNFreshStoreMembershipServiceViewController *memberVC;
// 迷你店视图控制器
@property (strong, nonatomic) FNFreshMiniStoreViewController *miniVC;
// 新店视图控制器
@property (strong, nonatomic) FNFreshNewStoreViewController *newStoreVC;
// 标记当前页面是否可见
@property (assign, nonatomic, getter=isCurrentPage) BOOL currentPage;

@end

@implementation FNFreshOfflineBaseViewController

/**
 * 单例方法，确保整个应用中只有一个实例
 */
+ (instancetype)shareInstance {
    static FNFreshOfflineBaseViewController *_shareInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _shareInstance = [[FNFreshOfflineBaseViewController alloc] init];
    });
    return _shareInstance;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.currentPage = YES;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.currentPage = NO;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setChildVC];

    /// 检查是否存在广告页，以判断如何进行后续流程
    ///【定位在1. 会员服务门店(memberVC) 2. 迷你店(miniVC) 3. 新店(newStoreVC)】触发
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(checkAdPage) name:kFreshStoreHomePopWindow object:nil];
}

//MARK: - UI About

/**
 * 设置子视图控制器
 * 根据用户当前的店铺类型，初始化并显示对应的视图控制器
 */
- (void)setChildVC {
    [self addChildViewController:self.memberVC];
    [self addChildViewController:self.miniVC];
    [self addChildViewController:self.newStoreVC];
    
    if ([FNFreshUser shareInstance].isNewStore) {
        _currentVC = self.newStoreVC;
    } else if ([FNFreshUser shareInstance].storeType == 3) {
        _currentVC = self.miniVC;
    } else {
        _currentVC = self.memberVC;
    }
    [self.view addSubview:_currentVC.view];
    [_currentVC didMoveToParentViewController:self];
    _currentVC.view.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - (kBottomBarHeight));
}

/**
 * 切换到线下会员中心视图
 */
- (void)chooseOffLine {
    if (![_currentVC isKindOfClass:[FNFreshStoreMembershipServiceViewController class]]) {
        [self moveFromVC:_currentVC toVC:self.memberVC];
    }
}

/**
 * 切换到迷你店视图
 */
- (void)chooseMini {
    if (![_currentVC isKindOfClass:[FNFreshMiniStoreViewController class]]) {
        [self moveFromVC:_currentVC toVC:self.miniVC];
    }
}

/**
 * 切换到新店视图
 */
- (void)chooseNewStore {
    if (![_currentVC isKindOfClass:[FNFreshNewStoreViewController class]]) {
        [self moveFromVC:_currentVC toVC:self.newStoreVC];
    }
}

#pragma mark - Private Method

/**
 * 检查广告页面
 * 如果存在广告页面，则添加回调；否则直接请求弹窗
 */
- (void)checkAdPage {
    __weak typeof(self) weakSelf = self;
    void(^block)(void) = ^{
        [weakSelf requestPopWindow];
    };
    FNFreshADPageViewController *viewController = [FNFreshADPageViewController existInstance];
    if (viewController) {
        [viewController addBlockWith:block];
    } else {
        block();
    }
}

/**
 * 视图控制器切换动画
 * @param fromVC 当前显示的视图控制器
 * @param toVC 要切换到的视图控制器
 */
- (void)moveFromVC:(UIViewController *)fromVC
              toVC:(UIViewController *)toVC {
    if ([fromVC isEqual:toVC]) {
        return;
    }
    toVC.view.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - (kBottomBarHeight));
    [self.view addSubview:toVC.view];
    [[UIApplication sharedApplication] beginIgnoringInteractionEvents];
    [self transitionFromViewController:fromVC
                      toViewController:toVC
                              duration:0.5
                               options:UIViewAnimationOptionTransitionCrossDissolve
                            animations:nil
                            completion:^(BOOL finished) {
        [toVC didMoveToParentViewController:self];
        [fromVC willMoveToParentViewController:nil];
        [[UIApplication sharedApplication] endIgnoringInteractionEvents];
    }];
    _currentVC = toVC;
}

#pragma mark - Lazy Loading

/**
 * 懒加载会员服务门店视图控制器
 */
- (FNFreshStoreMembershipServiceViewController *)memberVC {
    if (!_memberVC) {
        _memberVC = [FNFreshStoreMembershipServiceViewController fnFreshBundleViewController];
    }
    return _memberVC;
}

/**
 * 懒加载迷你店视图控制器
 */
- (FNFreshMiniStoreViewController *)miniVC {
    if (!_miniVC) {
        _miniVC = [FNFreshMiniStoreViewController fnFreshBundleViewController];
    }
    return _miniVC;
}

/**
 * 懒加载新店视图控制器
 */
- (FNFreshNewStoreViewController *)newStoreVC {
    if (!_newStoreVC) {
        _newStoreVC = [FNFreshNewStoreViewController fnFreshBundleViewController];
    }
    return _newStoreVC;
}



#pragma mark - 线下门店多门店弹框

/**
 * 请求线下门店多门店弹框
 * 根据用户位置信息和店铺信息请求弹窗数据
 */
- (void)requestPopWindow {
    /// 如果不是线下门店首页，则不进行处理
    if ([FNFreshUser shareInstance].gpsAddressResponseModel.showType != 2) {
        return;
    }
    WS(weakSelf)
    FNFreshNewStorePopWindowParameter *para = [[FNFreshNewStorePopWindowParameter alloc] init];
    if ([CLLocationManager authorizationStatus] != kCLAuthorizationStatusDenied) {
        para.currentLatitude = [FNFreshUser shareInstance].latitude;
        para.currentLongitude = [FNFreshUser shareInstance].longitude;
    }
    para.isRaiseCardLocation = [FNFreshUser shareInstance].gpsAddressResponseModel.isRaiseCardLocation;
    FNFreshShopInfoModel *shopInfo = [[FNFreshShopInfoModel alloc] init];
    shopInfo.shopId = [FNFreshUser shareInstance].shopId;
    shopInfo.storeType = [FNFreshUser shareInstance].storeType;
    shopInfo.shopLatitude = [FNFreshUser shareInstance].shopLatitude;
    shopInfo.shopLongitude = [FNFreshUser shareInstance].shopLongitude;
    shopInfo.scopeType = [FNFreshUser shareInstance].scopeType;
    shopInfo.isNewStore = [FNFreshUser shareInstance].isNewStore;
    shopInfo.businessId = [FNFreshUser shareInstance].businessId;
    shopInfo.shopName = [FNFreshUser shareInstance].shopName;
    para.shopInfo = shopInfo;
    [FNFreshMrFreshService requestNewStorePopWindow:para success:^(FNFreshNewStorePopWindowResponse *responseObject, BOOL isCache) {
        /// 判断是否弹框
        if ([responseObject.popType isEqualToString:@"1"]) {
            [weakSelf storeListPopWindow:responseObject];
        } else {
            [[FNFreshTabBarController shareInstance] offlineToOnline];
        }
        /// 显示默认的第一个店铺信息
        FNFreshShopInfoModel *shopInfo = [responseObject.shopList safeObjectAtIndex:0];
        if (shopInfo) {
            [weakSelf showFirstShopInfo:shopInfo];
        }
    } failure:^(FNFreshNewStorePopWindowResponse *responseObject, NSError *error) {}];
}

/**
 * 显示默认的第一个店铺信息
 * @param shopInfo 店铺信息模型
 */
- (void)showFirstShopInfo:(FNFreshShopInfoModel *)shopInfo {
    [[FNFreshUser shareInstance] holdShopInfomationWithShopInfoModel:shopInfo errorType:0 needFresh:false];
    /// 迷你店
    if (shopInfo.storeType == 3 && !shopInfo.isNewStore) {
        [self chooseMini];
    } else {
        /// 新店
        if (shopInfo.isNewStore) {
            [self chooseNewStore];
        }
        /// 会员中心
        else {
            [self chooseOffLine];
        }
    }
}

/**
 * 显示店铺列表弹窗
 * @param response 弹窗数据响应模型
 */
- (void)storeListPopWindow:(FNFreshNewStorePopWindowResponse *)response {
    if (!self.isCurrentPage) {
        return;
    }
    WS(weakSelf)
    FNFreshChooceMiniStoreViewController *vc = [FNFreshChooceMiniStoreViewController instanceWithData:response handler:^(FNFreshShopInfoModel *shopInfo) {
        [[FNFreshUser shareInstance] holdShopInfomationWithShopInfoModel:shopInfo errorType:0 needFresh:false];
        /// 迷你店
        if (shopInfo.storeType == 3 && !shopInfo.isNewStore) {
            [weakSelf chooseMini];
        } else {
            /// 新店
            if (shopInfo.isNewStore) {
                [weakSelf chooseNewStore];
            }
            /// 会员中心
            else {
                [weakSelf chooseOffLine];
            }
        }
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"143083",
            @"page_id" :@"117",
            @"track_type":@"2",
        }];
        
        [[FNFreshTabBarController shareInstance] offlineToOnline];
    }];
    CGFloat height = MIN(88 * (response.shopList.count + 1), 434*Ratio);
    [self fn_presentViewController:vc customAnimateType:FNFreshMrFreshCustomAnimateTypeNone viewSize:CGSizeMake(325*Ratio, height) duration:0.75 alpha:0.5 handle:nil];
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"143082",
        @"page_id" :@"117",
        @"track_type":@"6",
    }];
}

/**
 * 判断当前页面是否可见且无模态视图控制器
 */
- (BOOL)isCurrentPage {
    
    return _currentPage && ![FNFreshTabBarController shareInstance].navigationController.hasPresentedViewController;
}

- (void)requestPopupWindowInfo {
    if (!self.isCurrentPage ||
        !FNFreshUser.shareInstance.shouldRequestHomePopwindow) {
        return;
    }
    
    __weak typeof(self)weakSelf = self;
    
    FNFreshMrFreshPopupWindowParameterModel *parameterModel = [[FNFreshMrFreshPopupWindowParameterModel alloc] init];
    parameterModel.storeCode = [FNFreshUser shareInstance].shopId;
    parameterModel.noviciateGiftBombTime = [[self readFromFile:FNMrFreshLastNewGuidancePopupWindowTime] doubleValue];
    parameterModel.homePageType = 1;

    [FNFreshMrFreshService requestMrFreshPopupWindowWithParameter:parameterModel success:
     ^(id responseObject, BOOL isCache) {
        // 处理response
        [weakSelf handlePopupWindowWithResponseModel:responseObject];
    } failure:^(id responseObject, NSError *error) {
//        [weakSelf handlePopupWindowWithResponseModel:nil];
    }];
}

- (void)handlePopupWindowWithResponseModel:(FNFreshMrFreshPopupWindowResponseModel *)responseModel {
    if (!self.isCurrentPage) {
        //如果已经跳转到其他页面  这里就不弹窗
        return;
    }
    //说明是新人礼包
    if (responseModel.noviciateGift) {
        
        if ([self trackPageId].length > 0) {
            //新人礼包弹窗曝光
            [FNFreshAgent eventWithTrackDataPrameters:@{
                @"page_col":@"215008",
                @"page_id" :[self trackPageId],
                @"track_type":@"6",
            }];
        }
        
        [self presentNoviceGiftBagViewControllerWithNewGuidanceModel:responseModel.noviciateGift];
        [self writeToFile:@(responseModel.noviciateGift.noviciateGiftBombTime) key:FNMrFreshLastNewGuidancePopupWindowTime];
    }
}

// 新人礼包
- (void)presentNoviceGiftBagViewControllerWithNewGuidanceModel:(FNMrFreshPopupWindowNewGuidanceModel *)newGuidanceModel {
    WS(weakSelf);
    FNFreshMrFreshNoviceGiftAlertViewController *viewController =
    [FNFreshMrFreshNoviceGiftAlertViewController initWithNewGuidanceModel:newGuidanceModel
                                                                   handle:^(BOOL dismiss) {
        if (!dismiss) {
            [weakSelf handleClickNoviceGiftPopwindowButton];
        }
    }];
    
    CGFloat width = 375;
    CGFloat height = 452;
    FNFreshUser.shareInstance.shouldRequestHomePopwindow = false;
    [self fn_presentViewController:viewController
                 customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack
                          viewSize:CGSizeMake(width, height)
                          duration:0.75
                             alpha:0.5
                            handle:nil];
}

- (void)handleClickNoviceGiftPopwindowButton {
    if ([self trackPageId].length > 0) {
        //点击新人礼包弹窗中按钮
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"215009",
            @"page_id" :[self trackPageId],
            @"track_type":@"2",
        }];
    }
    
    if ([FNFreshUser shareInstance].isLogin) {
        // 已登录状态 按钮名称为立即查看 跳转到新人频道页
        FNFreshNewcomerChannelViewController *vc = [FNFreshNewcomerChannelViewController new];
        [[FNFreshTabBarController shareInstance] pushViewController:vc animated:YES];
    } else {
        [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
        }];
    }
}

- (NSString *)trackPageId {
    NSString *pageId = @"";
    if ([_currentVC isKindOfClass:[FNFreshStoreMembershipServiceViewController class]]) {
        pageId = @"211";
    }
//    else if ([_currentVC isKindOfClass:[FNFreshNewStoreViewController class]]) {
//        pageId = @"160";
//    }
    return pageId;
}

@end
