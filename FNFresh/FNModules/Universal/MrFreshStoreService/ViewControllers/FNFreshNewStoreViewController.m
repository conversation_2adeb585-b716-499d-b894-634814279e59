//
//  FNFreshNewStoreViewController.m
//  FNFresh
//
//  Created by wangbo on 2020/10/15.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshNewStoreViewController.h"
#import "UIViewController+FNNavigationBarHidden.h"
#import "FNFreshNewStoreViewModel.h"
#import "UIViewController+FNFreshEmptyDataSet.h"
#import "FNFreshNewStoreTopCollectionViewCell.h"
#import "FNFreshShoppingStreetCollectionCell.h"
#import "FNFreshStoreServiceBannerCell.h"
#import "FNFreshUrlRouter.h"
#import "FNMediator+FreshAddressModule.h"
#import "FNFreshTabBarController.h"
#import "FNFreshNewBindCardResponseModel.h"
#import "FNMediator+FNFreshUserCenterModule.h"
#import "FNAlertView+UIAlertController.h"
#import "FNFreshMemberCardContainerVC.h"
#import "FNFreshNewStoreHomePopGiftWindowResponseModel.h"
#import "FNFreshMrFreshService.h"
#import "FNFreshMJRefreshHeader.h"
#import "FNFreshMJRefreshFooter.h"
#import "FNFreshNormalRefreshHeader.h"
#import "FNFreshNewStoreGiftPopWindowViewController.h"
#import "UIViewController+FNMrFreshAnimatedTransitioning.h"
#import "FNFreshCouponViewController.h"
#import "FNFreshMrFreshConstantCacheHandler.h"
#import "FNFreshLoginViewController.h"
#import "FNFreshStoreServiceBgViewCell.h"
#import "FNFreshShoppingStreetIntegralCell.h"
#import "UIColor+Gradient.h"
#import "FNFreshImproveInfoManager.h"
#import "UIViewController+FNTopVC.h"

/// 新店页面界面代理，实现UICollectionView数据源、布局代理和店铺服务Cell代理
@interface FNFreshNewStoreViewController ()<UICollectionViewDataSource, UICollectionViewDelegateFlowLayout, FNMrFreshStoreServiceCellDelegate>

/// 顶部导航视图
@property (weak, nonatomic) IBOutlet UIView *topNavigationView;
/// 顶部导航栏高度约束
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topNaviHeight;
/// 主要内容展示的CollectionView
@property (weak, nonatomic) IBOutlet UICollectionView *collectionView;
/// 空状态展示的ScrollView
@property (strong, nonatomic) UIScrollView *emptyScrollView;
/// 视图模型，处理数据和业务逻辑
@property (strong, nonatomic) FNFreshNewStoreViewModel *viewModel;
/// 是否正在刷新
@property (assign, nonatomic, getter=isRefreshing) BOOL refreshing;
/// 当前页面是否可见
@property (assign, nonatomic, getter=isCurrentPage) BOOL currentPage;
/// 小火箭是否显示
@property (assign, nonatomic) BOOL isRocketShow; // 小火箭
/// 顶部背景视图
@property (weak, nonatomic) IBOutlet UIView *topBgView;
/// 顶部背景视图顶部约束
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *topBgViewTopConstraint;
/// CollectionView顶部约束
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewTopConstraint;
/// 渐变视图
@property (weak, nonatomic) IBOutlet UIView *gradientView;
/// 渐变视图顶部约束
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *gradientViewTopConstraint;

@end

@implementation FNFreshNewStoreViewController

#pragma mark - Life Cycle

/// 视图加载完成时调用，初始化UI、添加观察者、注册Cell等
- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self addObserver];
    [self registerCollectionReusableView];
    [self requestHomeData];
}

/// 视图即将显示时调用，处理页面埋点、当前页面状态更新、信息完善提示等
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"143001",
        @"page_id" :@"160",
        @"track_type":@"1",
    }];
    
    self.currentPage = YES;
    WS(weakSelf);
    [FNFreshImproveInfoManager.shared alertImproveInfoIfNeeded:self completion:^(BOOL isSuccess, NSString * _Nonnull toast) {
        if (toast.length > 0) {
            [UIViewController.topViewController startProgressText:toast];
        }
        [weakSelf requestNewGiftPopupWindowInfo];
    }];
}

- (void)requestNewGiftPopupWindowInfo {
    [[FNFreshOfflineBaseViewController shareInstance] requestPopupWindowInfo];
}

/// 视图已经显示时调用，处理信息完善相关逻辑
- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [[FNFreshImproveInfoManager shared] viewDidAppearWith:self];
}

/// 视图即将消失时调用
- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
}

#pragma mark - UI About

/// 初始化UI界面，设置导航栏、背景色、布局约束等
- (void)setupUI {
    self.fnPreferNavigationBarHidden = YES;
    self.view.backgroundColor = [UIColor hex:@"#F2F2F2"];
    self.edgesForExtendedLayout = UIRectEdgeTop;
    self.collectionViewTopConstraint.constant = [UIApplication sharedApplication].statusBarFrame.size.height;
    self.collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    self.collectionView.contentInset = UIEdgeInsetsMake(0, 0, 12, 0);
    self.topNaviHeight.constant = [UIApplication sharedApplication].statusBarFrame.size.height + 44;
    self.gradientViewTopConstraint.constant = [self.viewModel heightForTopViewCell];
    self.gradientView.backgroundColor = [UIColor fn_colorGradientChangeWithSize:CGSizeMake(SCREEN_WIDTH, 180) direction:FNGradientChangeDirectionVertical startColor:[UIColor whiteColor] endColor:[UIColor hex:@"#F2F2F2"]];
}

/// 注册CollectionView的可重用视图，包括各种类型的cell注册
- (void)registerCollectionReusableView {
    NSArray *cellClassArray = @[
        NSStringFromClass([FNFreshNewStoreTopCollectionViewCell class]),
        NSStringFromClass([FNFreshShoppingStreetCollectionCell class]),
        NSStringFromClass([FNFreshStoreServiceBannerCell class]),
        NSStringFromClass([FNFreshStoreServiceBgViewCell class]),
        NSStringFromClass([FNFreshShoppingStreetIntegralCell class]),
    ];
    for (NSString *cellClassString in cellClassArray) {
        [self.collectionView registerNib:[UINib nibWithNibName:cellClassString bundle:[FNFreshBundleHandler fnFreshBundle]] forCellWithReuseIdentifier:cellClassString];
    }
}

/// 添加上拉加载和下拉刷新手势
- (void)addRefreshGestures {
    
    if (!self.collectionView.mj_header) {
        __weak typeof(self)weakSelf = self;
        FNFreshNormalRefreshHeader *header;
        
        header = [FNFreshNormalRefreshHeader headerWithRefreshingBlock:^{
            if (weakSelf.isRefreshing) {
                [weakSelf.collectionView.mj_header endRefreshing];
                return ;
            }
            [weakSelf requestHomeData];
        }];
        [header.arrowView setImage:[UIImage imageNamed:@"loading_white"]];
        header.stateLabel.textColor = [UIColor whiteColor];
        
        header.beginRefreshingCompletionBlock = ^{
            weakSelf.refreshing = YES;
        };
        header.endRefreshingCompletionBlock = ^{
            weakSelf.refreshing = NO;
        };
        self.collectionView.mj_header = header;
    };
    self.collectionView.mj_footer = nil;
}

#pragma mark - Notification About

/// 添加通知观察者，监听地址变更、登录状态、数据刷新等通知
- (void)addObserver {
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFNFreshAddressChangedNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFNFreshLoginStateDidChangeNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFNFreshRefreshHomeDataNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestHomeData) name:kFreshLocationNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(scrollToTop) name:kFNFreshScrollToTopNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleWillEnterForeground) name:UIApplicationWillEnterForegroundNotification object:nil];
}

- (void)handleWillEnterForeground {
    [self requestHomeData];
    [self requestNewGiftPopupWindowInfo];
}

#pragma mark - Private Method

/// 滚动到顶部，响应双击标签栏的回到顶部事件
- (void)scrollToTop {
    if (!self.isCurrentPage) {
        return;
    }
    [self.collectionView setContentOffset:CGPointZero animated:YES];
}

/// 判断当前页面是否可见且无模态视图
- (BOOL)isCurrentPage {
    return _currentPage && ![FNFreshTabBarController shareInstance].navigationController.hasPresentedViewController;
}

#pragma mark - Request About

/// 请求首页数据，获取店铺信息、活动信息等数据
- (void)requestHomeData {
    WS(weakSelf)
    [self.viewModel requestNewStoreHomeIndexWithCallBack:^(BOOL isSuccess, NSError *error) {
        if (weakSelf.emptyScrollView) {
            [weakSelf.emptyScrollView removeFromSuperview];
            weakSelf.emptyScrollView = nil;
        }
        if (isSuccess) {
            /// 如果闭店、启动则切换到线下会员中心视图
            if (weakSelf.viewModel.responseModel.shopInfo.storeStatus == 4 ||
                weakSelf.viewModel.responseModel.shopInfo.storeStatus == 5) {
                [[FNFreshOfflineBaseViewController shareInstance] chooseOffLine];
                return;
            }
            /// 展示新店
            if (weakSelf.viewModel.responseModel.shopInfo.isNewStore) {
                [[FNFreshOfflineBaseViewController shareInstance] chooseNewStore];
                
            }
            /// 展示迷你店
            else if (weakSelf.viewModel.responseModel.shopInfo.storeType == 3) {
                [[FNFreshOfflineBaseViewController shareInstance] chooseMini];
                return;
            }
            /// 容错
            else {
                [[FNFreshOfflineBaseViewController shareInstance] chooseOffLine];
                return;
            }
            [weakSelf.collectionView reloadData];
            weakSelf.collectionView.contentOffset = CGPointZero;
            [weakSelf addRefreshGestures];
            [weakSelf.collectionView.mj_header endRefreshing];
        } else {
            [weakSelf.collectionView.mj_header endRefreshing];
            [weakSelf fnFresh_addNetworkErrorEmptyDataSetWithTarget:weakSelf.emptyScrollView errorCode:@(error.code) errorDesc:error.localizedDescription refreshEvent:^{
                [weakSelf requestHomeData];
            }];
        }
    }];
}

/// 请求礼包弹框数据
/// @param type 礼包类型（1：只发新人礼包 2：只发开业大礼包 3：新人礼包+开业大礼包发送）
- (void)requestGiftPopWindowWithType:(NSString *)type {
    WS(weakSelf)
    [FNFreshMrFreshService requestNewStoreGiftPopWindow:type success:^(FNFreshNewStoreHomePopGiftWindowResponseModel *responseObject, BOOL isCache) {
        // 礼包弹框
        if (responseObject.couponList.count > 0) {
            [weakSelf giftPopWindow:responseObject];
        }
        [weakSelf requestHomeData];
        
    } failure:^(FNFreshNewStoreHomePopGiftWindowResponseModel *responseObject, NSError *error) {
        [weakSelf startProgressText:responseObject.errorDesc delay:1];
    }];
}

/// 显示礼包弹窗
/// @param dataModel 礼包数据模型
- (void)giftPopWindow:(FNFreshNewStoreHomePopGiftWindowResponseModel *)dataModel {
    FNFreshNewStoreGiftPopWindowViewController *vc = [FNFreshNewStoreGiftPopWindowViewController  instanceWithDataModel:dataModel isNew:YES handler:^(BOOL dismiss) {
        if (!dismiss) {
            // 跳我的优惠券
            FNFreshCouponViewController *vc = [[FNFreshCouponViewController alloc] initWithCouponClass:FNFreshCouponClassCoupon];
            [FNFreshTabBarController pushViewController:vc animated:YES];
        }
    }];
    CGFloat maxHeight = IS_IPHONE_5?450:500;
    CGFloat countHeight = 270 + dataModel.couponList.count * 100;
    [self fn_presentViewController:vc customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack viewSize:CGSizeMake(315, MIN(maxHeight, countHeight)) duration:0.75 alpha:0.5 handle:nil];
}

/// 领取新人礼包、开业礼包、立即加入会员按钮点击处理
- (void)topBtnClick {
   
    WS(weakSelf)
    [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
        if (weakSelf.viewModel.responseModel.canSendOpenGift == 1) {
            //type： 1：只发新人礼包 2：只发开业大礼包 3：新人礼包+开业大礼包发送
            [weakSelf requestGiftPopWindowWithType:@"2"];
        }
    } targetShow:^(UIViewController * _Nonnull target) {
        if ([target isKindOfClass:UINavigationController.class]) {
            UINavigationController *nav = (UINavigationController *)target;
            for (UIViewController *vc in nav.viewControllers) {
                if ([vc isKindOfClass:[FNFreshLoginViewController class]]) {
                    FNFreshLoginViewController *loginVC = (FNFreshLoginViewController *)vc;
                    if ([self.viewModel.responseModel.btnStatus isEqualToString:@"1"]) {
                        loginVC.canNotGetCoupon = 1;
                        break;
                    }
                }
            }
        }
    }];
    
}

/// 切换门店按钮点击事件处理
- (IBAction)changeStoreClick:(id)sender {
    NSDictionary *parameterDict = @{
                                    @"parameter_FNFreshStoreMapIntroduceVCAnimation":@(2),
                                    @"parameter_FNFreshStoreListEnterType":@(10),
                                    };

    UIViewController *viewController = [[FNMediator sharedInstance] getFreshStoreListModule_StoreListViewControllerWithParameter:parameterDict freshChoiceStoreIdBlock:nil];
    [FNFreshTabBarController pushViewController:viewController animated:YES];
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"143003",
        @"page_id" :@"160",
        @"track_type":@"2",
    }];
}

/// 打开指定URL的页面
/// @param URLString 目标页面的URL
- (void)openPageWithURLString:(NSString *)URLString {
    FNFreshUrlRouter *URLRouter = [[FNFreshUrlRouter alloc] init];
    [URLRouter jumpControllerWithRemoteURLString:URLString completion:nil];
}

/// 处理按钮服务点击事件
/// @param textModel 按钮文本模型
- (void)dealWithBtnService:(FNMrFreshTextModel *)textModel {
    if (textModel.type == 60) { // 会员卡
        if ([FNFreshUser shareInstance].isLogin) {
            [self requestBindStatus];
        } else {
            WS(weakSelf)
            [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
                [weakSelf requestBindStatus];
            }];
        }
    } else {
        [self openPageWithURLString:textModel.linkUrl];
    }
}

/// 请求绑卡接口
- (void)requestBindStatus {
    WS(weakSelf)
    [[FNMediator sharedInstance] fnFreshUserCenterService_RequestBindMemberCard:^(FNFreshNewBindCardResponseModel *responseModel, BOOL isCache) {
        [weakSelf showMembershipCardVc];
    } failure:^(FNFreshNewBindCardResponseModel *responseModel, NSError *error) {
        [FNAlertView showWithTitle:@"温馨提示" message:responseModel.errorDesc cancelButtonTitle:@"好的" otherButtonTitles:nil tapBlock:nil];
    }];
    
}

/// 显示会员卡页面
- (void)showMembershipCardVc {
    NSString *attStr = [NSString stringWithFormat:@"%@_%@_%@_%@_%@", @"83",@"2",@"",@"",@""];
    [FNFreshAgent eventWithPage_col:@"116008" attributeStr:attStr];
    FNFreshMemberCardContainerVC *vc = [[FNFreshMemberCardContainerVC  alloc] initWithBarCodeVcWithEnterType:0 memCardVCType:0 cardId: @""];
    [FNFreshTabBarController pushViewController:vc animated:YES];
}

#pragma mark - UICollectionViewDataSource & Delegate

/// 返回CollectionView的分组数量
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return [self.viewModel numberOfSection];
}

/// 返回指定分组的item数量
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self.viewModel numberOfItemsInSection:section];
}

/// 配置并返回CollectionView的Cell
- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshStoreServiceSectionModel *sectionModel = [self.viewModel sectionModelAtSection:indexPath.section];
    UICollectionViewCell *collectionViewCell = nil;
    WS(weakSelf);
    switch (sectionModel.sectionType) {
        case FNFreshMrFreshStoreTypeTopView: {
            FNFreshNewStoreTopCollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshNewStoreTopCollectionViewCell class]) forIndexPath:indexPath];
            [cell setupWithDataModel:sectionModel.nStoreResponseModel handler:^{
                [weakSelf topBtnClick];
            } serviceItemClick:^(FNMrFreshTextModel *model) {
                [weakSelf dealWithBtnService:model];
            }];
            collectionViewCell = cell;
        }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreet: {
            FNFreshShoppingStreetCollectionCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshShoppingStreetCollectionCell class]) forIndexPath:indexPath];
            [cell setDataWith:sectionModel.storeServiceModel clickHandler:^(NSString *linkUrl) {
                [weakSelf openPageWithURLString:linkUrl];
                [FNFreshAgent eventWithTrackDataPrameters:@{
                    @"page_col":@"143011",
                    @"page_id" :@"160",
                    @"track_type":@"2",
                }];
            }];
            collectionViewCell = cell;
            [self agentShoppingStreetModuleDidAppear:false];
        }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreetNew: {
            FNFreshStoreServiceBgViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceBgViewCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            cell.delegate = self;
            collectionViewCell = cell;
            [self agentShoppingStreetModuleDidAppear:true];
        }
            break;
        case FNFreshMrFreshStoreTypeShoppingStreetIntegral: {
            FNFreshShoppingStreetIntegralCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshShoppingStreetIntegralCell class]) forIndexPath:indexPath];
            cell.sectionModel = sectionModel;
            cell.isNewStore = true;
            cell.handleClickItem = ^(NSString *jumpUrl) {
                [weakSelf openPageWithURLString:jumpUrl];
                [FNFreshAgent eventWithTrackDataPrameters:@{
                    @"page_col":@"176022",
                    @"page_id" :@"160",
                    @"track_type":@"2",
                }];
            };
            collectionViewCell = cell;
            [self agentShoppingStreetIntegralDidAppear:sectionModel];
        }
            break;
        case FNFreshMrFreshStoreTypeSingleBanner: {
            FNFreshStoreServiceBannerCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:NSStringFromClass([FNFreshStoreServiceBannerCell class]) forIndexPath:indexPath];

            cell.sectionModel = sectionModel;
            cell.delegate = self;
            sectionModel.indexPath = indexPath;
            collectionViewCell = cell;
            [self agentSingleBannerDidAppear];
        }
            break;
        default:
            break;
    }
    
    return collectionViewCell;
}

/// 处理Cell的点击事件
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    FNFreshStoreServiceSectionModel *sectionModel = [self.viewModel sectionModelAtSection:indexPath.section];
    switch (sectionModel.sectionType) {
        case FNFreshMrFreshStoreTypeSingleBanner: {
            [self agentClickSingleBanner];
            //点击单个的横幅banner
            [self openPageWithURLString:sectionModel.storeServiceModel.pic.url];
        }
            break;
        default:
            break;
    }
}

#pragma mark - UICollectionViewDelegateFlowLayout

/// 设置分组的内边距
- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    FNFreshStoreServiceSectionModel *sectionModel = [self.viewModel sectionModelAtSection:section];
    if (sectionModel.sectionType == FNFreshMrFreshStoreTypeTopView) {
        return UIEdgeInsetsZero;
    } else if (section == 1) {
        return UIEdgeInsetsMake(4, 0, 0, 0);
    }else if ((sectionModel.sectionType == FNFreshMrFreshStoreTypeShoppingStreet ||
                sectionModel.sectionType == FNFreshMrFreshStoreTypeShoppingStreetNew) &&
                self.viewModel.hasShoppingStreetIntegralData) {
         return UIEdgeInsetsMake(-15, 0, 0, 0);
     } else {
        return UIEdgeInsetsMake(12, 0, 0, 0);
    }
}

/// 设置Cell的尺寸
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return [self.viewModel sizeForItemAtIndexPath:indexPath];
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section {
    return CGSizeZero;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForFooterInSection:(NSInteger)section {
    return CGSizeZero;
}

#pragma mark - UIScrollViewDelegate

/// 处理滚动事件，控制导航栏显示、更新背景图位置等
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat YOffect = scrollView.contentOffset.y;
    if (self.topNavigationView.hidden && YOffect >= 0) {
        self.topNavigationView.hidden = NO;
    } else if (!self.topNavigationView.hidden && YOffect < 0) {
        self.topNavigationView.hidden = YES;
    }
    
    self.gradientViewTopConstraint.constant = [self.viewModel heightForTopViewCell]  - YOffect;
    [self updateTopBgImageViewWithContentOffset:scrollView.contentOffset];
}

/// 更新顶部背景图片视图的位置
/// @param contentOffset 滚动视图的偏移量
- (void)updateTopBgImageViewWithContentOffset:(CGPoint)contentOffset {
    CGFloat bgImgTopDiff = 0;
    if (contentOffset.y > 0) {
        CGFloat bgImgViewHeight = CGRectGetHeight(self.topBgView.bounds)+bgImgTopDiff;
        if (contentOffset.y <= bgImgViewHeight) {
            self.topBgViewTopConstraint.constant = -contentOffset.y+bgImgTopDiff;
        } else {
            self.topBgViewTopConstraint.constant = -CGRectGetHeight(self.topBgView.bounds);
        }
    } else {
        self.topBgViewTopConstraint.constant = bgImgTopDiff;
    }
}

#pragma mark - FNMrFreshStoreServiceCellDelegate

/// 处理新版商店街item点击事件
- (void)storeServiceCellDidClickNewShoppingStreetItemWithUrl:(NSString *)url index:(NSInteger)index {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"171046",
        @"page_id" :@"160",
        @"track_type":@"2",
        @"col_position":[NSString stringWithFormat:@"%ld",index + 1],
    }];
    [self openPageWithURLString:url];
}

/// 更新banner高度
- (void)storeServiceCellBannerUpdateHeight:(FNFreshStoreServiceBaseCell *)cell indexPath:(NSIndexPath *)indexPath {
    if ([cell isKindOfClass:[FNFreshStoreServiceBannerCell class]]) {
        if (indexPath && (indexPath.section < [self.viewModel numberOfSection])) {
            WS(weakSelf);
            [UIView performWithoutAnimation:^{
                [weakSelf.collectionView reloadItemsAtIndexPaths:@[indexPath]];
            }];
        }
    }
}

#pragma mark - Getters & Setters

/// 懒加载视图模型
- (FNFreshNewStoreViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [[FNFreshNewStoreViewModel alloc] init];
    }
    return _viewModel;
}

/// 懒加载空状态ScrollView
- (UIScrollView *)emptyScrollView {
    if (!_emptyScrollView) {
        _emptyScrollView = [[UIScrollView alloc] init];
        [self.view addSubview:_emptyScrollView];
        [_emptyScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.view.mas_leading);
            make.top.equalTo(self.topNavigationView.mas_bottom);
            make.trailing.equalTo(self.view.mas_trailing);
            make.bottom.equalTo(self.view.mas_bottom);
        }];
    }
    return _emptyScrollView;
}

#pragma mark - Event Tracking

/// 新店门店频道页商店街模块曝光埋点
- (void)agentShoppingStreetModuleDidAppear:(BOOL)isNew {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"171045",
        @"page_id" :@"160",
        @"track_type":@"6",
        @"col_position":isNew ? @"2": @"1"
    }];
}

/// 新店门店频道页积分兑券模块曝光埋点
- (void)agentShoppingStreetIntegralDidAppear:(FNFreshStoreServiceSectionModel *)sectionModel {
    NSInteger count = sectionModel.storeServiceModel.picList2.count;
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"176020",
        @"page_id" :@"160",
        @"track_type":@"6",
        @"col_position":[NSString stringWithFormat:@"%ld",count],
    }];
}

/// 新店门店频道页横幅banner模块曝光埋点
- (void)agentSingleBannerDidAppear {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"180002",
        @"page_id" :@"160",
        @"track_type":@"6",
    }];
}

/// 新店门店频道页点击横幅banner埋点
- (void)agentClickSingleBanner {
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"180003",
        @"page_id" :@"160",
        @"track_type":@"2",
    }];
}

@end
