//
//  FNFreshMiniStoreViewController.m
//  FNFresh
//
//  Created by wangbo on 2020/6/3.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

/**
 * FNFreshMiniStoreViewController
 * 迷你店首页视图控制器的具体实现
 */

#import "FNFreshMiniStoreViewController.h"
#import "UIViewController+FNNavigationBarHidden.h"
#import "FNFreshMiniHomeTopView.h"
#import "FNFreshMiniTableViewCell.h"
#import "FNFreshMiniStoreHomeParameterModel.h"
#import "FNFreshMiniStoreHomeResponseModel.h"
#import "FNFreshMrFreshConstantCacheHandler.h"
#import "FNFreshMiniStoreInfoResponseModel.h"
#import "FNFreshMrFreshService.h"
#import "FNMediator+FNFreshUserCenterModule.h"
#import "FNFreshMemberCardContainerVC.h"
#import "FNFreshTabBarController.h"
#import "FNMediator+FNFreshUserCenterModule.h"
#import "UIViewController+FNMrFreshAnimatedTransitioning.h"
#import "FNFreshOfflineBaseViewController.h"
#import "FNFreshTabBarController.h"
#import "FNMediator+FreshAddressModule.h"
#import "FNFreshNewBindCardResponseModel.h"
#import "FNAlertView+UIAlertController.h"

/// 迷你店TableView的Cell重用标识符
static NSString * const FNFreshMiniTableViewCellIdentifier = @"FNFreshMiniTableViewCell";

@interface FNFreshMiniStoreViewController ()<UITableViewDelegate,UITableViewDataSource>

/// 主要展示内容的TableView
@property (weak, nonatomic) IBOutlet UITableView *miniTableView;
/// 头部视图
@property (strong, nonatomic) FNFreshMiniHomeTopView *headerView;
/// 本地图片数组
@property (strong, nonatomic) NSMutableArray *localImgs;
/// 本地图片尺寸数组
@property (strong, nonatomic) NSMutableArray *localImgSizeArr;
/// 首页数据模型
@property (strong, nonatomic) FNFreshMiniStoreHomeResponseModel *responseModel;
/// 当前页面是否可见
@property (assign, nonatomic, getter=isCurrentPage) BOOL currentPage;
/// 迷你店铺ID
@property (strong, nonatomic) NSString *miniShopId;
/// 店铺信息数据模型
@property (strong, nonatomic) FNFreshMiniStoreInfoResponseModel *storeInfo;

@end

@implementation FNFreshMiniStoreViewController

#pragma mark - Life Cycle

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setUI];
    [self addObserver];
    [self requestMiniHome];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.currentPage = YES;
    
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.currentPage = NO;
}

#pragma mark - UI About

/**
 * 初始化UI界面
 * 设置导航栏隐藏、TableView相关设置、注册Cell等
 */
- (void)setUI {
    self.fnPreferNavigationBarHidden = YES;
    self.edgesForExtendedLayout = UIRectEdgeTop;
    self.miniTableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    
    UIView *header = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, self.headerView.bounds.size.height - 56)];
    header.backgroundColor = [UIColor clearColor];
    self.miniTableView.tableHeaderView = header;
    [self.miniTableView addSubview:self.headerView];
    self.headerView.frame = CGRectMake(0, 0, self.headerView.bounds.size.width, self.headerView.bounds.size.height);
    [self.miniTableView.tableHeaderView removeFromSuperview];

    UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 40)];
    footer.backgroundColor = [UIColor hexString:@"#45A04C"];
    self.miniTableView.tableFooterView = footer;
    [self.miniTableView registerNib:[FNFreshMiniTableViewCell fnFreshNib] forCellReuseIdentifier:FNFreshMiniTableViewCellIdentifier];
    
    [FNFreshAgent eventWithTrackDataPrameters: @{@"page_col":@"138003",
                                                 @"page_id":@"141",
                                                 @"track_type":@"1",
    }];
}

#pragma mark - Notification About

/**
 * 添加通知观察者
 * 监听地址变更、登录状态变化、首页数据刷新等通知
 */
- (void)addObserver {
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestMiniHome) name:kFNFreshAddressChangedNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestMiniHome) name:kFNFreshLoginStateDidChangeNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestMiniHome) name:kFNFreshRefreshHomeDataNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(requestMiniHome) name:kFreshLocationNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(scrollToTop) name:kFNFreshScrollToTopNotification object:nil];
}

#pragma mark - Private Method

/**
 * 滚动到顶部
 * 响应双击标签栏的回到顶部事件
 */
- (void)scrollToTop {
    if (!self.isCurrentPage) {
        return;
    }
    [self.miniTableView setContentOffset:CGPointZero animated:YES];
}

/**
 * 判断当前页面是否可见且无模态视图
 */
- (BOOL)isCurrentPage {
    
    return _currentPage && ![FNFreshTabBarController shareInstance].navigationController.hasPresentedViewController;
}

#pragma mark - Request About

/**
 * 请求迷你店首页数据
 * 获取店铺基本信息和展示内容
 */
- (void)requestMiniHome {
    WS(weakSelf)
    [self startProgress];
    FNFreshMiniStoreHomeParameterModel *parameter = [[FNFreshMiniStoreHomeParameterModel alloc] init];
    NSString *shopId =  [FNFreshUser shareInstance].shopId;
    parameter.storeId = shopId;
    self.miniShopId = shopId;
    [FNFreshMrFreshService requestMiniStoreHomeIndexWithParameter:parameter success:^(FNFreshMiniStoreHomeResponseModel *responseObject, BOOL isCache) {
        [weakSelf stopProgress];
        weakSelf.responseModel = responseObject;
        /// 如果是闭店、启动则切换到线下会员中心视图
        if (responseObject.shopInfo.storeStatus == 4 ||
            responseObject.shopInfo.storeStatus == 5) {
            [[FNFreshOfflineBaseViewController shareInstance] chooseOffLine];
            return;
        }
        /// 展示新店
        if (responseObject.shopInfo.isNewStore) {
            [[FNFreshOfflineBaseViewController shareInstance] chooseNewStore];
            return;
        }
        /// 展示迷你店
        else if (responseObject.shopInfo.storeType == 3) {
            [[FNFreshOfflineBaseViewController shareInstance] chooseMini];
            
        }
        /// 容错
        else {
            [[FNFreshOfflineBaseViewController shareInstance] chooseOffLine];
            return;
        }
        [weakSelf.miniTableView setContentOffset:CGPointZero];
        [weakSelf dealWithTableHeader];
        if (responseObject.imgList.count > 0) {
            [weakSelf dealWithImgList:responseObject.imgList];
        }
    } failure:^(id responseObject, NSError *error) {
        [weakSelf stopProgress];
    }];
}

/**
 * 请求迷你店详细信息
 * 用于会员卡绑定前的校验
 */
- (void)requestMiniStoreInfo {
    WS(weakSelf)
    self.storeInfo = nil;
    FNFreshMiniStoreHomeParameterModel *parameter = [[FNFreshMiniStoreHomeParameterModel alloc] init];
    parameter.storeId = self.miniShopId;
    [FNFreshMrFreshService requestMiniStoreInfoWithParameter:parameter success:^(FNFreshMiniStoreInfoResponseModel *responseObject, BOOL isCache) {
        
        if (responseObject.canNotBindMsg.length > 0) {
            [weakSelf startProgressText:responseObject.canNotBindMsg];
        } else {
            weakSelf.storeInfo = responseObject;
            [weakSelf gotoBindMembershipCard];
        }
        
    } failure:^(id responseObject, NSError *error) {}];
}

/**
 * 处理TableView头部视图数据
 * 设置头部视图的展示内容和交互事件
 */
- (void)dealWithTableHeader {
    WS(weakSelf)
    [self.headerView setupWithDataModel:self.responseModel handler:^(BOOL isBind, NSString *showCode, NSString *barCode) {
        if ([FNFreshUser shareInstance].isLogin) {
            if (isBind) {
                [weakSelf showMembershipCardVc];
            } else {
                // 跳转领卡页前校验
                [weakSelf requestMiniStoreInfo];
            }
        } else {
            [FNFreshTarget_LoginModule_Helper login_initWithComplete:nil];
        }
    } andChangeStoreHome:^{
        [weakSelf handleChangeStore];
//        NSDictionary *parameterDict = @{
//                                        @"parameter_FNFreshStoreMapIntroduceVCAnimation":@(2),
//                                        @"parameter_FNFreshStoreListEnterType":@(9),
//                                        };
//
//        UIViewController *viewController = [[FNMediator sharedInstance] getFreshStoreListModule_StoreListViewControllerWithParameter:parameterDict freshChoiceStoreIdBlock:nil];
//        [FNFreshTabBarController pushViewController:viewController animated:YES];
//        [FNFreshAgent eventWithTrackDataPrameters: @{@"page_col":@"141003",
//                                                     @"page_id":@"117",
//                                                     @"track_type":@"2",
//        }];
    }];
}

- (void)handleChangeStore {
    [FNFreshTarget_LoginModule_Helper loginFor215WithComplete:^{
        NSDictionary *parameterDict = @{
                                        @"parameter_FNFreshStoreMapIntroduceVCAnimation":@(2),
                                        @"parameter_FNFreshStoreListEnterType":@(9),
                                        };

        UIViewController *viewController = [[FNMediator sharedInstance] getFreshStoreListModule_StoreListViewControllerWithParameter:parameterDict freshChoiceStoreIdBlock:nil];
        [FNFreshTabBarController pushViewController:viewController animated:YES];
        [FNFreshAgent eventWithTrackDataPrameters: @{@"page_col":@"141003",
                                                     @"page_id":@"117",
                                                     @"track_type":@"2",
        }];
    }];
}

/**
 * 处理图片列表数据
 * @param imgList 图片数据模型数组
 */
- (void)dealWithImgList:(NSArray<FNFreshMiniStorePictureModel *> *)imgList {
    [self.localImgs removeAllObjects];
    [self.localImgSizeArr removeAllObjects];
    for (FNFreshMiniStorePictureModel *model in imgList) {
        [self.localImgs addObject:model.imgUrl];
        CGSize size = CGSizeMake(750, model.imgHeight.floatValue);
        [self.localImgSizeArr addObject:[NSValue valueWithCGSize:size]];
    }
    [self.miniTableView reloadData];
    [self.miniTableView setContentOffset:CGPointZero];
}

/**
 * 显示会员卡页面
 */
- (void)showMembershipCardVc {
    NSString *attStr = [NSString stringWithFormat:@"%@_%@_%@_%@_%@", @"83",@"2",@"",@"",@""];
    [FNFreshAgent eventWithPage_col:@"116008" attributeStr:attStr];
    FNFreshMemberCardContainerVC *vc = [[FNFreshMemberCardContainerVC  alloc] initWithBarCodeVcWithEnterType:0 memCardVCType:0 cardId: @""];
    [FNFreshTabBarController pushViewController:vc animated:YES];
}

/**
 * 跳转到绑定会员卡页面
 */
- (void)gotoBindMembershipCard {
    WS(weakSelf)
    [[FNMediator sharedInstance] fnFreshUserCenterService_RequestBindMemberCard:^(FNFreshNewBindCardResponseModel *responseModel, BOOL isCache) {
        
        [weakSelf showMembershipCardVc];
        
    } failure:^(FNFreshNewBindCardResponseModel *responseModel, NSError *error) {
        [FNAlertView showWithTitle:@"温馨提示" message:responseModel.errorDesc cancelButtonTitle:@"好的" otherButtonTitles:nil tapBlock:nil];
    }];
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    if (![self.miniTableView.subviews.firstObject isKindOfClass:[FNFreshMiniHomeTopView class]]) {
        [self.miniTableView sendSubviewToBack:self.headerView];
    }
}

#pragma mark - UITableViewDelegate & UITableViewDataSource

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshMiniTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:FNFreshMiniTableViewCellIdentifier];
    NSString *imagePath = [self.localImgs objectAtIndex:indexPath.row];
    if (!cell) {
        cell = [[FNFreshMiniTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:FNFreshMiniTableViewCellIdentifier];
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    if (![tableView.subviews.firstObject isKindOfClass:[FNFreshMiniHomeTopView class]]) {
        [self.miniTableView sendSubviewToBack:self.headerView];
    }
    [cell configContentWithImgStr:imagePath];
    return cell;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.localImgs.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    CGSize size = [[self.localImgSizeArr objectAtIndex:indexPath.row] CGSizeValue];
    return size.height / size.width *SCREEN_WIDTH;
}

#pragma mark - Getters

/**
 * 懒加载头部视图
 * 从xib加载头部视图并设置初始frame
 */
- (FNFreshMiniHomeTopView *)headerView {
    if (!_headerView) {
        _headerView = [[NSBundle mainBundle] loadNibNamed:@"FNFreshMiniHomeTopView" owner:nil options:nil].firstObject;
        _headerView.bounds = CGRectMake(0, 0, SCREEN_WIDTH, [UIApplication sharedApplication].statusBarFrame.size.height + 44 + 644);
    }
    return _headerView;
}

/**
 * 懒加载本地图片数组
 * 从bundle中加载默认的本地图片资源
 */
- (NSMutableArray *)localImgs {
    if (!_localImgs) {
        _localImgs = [NSMutableArray array];
        NSString *bundle = @"FNMiniHomeImages.bundle";
        for (int i = 1; i <= 6; i++) {
            NSString *imgPath = [[[NSBundle mainBundle] resourcePath] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@/up_img_0%@.png",bundle,@(i)]];
            [_localImgs addObject:imgPath];
        }
    }
    return _localImgs;
}

/**
 * 懒加载本地图片尺寸数组
 * 获取本地图片的实际尺寸信息
 */
- (NSMutableArray *)localImgSizeArr {
    if (!_localImgSizeArr) {
        _localImgSizeArr = [NSMutableArray array];
         NSString *bundle = @"FNMiniHomeImages.bundle";
        for (int i = 1; i <= 6; i++) {
            NSString *imgPath = [[[NSBundle mainBundle] resourcePath] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@/up_img_0%@.png",bundle,@(i)]];
            CGSize size = [UIImage imageWithContentsOfFile:imgPath].size;
            [_localImgSizeArr addObject:[NSValue valueWithCGSize:size]];
        }
    }
    return _localImgSizeArr;
}

@end
