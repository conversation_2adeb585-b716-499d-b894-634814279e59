<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="15705" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina5_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="15706"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FNFreshMiniStoreViewController">
            <connections>
                <outlet property="miniTableView" destination="rfU-0B-OX1" id="dUD-lb-pep"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" bounces="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="rfU-0B-OX1">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                    <color key="backgroundColor" red="0.27058823529411763" green="0.62745098039215685" blue="0.29803921568627451" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="v1Z-yN-coH"/>
                        <outlet property="delegate" destination="-1" id="F9R-PY-Cg1"/>
                    </connections>
                </tableView>
            </subviews>
            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="rfU-0B-OX1" secondAttribute="bottom" id="9jB-NF-58D"/>
                <constraint firstItem="rfU-0B-OX1" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="WQt-Ib-mPZ"/>
                <constraint firstItem="rfU-0B-OX1" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="mYk-5O-NB2"/>
                <constraint firstAttribute="trailing" secondItem="rfU-0B-OX1" secondAttribute="trailing" id="z4N-UP-KXR"/>
            </constraints>
            <point key="canvasLocation" x="131.8840579710145" y="89.673913043478265"/>
        </view>
    </objects>
</document>
