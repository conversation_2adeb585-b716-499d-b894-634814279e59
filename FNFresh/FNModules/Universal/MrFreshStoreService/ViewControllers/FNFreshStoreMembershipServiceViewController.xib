<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FNFreshStoreMembershipServiceViewController">
            <connections>
                <outlet property="bgImageView" destination="hrB-Qh-zcX" id="eUW-24-Ldi"/>
                <outlet property="bgImgViewHeightConstraint" destination="ciw-R6-0QG" id="ZdM-jE-tsl"/>
                <outlet property="bgImgViewTopConstraint" destination="0ey-hd-k1f" id="CbG-dN-upl"/>
                <outlet property="collectionView" destination="q2s-yF-Jh9" id="2cm-HU-QHx"/>
                <outlet property="collectionViewTopConstraint" destination="Wx4-Va-yIh" id="egf-de-5kA"/>
                <outlet property="emptyNavBgView" destination="lTm-iJ-IFY" id="D1g-h4-1Wv"/>
                <outlet property="emptyNavTitleLab" destination="qcy-yx-fyd" id="gDf-pg-tqA"/>
                <outlet property="navBottomLine" destination="NUc-5u-L97" id="cWz-rA-jLn"/>
                <outlet property="naviSwitchImg" destination="dtz-iK-qlP" id="u2F-sk-rWQ"/>
                <outlet property="naviTitleLab" destination="IFX-EN-m4D" id="Jpd-jO-VAQ"/>
                <outlet property="navigationBar" destination="P4C-iv-4GB" id="U4o-in-DxH"/>
                <outlet property="navigationBarHeightConstraint" destination="kwJ-bB-gmw" id="Cly-Mq-1wp"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pxF-Db-KcA">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="0.0"/>
                    <constraints>
                        <constraint firstAttribute="height" id="tVn-1l-pQ9"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg_store_service" translatesAutoresizingMaskIntoConstraints="NO" id="hrB-Qh-zcX">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="300"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="300" id="ciw-R6-0QG"/>
                    </constraints>
                </imageView>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" contentInsetAdjustmentBehavior="never" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="q2s-yF-Jh9">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="12" minimumInteritemSpacing="10" id="NC7-oD-cFH">
                        <size key="itemSize" width="50" height="50"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="yLq-FE-eKa"/>
                        <outlet property="delegate" destination="-1" id="X6c-vS-eIc"/>
                    </connections>
                </collectionView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="P4C-iv-4GB">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5ze-I7-2zX">
                            <rect key="frame" x="145" y="12" width="124" height="20"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" usesAttributedText="YES" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IFX-EN-m4D">
                                    <rect key="frame" x="0.0" y="0.0" width="108" height="20"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="20" id="ZEK-yc-S2f"/>
                                    </constraints>
                                    <attributedString key="attributedText">
                                        <fragment content="门店会员服务">
                                            <attributes>
                                                <color key="NSColor" red="1" green="1" blue="1" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                                                <font key="NSFont" size="18" name="PingFangSC-Medium"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                            </attributes>
                                        </fragment>
                                    </attributedString>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" alpha="0.0" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_unfold" translatesAutoresizingMaskIntoConstraints="NO" id="dtz-iK-qlP">
                                    <rect key="frame" x="114" y="7" width="10" height="6.5"/>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="dtz-iK-qlP" firstAttribute="centerY" secondItem="IFX-EN-m4D" secondAttribute="centerY" id="3hq-J9-X3s"/>
                                <constraint firstItem="IFX-EN-m4D" firstAttribute="centerY" secondItem="5ze-I7-2zX" secondAttribute="centerY" id="Ebl-TD-lPP"/>
                                <constraint firstItem="dtz-iK-qlP" firstAttribute="leading" secondItem="IFX-EN-m4D" secondAttribute="trailing" constant="6" id="LCW-eg-OCy"/>
                                <constraint firstAttribute="height" constant="20" id="d3K-De-Uts"/>
                                <constraint firstItem="IFX-EN-m4D" firstAttribute="leading" secondItem="5ze-I7-2zX" secondAttribute="leading" id="exA-hd-F2w"/>
                                <constraint firstAttribute="trailing" secondItem="dtz-iK-qlP" secondAttribute="trailing" id="foj-gD-Cpx"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NUc-5u-L97">
                            <rect key="frame" x="0.0" y="43" width="414" height="1"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="1" id="x0s-BS-en4"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="T8u-2F-fIM">
                            <rect key="frame" x="145" y="12" width="124" height="20"/>
                            <connections>
                                <action selector="switchToStoreList:" destination="-1" eventType="touchUpInside" id="7VY-el-0YV"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="5ze-I7-2zX" firstAttribute="centerX" secondItem="P4C-iv-4GB" secondAttribute="centerX" id="Aoy-HB-rvz"/>
                        <constraint firstItem="T8u-2F-fIM" firstAttribute="top" secondItem="5ze-I7-2zX" secondAttribute="top" id="BjB-QH-GDb"/>
                        <constraint firstItem="T8u-2F-fIM" firstAttribute="bottom" secondItem="5ze-I7-2zX" secondAttribute="bottom" id="EJN-LU-kBa"/>
                        <constraint firstItem="NUc-5u-L97" firstAttribute="leading" secondItem="P4C-iv-4GB" secondAttribute="leading" id="MKy-KF-E99"/>
                        <constraint firstAttribute="bottom" secondItem="5ze-I7-2zX" secondAttribute="bottom" constant="12" id="OcW-vg-5w9"/>
                        <constraint firstItem="T8u-2F-fIM" firstAttribute="trailing" secondItem="5ze-I7-2zX" secondAttribute="trailing" id="V7L-8L-AYD"/>
                        <constraint firstItem="NUc-5u-L97" firstAttribute="width" secondItem="P4C-iv-4GB" secondAttribute="width" id="dj8-CD-sFD"/>
                        <constraint firstAttribute="height" constant="44" id="kwJ-bB-gmw"/>
                        <constraint firstItem="T8u-2F-fIM" firstAttribute="leading" secondItem="5ze-I7-2zX" secondAttribute="leading" id="lkz-m2-kCC"/>
                        <constraint firstAttribute="bottom" secondItem="NUc-5u-L97" secondAttribute="bottom" id="zhW-Zt-iI6"/>
                    </constraints>
                </view>
                <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lTm-iJ-IFY" customClass="FNFreshMrFreshGradientView">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="大润发优鲜" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qcy-yx-fyd">
                            <rect key="frame" x="161" y="12" width="92" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="4Bn-6c-Ylj"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="18"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="qcy-yx-fyd" secondAttribute="bottom" constant="12" id="aPB-uK-zuG"/>
                        <constraint firstItem="qcy-yx-fyd" firstAttribute="centerX" secondItem="lTm-iJ-IFY" secondAttribute="centerX" id="zxZ-ZA-ml1"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.94891661410000006" green="0.9490789771" blue="0.94890636210000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="pxF-Db-KcA" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="0d9-ht-F1i"/>
                <constraint firstItem="hrB-Qh-zcX" firstAttribute="top" secondItem="pxF-Db-KcA" secondAttribute="bottom" id="0ey-hd-k1f"/>
                <constraint firstItem="lTm-iJ-IFY" firstAttribute="height" secondItem="P4C-iv-4GB" secondAttribute="height" id="3Xv-Fy-6mg"/>
                <constraint firstAttribute="trailing" secondItem="hrB-Qh-zcX" secondAttribute="trailing" id="7k4-hh-g89"/>
                <constraint firstAttribute="trailing" secondItem="q2s-yF-Jh9" secondAttribute="trailing" id="9WK-l1-QqU"/>
                <constraint firstItem="lTm-iJ-IFY" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="CAH-dM-FPI"/>
                <constraint firstItem="pxF-Db-KcA" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="G6E-dC-DoS"/>
                <constraint firstItem="P4C-iv-4GB" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="Nq7-ah-h5a"/>
                <constraint firstItem="q2s-yF-Jh9" firstAttribute="top" secondItem="pxF-Db-KcA" secondAttribute="bottom" id="Wx4-Va-yIh"/>
                <constraint firstItem="hrB-Qh-zcX" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="XJE-5w-0g1"/>
                <constraint firstAttribute="trailing" secondItem="lTm-iJ-IFY" secondAttribute="trailing" id="bN1-ZZ-eIg"/>
                <constraint firstAttribute="trailing" secondItem="pxF-Db-KcA" secondAttribute="trailing" id="dGN-UZ-QcM"/>
                <constraint firstAttribute="bottom" secondItem="q2s-yF-Jh9" secondAttribute="bottom" id="idw-L8-iqJ"/>
                <constraint firstItem="q2s-yF-Jh9" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="n1B-X3-qrn"/>
                <constraint firstItem="lTm-iJ-IFY" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="qCx-ye-CA8"/>
                <constraint firstAttribute="trailing" secondItem="P4C-iv-4GB" secondAttribute="trailing" id="rrX-63-Kwh"/>
                <constraint firstItem="P4C-iv-4GB" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="tSw-p3-O9T"/>
            </constraints>
            <point key="canvasLocation" x="137.68115942028987" y="109.15178571428571"/>
        </view>
    </objects>
    <resources>
        <image name="bg_store_service" width="375" height="300"/>
        <image name="icon_unfold" width="10" height="6.5"/>
    </resources>
</document>
