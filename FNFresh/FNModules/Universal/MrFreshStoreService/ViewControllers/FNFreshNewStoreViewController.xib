<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina5_5" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="FNFreshNewStoreViewController">
            <connections>
                <outlet property="collectionView" destination="jX6-ij-Ie1" id="sp2-1t-OM2"/>
                <outlet property="collectionViewTopConstraint" destination="Omo-1o-2zI" id="aWx-fB-GX9"/>
                <outlet property="gradientView" destination="V9P-tH-PBc" id="JgM-zz-YGx"/>
                <outlet property="gradientViewTopConstraint" destination="fcY-TD-aZz" id="VRN-fZ-S4k"/>
                <outlet property="topBgView" destination="Cxf-EN-rb7" id="ZYT-T0-tGd"/>
                <outlet property="topBgViewTopConstraint" destination="20L-fM-NZj" id="5um-MY-hng"/>
                <outlet property="topNaviHeight" destination="1hu-jx-9CO" id="JLU-ze-bBm"/>
                <outlet property="topNavigationView" destination="EJ4-Fj-ze3" id="mOs-aV-6Ud"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="299-u9-1Gg">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="0.0"/>
                    <constraints>
                        <constraint firstAttribute="height" id="Wto-yU-Gzc"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Cxf-EN-rb7" userLabel="top bg view">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="300"/>
                    <color key="backgroundColor" red="0.92549026010000002" green="0.38431376220000002" blue="0.30980393290000002" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="300" id="iWY-Fc-Max"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="V9P-tH-PBc">
                    <rect key="frame" x="0.0" y="400" width="414" height="180"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="180" id="fJ9-b8-F4X"/>
                    </constraints>
                </view>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="jX6-ij-Ie1">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="736"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="12" minimumInteritemSpacing="10" id="HU7-UK-vYR">
                        <size key="itemSize" width="128" height="128"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="Pk3-6X-lsn"/>
                        <outlet property="delegate" destination="-1" id="FGA-dq-tDz"/>
                    </connections>
                </collectionView>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EJ4-Fj-ze3">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="64"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PBG-3c-W0N">
                            <rect key="frame" x="207" y="36" width="0.0" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="YTm-Nj-I8m"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon_choose" translatesAutoresizingMaskIntoConstraints="NO" id="2Tb-z6-f7w">
                            <rect key="frame" x="318.66666666666669" y="33" width="20" height="20"/>
                            <constraints>
                                <constraint firstAttribute="width" secondItem="2Tb-z6-f7w" secondAttribute="height" id="SyO-5W-8sL"/>
                                <constraint firstAttribute="width" constant="20" id="vwK-am-9bz"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="门店切换" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Z4J-Bo-uaE">
                            <rect key="frame" x="341.66666666666669" y="34.666666666666664" width="57.333333333333314" height="17"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0OM-Py-0Kl">
                            <rect key="frame" x="318.66666666666669" y="34" width="80.333333333333314" height="30"/>
                            <connections>
                                <action selector="changeStoreClick:" destination="-1" eventType="touchUpInside" id="jXf-pF-sIm"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" red="1" green="0.33333333329999998" blue="0.27058823529999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="64" id="1hu-jx-9CO"/>
                        <constraint firstItem="Z4J-Bo-uaE" firstAttribute="leading" secondItem="2Tb-z6-f7w" secondAttribute="trailing" constant="3" id="268-XW-Gd9"/>
                        <constraint firstItem="2Tb-z6-f7w" firstAttribute="centerY" secondItem="PBG-3c-W0N" secondAttribute="centerY" id="DdR-25-YH3"/>
                        <constraint firstAttribute="trailing" secondItem="Z4J-Bo-uaE" secondAttribute="trailing" constant="15" id="YJ2-5s-bFM"/>
                        <constraint firstItem="0OM-Py-0Kl" firstAttribute="leading" secondItem="2Tb-z6-f7w" secondAttribute="leading" id="dN1-Nf-rDd"/>
                        <constraint firstItem="Z4J-Bo-uaE" firstAttribute="centerY" secondItem="PBG-3c-W0N" secondAttribute="centerY" id="gYx-hr-Mh8"/>
                        <constraint firstAttribute="bottom" secondItem="PBG-3c-W0N" secondAttribute="bottom" constant="14" id="gu4-7h-90r"/>
                        <constraint firstItem="PBG-3c-W0N" firstAttribute="centerX" secondItem="EJ4-Fj-ze3" secondAttribute="centerX" id="kZL-xj-Mi6"/>
                        <constraint firstAttribute="bottom" secondItem="0OM-Py-0Kl" secondAttribute="bottom" id="rzr-Vm-XLS"/>
                        <constraint firstItem="0OM-Py-0Kl" firstAttribute="trailing" secondItem="Z4J-Bo-uaE" secondAttribute="trailing" id="tlH-TT-pXs"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.94901960784313721" green="0.94901960784313721" blue="0.94901960784313721" alpha="1" colorSpace="calibratedRGB"/>
            <constraints>
                <constraint firstItem="Cxf-EN-rb7" firstAttribute="top" secondItem="299-u9-1Gg" secondAttribute="bottom" id="20L-fM-NZj"/>
                <constraint firstItem="EJ4-Fj-ze3" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="Cg2-LF-asl"/>
                <constraint firstAttribute="trailing" secondItem="V9P-tH-PBc" secondAttribute="trailing" id="LsS-03-lII"/>
                <constraint firstItem="jX6-ij-Ie1" firstAttribute="top" secondItem="299-u9-1Gg" secondAttribute="bottom" id="Omo-1o-2zI"/>
                <constraint firstAttribute="trailing" secondItem="EJ4-Fj-ze3" secondAttribute="trailing" id="SMm-HA-YtM"/>
                <constraint firstAttribute="trailing" secondItem="299-u9-1Gg" secondAttribute="trailing" id="Te6-aN-kau"/>
                <constraint firstItem="299-u9-1Gg" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="VkF-ja-xXA"/>
                <constraint firstItem="V9P-tH-PBc" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="WaM-pK-EsN"/>
                <constraint firstAttribute="trailing" secondItem="Cxf-EN-rb7" secondAttribute="trailing" id="ZdL-YJ-svi"/>
                <constraint firstItem="299-u9-1Gg" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="fZd-4g-Qj2"/>
                <constraint firstItem="V9P-tH-PBc" firstAttribute="top" secondItem="jX6-ij-Ie1" secondAttribute="top" constant="400" id="fcY-TD-aZz"/>
                <constraint firstItem="jX6-ij-Ie1" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="feO-xP-Sc2"/>
                <constraint firstAttribute="bottom" secondItem="jX6-ij-Ie1" secondAttribute="bottom" id="hvV-Ii-3QX"/>
                <constraint firstItem="EJ4-Fj-ze3" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="jM3-aw-7z0"/>
                <constraint firstItem="Cxf-EN-rb7" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="jb8-0j-ere"/>
                <constraint firstAttribute="trailing" secondItem="jX6-ij-Ie1" secondAttribute="trailing" id="mDA-Q3-0Xs"/>
            </constraints>
            <point key="canvasLocation" x="137.68115942028987" y="63.586956521739133"/>
        </view>
    </objects>
    <resources>
        <image name="icon_choose" width="20" height="20"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
